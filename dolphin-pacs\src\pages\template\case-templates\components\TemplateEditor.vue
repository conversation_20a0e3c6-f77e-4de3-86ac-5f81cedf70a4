<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import Image from '@tiptap/extension-image'
import TextAlign from '@tiptap/extension-text-align'
import { TextStyle, FontFamily } from '@tiptap/extension-text-style'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'TemplateEditor'
})

interface Props {
  modelValue?: string
  placeholder?: string
  editable?: boolean
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  editable: true,
  height: '500px'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'save': [content: string]
  'change': [content: string]
}>()

// 编辑器实例
const editor = useEditor({
  content: props.modelValue,
  editable: props.editable,
  extensions: [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3, 4, 5, 6]
      }
    }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 50
    }),
    TableRow,
    TableHeader,
    TableCell,
    Image.configure({
      inline: true,
      allowBase64: true,
      HTMLAttributes: {
        class: 'editor-image',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
      alignments: ['left', 'center', 'right', 'justify'],
      defaultAlignment: 'left',
    }),
    TextStyle,
    FontFamily.configure({
      types: ['textStyle'],
    })
  ],
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
    emit('change', html)
  },
  editorProps: {
    attributes: {
      class: 'tiptap-editor-content'
    }
  }
})

// 工具栏状态
const toolbarState = reactive({
  isBold: false,
  isItalic: false,
  isUnderline: false,
  isStrike: false,
  isCode: false,
  isBulletList: false,
  isOrderedList: false,
  isBlockquote: false,
  currentHeading: 0,
  textAlign: 'left',
  currentFontFamily: 'SimSun, serif'
})

// 常用字体列表
const fontFamilies = [
  { label: '宋体', value: 'SimSun, serif' },
  { label: '微软雅黑', value: 'Microsoft YaHei, sans-serif' },
  { label: '黑体', value: 'SimHei, sans-serif' },
  { label: '楷体', value: 'KaiTi, serif' },
  { label: '仿宋', value: 'FangSong, serif' },
  { label: 'Arial', value: 'Arial, sans-serif' },
  { label: 'Times New Roman', value: 'Times New Roman, serif' },
  { label: 'Helvetica', value: 'Helvetica, sans-serif' },
  { label: 'Georgia', value: 'Georgia, serif' },
  { label: 'Verdana', value: 'Verdana, sans-serif' },
  { label: 'Courier New', value: 'Courier New, monospace' }
]

// 更新工具栏状态
const updateToolbarState = () => {
  if (!editor.value) return

  toolbarState.isBold = editor.value.isActive('bold')
  toolbarState.isItalic = editor.value.isActive('italic')
  toolbarState.isStrike = editor.value.isActive('strike')
  toolbarState.isCode = editor.value.isActive('code')
  toolbarState.isBulletList = editor.value.isActive('bulletList')
  toolbarState.isOrderedList = editor.value.isActive('orderedList')
  toolbarState.isBlockquote = editor.value.isActive('blockquote')

  // 检查当前标题级别
  for (let level = 1; level <= 6; level++) {
    if (editor.value.isActive('heading', { level })) {
      toolbarState.currentHeading = level
      break
    } else {
      toolbarState.currentHeading = 0
    }
  }

  // 检查文本对齐状态
  if (editor.value.isActive({ textAlign: 'left' })) {
    toolbarState.textAlign = 'left'
  } else if (editor.value.isActive({ textAlign: 'center' })) {
    toolbarState.textAlign = 'center'
  } else if (editor.value.isActive({ textAlign: 'right' })) {
    toolbarState.textAlign = 'right'
  } else if (editor.value.isActive({ textAlign: 'justify' })) {
    toolbarState.textAlign = 'justify'
  } else {
    toolbarState.textAlign = 'left'
  }

  // 检查当前字体
  const currentFontFamily = editor.value.getAttributes('textStyle').fontFamily || ''
  toolbarState.currentFontFamily = currentFontFamily
}

// 监听编辑器选择变化
watch(() => editor.value?.state.selection, () => {
  updateToolbarState()
}, { deep: true })

// 工具栏操作方法
const toggleBold = () => editor.value?.chain().focus().toggleBold().run()
const toggleItalic = () => editor.value?.chain().focus().toggleItalic().run()
const toggleStrike = () => editor.value?.chain().focus().toggleStrike().run()
const toggleCode = () => editor.value?.chain().focus().toggleCode().run()
const toggleBulletList = () => editor.value?.chain().focus().toggleBulletList().run()
const toggleOrderedList = () => editor.value?.chain().focus().toggleOrderedList().run()
const toggleBlockquote = () => editor.value?.chain().focus().toggleBlockquote().run()

const setHeading = (level: number) => {
  if (level === 0) {
    editor.value?.chain().focus().setParagraph().run()
  } else {
    // 确保level是有效的标题级别 (1-6)
    const validLevel = Math.max(1, Math.min(6, level)) as 1 | 2 | 3 | 4 | 5 | 6
    editor.value?.chain().focus().toggleHeading({ level: validLevel }).run()
  }
}

const setTextAlign = (alignment: string) => {
  toolbarState.textAlign = alignment
  editor.value?.chain().focus().setTextAlign(alignment).run()
}

// 字体设置方法
const setFontFamily = (fontFamily: string) => {
  if (fontFamily === '') {
    editor.value?.chain().focus().unsetFontFamily().run()
  } else {
    editor.value?.chain().focus().setFontFamily(fontFamily).run()
  }
  toolbarState.currentFontFamily = fontFamily
}

const undo = () => {
  editor.value?.chain().focus().undo().run()
}
const redo = () => {
  editor.value?.chain().focus().redo().run()
}

const addHorizontalRule = () => editor.value?.chain().focus().setHorizontalRule().run()

// 表格操作
const insertTable = () => {
  editor.value?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

const addColumnBefore = () => editor.value?.chain().focus().addColumnBefore().run()
const addColumnAfter = () => editor.value?.chain().focus().addColumnAfter().run()
const deleteColumn = () => editor.value?.chain().focus().deleteColumn().run()
const addRowBefore = () => editor.value?.chain().focus().addRowBefore().run()
const addRowAfter = () => editor.value?.chain().focus().addRowAfter().run()
const deleteRow = () => editor.value?.chain().focus().deleteRow().run()
const deleteTable = () => editor.value?.chain().focus().deleteTable().run()
const mergeCells = () => editor.value?.chain().focus().mergeCells().run()
const splitCell = () => editor.value?.chain().focus().splitCell().run()

// 表格命令处理
const handleTableCommand = (command: string) => {
  switch (command) {
    case 'insert':
      insertTable()
      break
    case 'addColumnBefore':
      addColumnBefore()
      break
    case 'addColumnAfter':
      addColumnAfter()
      break
    case 'deleteColumn':
      deleteColumn()
      break
    case 'addRowBefore':
      addRowBefore()
      break
    case 'addRowAfter':
      addRowAfter()
      break
    case 'deleteRow':
      deleteRow()
      break
    case 'mergeCells':
      mergeCells()
      break
    case 'splitCell':
      splitCell()
      break
    case 'deleteTable':
      deleteTable()
      break
  }
}

// 链接操作
const addLink = () => {
  const url = window.prompt('请输入链接地址:')
  if (url) {
    editor.value?.chain().focus().setLink({ href: url }).run()
  }
}

const removeLink = () => editor.value?.chain().focus().unsetLink().run()

// 图片操作
const addImage = () => {
  const url = window.prompt('请输入图片地址:')
  if (url) {
    // 使用TipTap Image扩展的正确命令
    editor.value?.chain().focus().setImage({ src: url, alt: '插入的图片' }).run()
  }
}

// 保存操作
const handleSave = () => {
  const content = editor.value?.getHTML() || ''
  emit('save', content)
}

// 导出操作
const handleExport = () => {
  const content = editor.value?.getHTML() || ''
  return content
}

// 导入操作
const handleImport = (content: string) => {
  editor.value?.commands.setContent(content)
}

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue)
  }
})

watch(() => props.editable, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(newValue)
  }
})

onMounted(() => {
  updateToolbarState()
  // 设置默认字体为宋体
  setTimeout(() => {
    if (editor.value && !props.modelValue) {
      editor.value.chain().focus().selectAll().setFontFamily('SimSun, serif').run()
    }
  }, 100)
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})

// 暴露方法给父组件
defineExpose({
  // 历史操作
  undo,
  redo,
  canUndo: () => editor.value?.can().undo() || false,
  canRedo: () => editor.value?.can().redo() || false,

  // 文件操作
  handleSave,
  handleExport,
  handleImport,

  // 文本格式
  toggleBold,
  toggleItalic,
  toggleStrike,
  toggleCode,

  // 文本对齐
  setTextAlign,

  // 字体设置
  setFontFamily,
  getFontFamilies: () => fontFamilies,

  // 标题
  setHeading,

  // 列表和引用
  toggleBulletList,
  toggleOrderedList,
  toggleBlockquote,

  // 表格操作
  insertTable,
  addColumnBefore,
  addColumnAfter,
  deleteColumn,
  addRowBefore,
  addRowAfter,
  deleteRow,
  deleteTable,
  mergeCells,
  splitCell,

  // 插入操作
  addLink,
  removeLink,
  addImage,
  addHorizontalRule,

  // 工具栏状态
  getToolbarState: () => toolbarState,

  // 编辑器实例
  getEditor: () => editor.value
})
</script>

<template>
  <div class="template-editor">

    <!-- 编辑器内容区域 -->
    <div class="editor-content-wrapper" :style="{ height: typeof props.height === 'number' ? `${props.height}px` : props.height }">
      <EditorContent
        :editor="editor"
        class="editor-content"
        :placeholder="props.placeholder"
      />
    </div>

    <!-- 状态栏 -->
    <div class="editor-status-bar">
      <div class="status-left">
        <span class="word-count">
          字符数: {{ editor?.storage.characterCount?.characters() || 0 }}
        </span>
        <span class="word-count">
          单词数: {{ editor?.storage.characterCount?.words() || 0 }}
        </span>
      </div>
      <div class="status-right">
        <span class="editor-mode">
          {{ props.editable ? '📝 编辑模式' : '👁️ 只读模式' }}
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.template-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: white;

  .editor-toolbar {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color);
    flex-wrap: wrap;
    gap: 8px;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .el-divider--vertical {
      height: 20px;
      margin: 0 8px;
    }

    // 图标按钮样式
    .icon-button {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      height: 32px;
      padding: 6px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--el-text-color-regular);
      background: transparent;
      border: 1px solid transparent;

      &:hover {
        background: var(--el-fill-color-light);
        color: var(--el-color-primary);
      }

      &.active {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        border-color: var(--el-color-primary-light-7);
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background: transparent;
          color: var(--el-text-color-regular);
        }
      }

      &.dropdown-trigger {
        gap: 4px;
        min-width: auto;
        padding: 6px 8px;

        .dropdown-text {
          font-size: 12px;
        }

        .dropdown-arrow {
          font-size: 10px;
          opacity: 0.7;
        }
      }

      .button-text {
        margin-left: 4px;
        font-size: 12px;
      }
    }

    // 保留原有按钮样式用于兼容
    .el-button {
      min-width: auto;

      strong, em, s {
        font-style: normal;
        font-weight: bold;
      }

      em {
        font-style: italic;
        font-weight: normal;
      }
    }
  }

  .editor-content-wrapper {
    position: relative;
    overflow-y: auto;

    .editor-content {
      height: 100%;

      :deep(.tiptap-editor-content) {
        padding: 20px;
        outline: none;
        min-height: 100%;
        font-family: 'SimSun', serif; // 设置默认字体为宋体

        // 基础样式
        p {
          margin: 0 0 16px 0;
          line-height: 1.6;
          font-family: inherit; // 继承父元素字体

          &:last-child {
            margin-bottom: 0;
          }
        }

        // 标题样式
        h1, h2, h3, h4, h5, h6 {
          margin: 24px 0 16px 0;
          font-weight: 600;
          line-height: 1.4;

          &:first-child {
            margin-top: 0;
          }
        }

        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1.1em; }
        h5 { font-size: 1em; }
        h6 { font-size: 0.9em; }

        // 列表样式
        ul, ol {
          margin: 16px 0;
          padding-left: 24px;

          li {
            margin: 4px 0;
            line-height: 1.6;
          }
        }

        // 引用样式
        blockquote {
          margin: 16px 0;
          padding: 12px 16px;
          border-left: 4px solid var(--el-color-primary);
          background: var(--el-bg-color-page);
          font-style: italic;

          p {
            margin: 0;
          }
        }

        // 代码样式
        code {
          background: var(--el-bg-color-page);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }

        pre {
          background: var(--el-bg-color-page);
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 16px 0;

          code {
            background: none;
            padding: 0;
          }
        }

        // 分割线样式
        hr {
          margin: 24px 0;
          border: none;
          border-top: 2px solid var(--el-border-color);
        }

        // 链接样式
        a {
          color: var(--el-color-primary);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        // 图片样式
        img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 16px 0;
        }

        // 表格样式
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 16px 0;

          th, td {
            border: 1px solid var(--el-border-color);
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
          }

          th {
            background: var(--el-bg-color-page);
            font-weight: 600;
          }

          tr:nth-child(even) {
            background: var(--el-bg-color-page);
          }
        }

        // 占位符样式
        .is-empty::before {
          content: attr(data-placeholder);
          float: left;
          color: var(--el-text-color-placeholder);
          pointer-events: none;
          height: 0;
        }
      }
    }
  }

  .editor-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color);
    font-size: 12px;
    color: var(--el-text-color-secondary);

    .status-left {
      display: flex;
      gap: 16px;
    }

    .word-count {
      font-family: monospace;
    }

    .editor-mode {
      font-weight: 500;
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .template-editor {
    .editor-toolbar {
      padding: 8px 12px;

      .toolbar-group {
        gap: 2px;
      }

      .icon-button {
        min-width: 28px;
        height: 28px;
        padding: 4px;

        .button-text {
          display: none; // 在移动端隐藏文字
        }

        &.dropdown-trigger {
          padding: 4px 6px;

          .dropdown-text {
            font-size: 11px;
          }
        }
      }

      .el-button {
        padding: 6px 8px;
        font-size: 12px;
      }
    }

    .editor-content-wrapper {
      .editor-content {
        :deep(.tiptap-editor-content) {
          padding: 12px;
        }
      }
    }

    .editor-status-bar {
      padding: 6px 12px;
      font-size: 11px;

      .status-left {
        gap: 8px;
      }
    }
  }
}
</style>