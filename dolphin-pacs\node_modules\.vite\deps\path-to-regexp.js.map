{"version": 3, "sources": ["../../path-to-regexp/src/index.ts"], "sourcesContent": ["const DEFAULT_DELIMITER = \"/\";\nconst NOOP_VALUE = (value: string) => value;\nconst ID_START = /^[$_\\p{ID_Start}]$/u;\nconst ID_CONTINUE = /^[$\\u200c\\u200d\\p{ID_Continue}]$/u;\nconst DEBUG_URL = \"https://git.new/pathToRegexpError\";\n\n/**\n * Encode a string into another string.\n */\nexport type Encode = (value: string) => string;\n\n/**\n * Decode a string into another string.\n */\nexport type Decode = (value: string) => string;\n\nexport interface ParseOptions {\n  /**\n   * A function for encoding input strings.\n   */\n  encodePath?: Encode;\n}\n\nexport interface PathToRegexpOptions {\n  /**\n   * Matches the path completely without trailing characters. (default: `true`)\n   */\n  end?: boolean;\n  /**\n   * Allows optional trailing delimiter to match. (default: `true`)\n   */\n  trailing?: boolean;\n  /**\n   * Match will be case sensitive. (default: `false`)\n   */\n  sensitive?: boolean;\n  /**\n   * The default delimiter for segments. (default: `'/'`)\n   */\n  delimiter?: string;\n}\n\nexport interface MatchOptions extends PathToRegexpOptions {\n  /**\n   * Function for decoding strings for params, or `false` to disable entirely. (default: `decodeURIComponent`)\n   */\n  decode?: Decode | false;\n}\n\nexport interface CompileOptions {\n  /**\n   * Function for encoding input strings for output into the path, or `false` to disable entirely. (default: `encodeURIComponent`)\n   */\n  encode?: Encode | false;\n  /**\n   * The default delimiter for segments. (default: `'/'`)\n   */\n  delimiter?: string;\n}\n\ntype TokenType =\n  | \"{\"\n  | \"}\"\n  | \"WILDCARD\"\n  | \"PARAM\"\n  | \"CHAR\"\n  | \"ESCAPED\"\n  | \"END\"\n  // Reserved for use or ambiguous due to past use.\n  | \"(\"\n  | \")\"\n  | \"[\"\n  | \"]\"\n  | \"+\"\n  | \"?\"\n  | \"!\";\n\n/**\n * Tokenizer results.\n */\ninterface LexToken {\n  type: TokenType;\n  index: number;\n  value: string;\n}\n\nconst SIMPLE_TOKENS: Record<string, TokenType> = {\n  // Groups.\n  \"{\": \"{\",\n  \"}\": \"}\",\n  // Reserved.\n  \"(\": \"(\",\n  \")\": \")\",\n  \"[\": \"[\",\n  \"]\": \"]\",\n  \"+\": \"+\",\n  \"?\": \"?\",\n  \"!\": \"!\",\n};\n\n/**\n * Escape text for stringify to path.\n */\nfunction escapeText(str: string) {\n  return str.replace(/[{}()\\[\\]+?!:*]/g, \"\\\\$&\");\n}\n\n/**\n * Escape a regular expression string.\n */\nfunction escape(str: string) {\n  return str.replace(/[.+*?^${}()[\\]|/\\\\]/g, \"\\\\$&\");\n}\n\n/**\n * Tokenize input string.\n */\nfunction* lexer(str: string): Generator<LexToken, LexToken> {\n  const chars = [...str];\n  let i = 0;\n\n  function name() {\n    let value = \"\";\n\n    if (ID_START.test(chars[++i])) {\n      value += chars[i];\n      while (ID_CONTINUE.test(chars[++i])) {\n        value += chars[i];\n      }\n    } else if (chars[i] === '\"') {\n      let pos = i;\n\n      while (i < chars.length) {\n        if (chars[++i] === '\"') {\n          i++;\n          pos = 0;\n          break;\n        }\n\n        if (chars[i] === \"\\\\\") {\n          value += chars[++i];\n        } else {\n          value += chars[i];\n        }\n      }\n\n      if (pos) {\n        throw new TypeError(`Unterminated quote at ${pos}: ${DEBUG_URL}`);\n      }\n    }\n\n    if (!value) {\n      throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);\n    }\n\n    return value;\n  }\n\n  while (i < chars.length) {\n    const value = chars[i];\n    const type = SIMPLE_TOKENS[value];\n\n    if (type) {\n      yield { type, index: i++, value };\n    } else if (value === \"\\\\\") {\n      yield { type: \"ESCAPED\", index: i++, value: chars[i++] };\n    } else if (value === \":\") {\n      const value = name();\n      yield { type: \"PARAM\", index: i, value };\n    } else if (value === \"*\") {\n      const value = name();\n      yield { type: \"WILDCARD\", index: i, value };\n    } else {\n      yield { type: \"CHAR\", index: i, value: chars[i++] };\n    }\n  }\n\n  return { type: \"END\", index: i, value: \"\" };\n}\n\nclass Iter {\n  private _peek?: LexToken;\n\n  constructor(private tokens: Generator<LexToken, LexToken>) {}\n\n  peek(): LexToken {\n    if (!this._peek) {\n      const next = this.tokens.next();\n      this._peek = next.value;\n    }\n    return this._peek;\n  }\n\n  tryConsume(type: TokenType): string | undefined {\n    const token = this.peek();\n    if (token.type !== type) return;\n    this._peek = undefined; // Reset after consumed.\n    return token.value;\n  }\n\n  consume(type: TokenType): string {\n    const value = this.tryConsume(type);\n    if (value !== undefined) return value;\n    const { type: nextType, index } = this.peek();\n    throw new TypeError(\n      `Unexpected ${nextType} at ${index}, expected ${type}: ${DEBUG_URL}`,\n    );\n  }\n\n  text(): string {\n    let result = \"\";\n    let value: string | undefined;\n    while ((value = this.tryConsume(\"CHAR\") || this.tryConsume(\"ESCAPED\"))) {\n      result += value;\n    }\n    return result;\n  }\n}\n\n/**\n * Plain text.\n */\nexport interface Text {\n  type: \"text\";\n  value: string;\n}\n\n/**\n * A parameter designed to match arbitrary text within a segment.\n */\nexport interface Parameter {\n  type: \"param\";\n  name: string;\n}\n\n/**\n * A wildcard parameter designed to match multiple segments.\n */\nexport interface Wildcard {\n  type: \"wildcard\";\n  name: string;\n}\n\n/**\n * A set of possible tokens to expand when matching.\n */\nexport interface Group {\n  type: \"group\";\n  tokens: Token[];\n}\n\n/**\n * A token that corresponds with a regexp capture.\n */\nexport type Key = Parameter | Wildcard;\n\n/**\n * A sequence of `path-to-regexp` keys that match capturing groups.\n */\nexport type Keys = Array<Key>;\n\n/**\n * A sequence of path match characters.\n */\nexport type Token = Text | Parameter | Wildcard | Group;\n\n/**\n * Tokenized path instance.\n */\nexport class TokenData {\n  constructor(public readonly tokens: Token[]) {}\n}\n\n/**\n * Parse a string for the raw tokens.\n */\nexport function parse(str: string, options: ParseOptions = {}): TokenData {\n  const { encodePath = NOOP_VALUE } = options;\n  const it = new Iter(lexer(str));\n\n  function consume(endType: TokenType): Token[] {\n    const tokens: Token[] = [];\n\n    while (true) {\n      const path = it.text();\n      if (path) tokens.push({ type: \"text\", value: encodePath(path) });\n\n      const param = it.tryConsume(\"PARAM\");\n      if (param) {\n        tokens.push({\n          type: \"param\",\n          name: param,\n        });\n        continue;\n      }\n\n      const wildcard = it.tryConsume(\"WILDCARD\");\n      if (wildcard) {\n        tokens.push({\n          type: \"wildcard\",\n          name: wildcard,\n        });\n        continue;\n      }\n\n      const open = it.tryConsume(\"{\");\n      if (open) {\n        tokens.push({\n          type: \"group\",\n          tokens: consume(\"}\"),\n        });\n        continue;\n      }\n\n      it.consume(endType);\n      return tokens;\n    }\n  }\n\n  const tokens = consume(\"END\");\n  return new TokenData(tokens);\n}\n\n/**\n * Compile a string to a template function for the path.\n */\nexport function compile<P extends ParamData = ParamData>(\n  path: Path,\n  options: CompileOptions & ParseOptions = {},\n) {\n  const { encode = encodeURIComponent, delimiter = DEFAULT_DELIMITER } =\n    options;\n  const data = path instanceof TokenData ? path : parse(path, options);\n  const fn = tokensToFunction(data.tokens, delimiter, encode);\n\n  return function path(data: P = {} as P) {\n    const [path, ...missing] = fn(data);\n    if (missing.length) {\n      throw new TypeError(`Missing parameters: ${missing.join(\", \")}`);\n    }\n    return path;\n  };\n}\n\nexport type ParamData = Partial<Record<string, string | string[]>>;\nexport type PathFunction<P extends ParamData> = (data?: P) => string;\n\nfunction tokensToFunction(\n  tokens: Token[],\n  delimiter: string,\n  encode: Encode | false,\n) {\n  const encoders = tokens.map((token) =>\n    tokenToFunction(token, delimiter, encode),\n  );\n\n  return (data: ParamData) => {\n    const result: string[] = [\"\"];\n\n    for (const encoder of encoders) {\n      const [value, ...extras] = encoder(data);\n      result[0] += value;\n      result.push(...extras);\n    }\n\n    return result;\n  };\n}\n\n/**\n * Convert a single token into a path building function.\n */\nfunction tokenToFunction(\n  token: Token,\n  delimiter: string,\n  encode: Encode | false,\n): (data: ParamData) => string[] {\n  if (token.type === \"text\") return () => [token.value];\n\n  if (token.type === \"group\") {\n    const fn = tokensToFunction(token.tokens, delimiter, encode);\n\n    return (data) => {\n      const [value, ...missing] = fn(data);\n      if (!missing.length) return [value];\n      return [\"\"];\n    };\n  }\n\n  const encodeValue = encode || NOOP_VALUE;\n\n  if (token.type === \"wildcard\" && encode !== false) {\n    return (data) => {\n      const value = data[token.name];\n      if (value == null) return [\"\", token.name];\n\n      if (!Array.isArray(value) || value.length === 0) {\n        throw new TypeError(`Expected \"${token.name}\" to be a non-empty array`);\n      }\n\n      return [\n        value\n          .map((value, index) => {\n            if (typeof value !== \"string\") {\n              throw new TypeError(\n                `Expected \"${token.name}/${index}\" to be a string`,\n              );\n            }\n\n            return encodeValue(value);\n          })\n          .join(delimiter),\n      ];\n    };\n  }\n\n  return (data) => {\n    const value = data[token.name];\n    if (value == null) return [\"\", token.name];\n\n    if (typeof value !== \"string\") {\n      throw new TypeError(`Expected \"${token.name}\" to be a string`);\n    }\n\n    return [encodeValue(value)];\n  };\n}\n\n/**\n * A match result contains data about the path match.\n */\nexport interface MatchResult<P extends ParamData> {\n  path: string;\n  params: P;\n}\n\n/**\n * A match is either `false` (no match) or a match result.\n */\nexport type Match<P extends ParamData> = false | MatchResult<P>;\n\n/**\n * The match function takes a string and returns whether it matched the path.\n */\nexport type MatchFunction<P extends ParamData> = (path: string) => Match<P>;\n\n/**\n * Supported path types.\n */\nexport type Path = string | TokenData;\n\n/**\n * Transform a path into a match function.\n */\nexport function match<P extends ParamData>(\n  path: Path | Path[],\n  options: MatchOptions & ParseOptions = {},\n): MatchFunction<P> {\n  const { decode = decodeURIComponent, delimiter = DEFAULT_DELIMITER } =\n    options;\n  const { regexp, keys } = pathToRegexp(path, options);\n\n  const decoders = keys.map((key) => {\n    if (decode === false) return NOOP_VALUE;\n    if (key.type === \"param\") return decode;\n    return (value: string) => value.split(delimiter).map(decode);\n  });\n\n  return function match(input: string) {\n    const m = regexp.exec(input);\n    if (!m) return false;\n\n    const path = m[0];\n    const params = Object.create(null);\n\n    for (let i = 1; i < m.length; i++) {\n      if (m[i] === undefined) continue;\n\n      const key = keys[i - 1];\n      const decoder = decoders[i - 1];\n      params[key.name] = decoder(m[i]);\n    }\n\n    return { path, params };\n  };\n}\n\nexport function pathToRegexp(\n  path: Path | Path[],\n  options: PathToRegexpOptions & ParseOptions = {},\n) {\n  const {\n    delimiter = DEFAULT_DELIMITER,\n    end = true,\n    sensitive = false,\n    trailing = true,\n  } = options;\n  const keys: Keys = [];\n  const sources: string[] = [];\n  const flags = sensitive ? \"\" : \"i\";\n  const paths = Array.isArray(path) ? path : [path];\n  const items = paths.map((path) =>\n    path instanceof TokenData ? path : parse(path, options),\n  );\n\n  for (const { tokens } of items) {\n    for (const seq of flatten(tokens, 0, [])) {\n      const regexp = sequenceToRegExp(seq, delimiter, keys);\n      sources.push(regexp);\n    }\n  }\n\n  let pattern = `^(?:${sources.join(\"|\")})`;\n  if (trailing) pattern += `(?:${escape(delimiter)}$)?`;\n  pattern += end ? \"$\" : `(?=${escape(delimiter)}|$)`;\n\n  const regexp = new RegExp(pattern, flags);\n  return { regexp, keys };\n}\n\n/**\n * Flattened token set.\n */\ntype Flattened = Text | Parameter | Wildcard;\n\n/**\n * Generate a flat list of sequence tokens from the given tokens.\n */\nfunction* flatten(\n  tokens: Token[],\n  index: number,\n  init: Flattened[],\n): Generator<Flattened[]> {\n  if (index === tokens.length) {\n    return yield init;\n  }\n\n  const token = tokens[index];\n\n  if (token.type === \"group\") {\n    const fork = init.slice();\n    for (const seq of flatten(token.tokens, 0, fork)) {\n      yield* flatten(tokens, index + 1, seq);\n    }\n  } else {\n    init.push(token);\n  }\n\n  yield* flatten(tokens, index + 1, init);\n}\n\n/**\n * Transform a flat sequence of tokens into a regular expression.\n */\nfunction sequenceToRegExp(tokens: Flattened[], delimiter: string, keys: Keys) {\n  let result = \"\";\n  let backtrack = \"\";\n  let isSafeSegmentParam = true;\n\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n\n    if (token.type === \"text\") {\n      result += escape(token.value);\n      backtrack += token.value;\n      isSafeSegmentParam ||= token.value.includes(delimiter);\n      continue;\n    }\n\n    if (token.type === \"param\" || token.type === \"wildcard\") {\n      if (!isSafeSegmentParam && !backtrack) {\n        throw new TypeError(`Missing text after \"${token.name}\": ${DEBUG_URL}`);\n      }\n\n      if (token.type === \"param\") {\n        result += `(${negate(delimiter, isSafeSegmentParam ? \"\" : backtrack)}+)`;\n      } else {\n        result += `([\\\\s\\\\S]+)`;\n      }\n\n      keys.push(token);\n      backtrack = \"\";\n      isSafeSegmentParam = false;\n      continue;\n    }\n  }\n\n  return result;\n}\n\nfunction negate(delimiter: string, backtrack: string) {\n  if (backtrack.length < 2) {\n    if (delimiter.length < 2) return `[^${escape(delimiter + backtrack)}]`;\n    return `(?:(?!${escape(delimiter)})[^${escape(backtrack)}])`;\n  }\n  if (delimiter.length < 2) {\n    return `(?:(?!${escape(backtrack)})[^${escape(delimiter)}])`;\n  }\n  return `(?:(?!${escape(backtrack)}|${escape(delimiter)})[\\\\s\\\\S])`;\n}\n\n/**\n * Stringify token data into a path string.\n */\nexport function stringify(data: TokenData) {\n  return data.tokens\n    .map(function stringifyToken(token, index, tokens): string {\n      if (token.type === \"text\") return escapeText(token.value);\n      if (token.type === \"group\") {\n        return `{${token.tokens.map(stringifyToken).join(\"\")}}`;\n      }\n\n      const isSafe =\n        isNameSafe(token.name) && isNextNameSafe(tokens[index + 1]);\n      const key = isSafe ? token.name : JSON.stringify(token.name);\n\n      if (token.type === \"param\") return `:${key}`;\n      if (token.type === \"wildcard\") return `*${key}`;\n      throw new TypeError(`Unexpected token: ${token}`);\n    })\n    .join(\"\");\n}\n\nfunction isNameSafe(name: string) {\n  const [first, ...rest] = name;\n  if (!ID_START.test(first)) return false;\n  return rest.every((char) => ID_CONTINUE.test(char));\n}\n\nfunction isNextNameSafe(token: Token | undefined) {\n  if (token?.type !== \"text\") return true;\n  return !ID_CONTINUE.test(token.value[0]);\n}\n"], "mappings": ";;;;;;;;;AAoRA,YAAA,QAAA;AAkDA,YAAA,UAAA;AAgIA,YAAA,QAAA;AAiCA,YAAA,eAAA;AAqHA,YAAA,YAAA;AA5lBA,QAAM,oBAAoB;AAC1B,QAAM,aAAa,CAAC,UAAkB;AACtC,QAAM,WAAW;AACjB,QAAM,cAAc;AACpB,QAAM,YAAY;AAkFlB,QAAM,gBAA2C;;MAE/C,KAAK;MACL,KAAK;;MAEL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;;AAMP,aAAS,WAAW,KAAW;AAC7B,aAAO,IAAI,QAAQ,oBAAoB,MAAM;IAC/C;AAKA,aAAS,OAAO,KAAW;AACzB,aAAO,IAAI,QAAQ,wBAAwB,MAAM;IACnD;AAKA,cAAU,MAAM,KAAW;AACzB,YAAM,QAAQ,CAAC,GAAG,GAAG;AACrB,UAAI,IAAI;AAER,eAAS,OAAI;AACX,YAAI,QAAQ;AAEZ,YAAI,SAAS,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG;AAC7B,mBAAS,MAAM,CAAC;AAChB,iBAAO,YAAY,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG;AACnC,qBAAS,MAAM,CAAC;UAClB;QACF,WAAW,MAAM,CAAC,MAAM,KAAK;AAC3B,cAAI,MAAM;AAEV,iBAAO,IAAI,MAAM,QAAQ;AACvB,gBAAI,MAAM,EAAE,CAAC,MAAM,KAAK;AACtB;AACA,oBAAM;AACN;YACF;AAEA,gBAAI,MAAM,CAAC,MAAM,MAAM;AACrB,uBAAS,MAAM,EAAE,CAAC;YACpB,OAAO;AACL,uBAAS,MAAM,CAAC;YAClB;UACF;AAEA,cAAI,KAAK;AACP,kBAAM,IAAI,UAAU,yBAAyB,GAAG,KAAK,SAAS,EAAE;UAClE;QACF;AAEA,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI,UAAU,6BAA6B,CAAC,KAAK,SAAS,EAAE;QACpE;AAEA,eAAO;MACT;AAEA,aAAO,IAAI,MAAM,QAAQ;AACvB,cAAM,QAAQ,MAAM,CAAC;AACrB,cAAM,OAAO,cAAc,KAAK;AAEhC,YAAI,MAAM;AACR,gBAAM,EAAE,MAAM,OAAO,KAAK,MAAK;QACjC,WAAW,UAAU,MAAM;AACzB,gBAAM,EAAE,MAAM,WAAW,OAAO,KAAK,OAAO,MAAM,GAAG,EAAC;QACxD,WAAW,UAAU,KAAK;AACxB,gBAAMA,SAAQ,KAAI;AAClB,gBAAM,EAAE,MAAM,SAAS,OAAO,GAAG,OAAAA,OAAK;QACxC,WAAW,UAAU,KAAK;AACxB,gBAAMA,SAAQ,KAAI;AAClB,gBAAM,EAAE,MAAM,YAAY,OAAO,GAAG,OAAAA,OAAK;QAC3C,OAAO;AACL,gBAAM,EAAE,MAAM,QAAQ,OAAO,GAAG,OAAO,MAAM,GAAG,EAAC;QACnD;MACF;AAEA,aAAO,EAAE,MAAM,OAAO,OAAO,GAAG,OAAO,GAAE;IAC3C;AAEA,QAAM,OAAN,MAAU;MAGR,YAAoB,QAAqC;AAArC,aAAA,SAAA;MAAwC;MAE5D,OAAI;AACF,YAAI,CAAC,KAAK,OAAO;AACf,gBAAM,OAAO,KAAK,OAAO,KAAI;AAC7B,eAAK,QAAQ,KAAK;QACpB;AACA,eAAO,KAAK;MACd;MAEA,WAAW,MAAe;AACxB,cAAM,QAAQ,KAAK,KAAI;AACvB,YAAI,MAAM,SAAS;AAAM;AACzB,aAAK,QAAQ;AACb,eAAO,MAAM;MACf;MAEA,QAAQ,MAAe;AACrB,cAAM,QAAQ,KAAK,WAAW,IAAI;AAClC,YAAI,UAAU;AAAW,iBAAO;AAChC,cAAM,EAAE,MAAM,UAAU,MAAK,IAAK,KAAK,KAAI;AAC3C,cAAM,IAAI,UACR,cAAc,QAAQ,OAAO,KAAK,cAAc,IAAI,KAAK,SAAS,EAAE;MAExE;MAEA,OAAI;AACF,YAAI,SAAS;AACb,YAAI;AACJ,eAAQ,QAAQ,KAAK,WAAW,MAAM,KAAK,KAAK,WAAW,SAAS,GAAI;AACtE,oBAAU;QACZ;AACA,eAAO;MACT;;AAqDF,QAAa,YAAb,MAAsB;MACpB,YAA4B,QAAe;AAAf,aAAA,SAAA;MAAkB;;AADhD,YAAA,YAAA;AAOA,aAAgB,MAAM,KAAa,UAAwB,CAAA,GAAE;AAC3D,YAAM,EAAE,aAAa,WAAU,IAAK;AACpC,YAAM,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC;AAE9B,eAAS,QAAQ,SAAkB;AACjC,cAAMC,UAAkB,CAAA;AAExB,eAAO,MAAM;AACX,gBAAM,OAAO,GAAG,KAAI;AACpB,cAAI;AAAM,YAAAA,QAAO,KAAK,EAAE,MAAM,QAAQ,OAAO,WAAW,IAAI,EAAC,CAAE;AAE/D,gBAAM,QAAQ,GAAG,WAAW,OAAO;AACnC,cAAI,OAAO;AACT,YAAAA,QAAO,KAAK;cACV,MAAM;cACN,MAAM;aACP;AACD;UACF;AAEA,gBAAM,WAAW,GAAG,WAAW,UAAU;AACzC,cAAI,UAAU;AACZ,YAAAA,QAAO,KAAK;cACV,MAAM;cACN,MAAM;aACP;AACD;UACF;AAEA,gBAAM,OAAO,GAAG,WAAW,GAAG;AAC9B,cAAI,MAAM;AACR,YAAAA,QAAO,KAAK;cACV,MAAM;cACN,QAAQ,QAAQ,GAAG;aACpB;AACD;UACF;AAEA,aAAG,QAAQ,OAAO;AAClB,iBAAOA;QACT;MACF;AAEA,YAAM,SAAS,QAAQ,KAAK;AAC5B,aAAO,IAAI,UAAU,MAAM;IAC7B;AAKA,aAAgB,QACd,MACA,UAAyC,CAAA,GAAE;AAE3C,YAAM,EAAE,SAAS,oBAAoB,YAAY,kBAAiB,IAChE;AACF,YAAM,OAAO,gBAAgB,YAAY,OAAO,MAAM,MAAM,OAAO;AACnE,YAAM,KAAK,iBAAiB,KAAK,QAAQ,WAAW,MAAM;AAE1D,aAAO,SAASC,MAAKC,QAAU,CAAA,GAAO;AACpC,cAAM,CAACD,OAAM,GAAG,OAAO,IAAI,GAAGC,KAAI;AAClC,YAAI,QAAQ,QAAQ;AAClB,gBAAM,IAAI,UAAU,uBAAuB,QAAQ,KAAK,IAAI,CAAC,EAAE;QACjE;AACA,eAAOD;MACT;IACF;AAKA,aAAS,iBACP,QACA,WACA,QAAsB;AAEtB,YAAM,WAAW,OAAO,IAAI,CAAC,UAC3B,gBAAgB,OAAO,WAAW,MAAM,CAAC;AAG3C,aAAO,CAAC,SAAmB;AACzB,cAAM,SAAmB,CAAC,EAAE;AAE5B,mBAAW,WAAW,UAAU;AAC9B,gBAAM,CAAC,OAAO,GAAG,MAAM,IAAI,QAAQ,IAAI;AACvC,iBAAO,CAAC,KAAK;AACb,iBAAO,KAAK,GAAG,MAAM;QACvB;AAEA,eAAO;MACT;IACF;AAKA,aAAS,gBACP,OACA,WACA,QAAsB;AAEtB,UAAI,MAAM,SAAS;AAAQ,eAAO,MAAM,CAAC,MAAM,KAAK;AAEpD,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,KAAK,iBAAiB,MAAM,QAAQ,WAAW,MAAM;AAE3D,eAAO,CAAC,SAAQ;AACd,gBAAM,CAAC,OAAO,GAAG,OAAO,IAAI,GAAG,IAAI;AACnC,cAAI,CAAC,QAAQ;AAAQ,mBAAO,CAAC,KAAK;AAClC,iBAAO,CAAC,EAAE;QACZ;MACF;AAEA,YAAM,cAAc,UAAU;AAE9B,UAAI,MAAM,SAAS,cAAc,WAAW,OAAO;AACjD,eAAO,CAAC,SAAQ;AACd,gBAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,cAAI,SAAS;AAAM,mBAAO,CAAC,IAAI,MAAM,IAAI;AAEzC,cAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG;AAC/C,kBAAM,IAAI,UAAU,aAAa,MAAM,IAAI,2BAA2B;UACxE;AAEA,iBAAO;YACL,MACG,IAAI,CAACF,QAAO,UAAS;AACpB,kBAAI,OAAOA,WAAU,UAAU;AAC7B,sBAAM,IAAI,UACR,aAAa,MAAM,IAAI,IAAI,KAAK,kBAAkB;cAEtD;AAEA,qBAAO,YAAYA,MAAK;YAC1B,CAAC,EACA,KAAK,SAAS;;QAErB;MACF;AAEA,aAAO,CAAC,SAAQ;AACd,cAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,YAAI,SAAS;AAAM,iBAAO,CAAC,IAAI,MAAM,IAAI;AAEzC,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,IAAI,UAAU,aAAa,MAAM,IAAI,kBAAkB;QAC/D;AAEA,eAAO,CAAC,YAAY,KAAK,CAAC;MAC5B;IACF;AA4BA,aAAgB,MACd,MACA,UAAuC,CAAA,GAAE;AAEzC,YAAM,EAAE,SAAS,oBAAoB,YAAY,kBAAiB,IAChE;AACF,YAAM,EAAE,QAAQ,KAAI,IAAK,aAAa,MAAM,OAAO;AAEnD,YAAM,WAAW,KAAK,IAAI,CAAC,QAAO;AAChC,YAAI,WAAW;AAAO,iBAAO;AAC7B,YAAI,IAAI,SAAS;AAAS,iBAAO;AACjC,eAAO,CAAC,UAAkB,MAAM,MAAM,SAAS,EAAE,IAAI,MAAM;MAC7D,CAAC;AAED,aAAO,SAASI,OAAM,OAAa;AACjC,cAAM,IAAI,OAAO,KAAK,KAAK;AAC3B,YAAI,CAAC;AAAG,iBAAO;AAEf,cAAMF,QAAO,EAAE,CAAC;AAChB,cAAM,SAAS,uBAAO,OAAO,IAAI;AAEjC,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAI,EAAE,CAAC,MAAM;AAAW;AAExB,gBAAM,MAAM,KAAK,IAAI,CAAC;AACtB,gBAAM,UAAU,SAAS,IAAI,CAAC;AAC9B,iBAAO,IAAI,IAAI,IAAI,QAAQ,EAAE,CAAC,CAAC;QACjC;AAEA,eAAO,EAAE,MAAAA,OAAM,OAAM;MACvB;IACF;AAEA,aAAgB,aACd,MACA,UAA8C,CAAA,GAAE;AAEhD,YAAM,EACJ,YAAY,mBACZ,MAAM,MACN,YAAY,OACZ,WAAW,KAAI,IACb;AACJ,YAAM,OAAa,CAAA;AACnB,YAAM,UAAoB,CAAA;AAC1B,YAAM,QAAQ,YAAY,KAAK;AAC/B,YAAM,QAAQ,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAChD,YAAM,QAAQ,MAAM,IAAI,CAACA,UACvBA,iBAAgB,YAAYA,QAAO,MAAMA,OAAM,OAAO,CAAC;AAGzD,iBAAW,EAAE,OAAM,KAAM,OAAO;AAC9B,mBAAW,OAAO,QAAQ,QAAQ,GAAG,CAAA,CAAE,GAAG;AACxC,gBAAMG,UAAS,iBAAiB,KAAK,WAAW,IAAI;AACpD,kBAAQ,KAAKA,OAAM;QACrB;MACF;AAEA,UAAI,UAAU,OAAO,QAAQ,KAAK,GAAG,CAAC;AACtC,UAAI;AAAU,mBAAW,MAAM,OAAO,SAAS,CAAC;AAChD,iBAAW,MAAM,MAAM,MAAM,OAAO,SAAS,CAAC;AAE9C,YAAM,SAAS,IAAI,OAAO,SAAS,KAAK;AACxC,aAAO,EAAE,QAAQ,KAAI;IACvB;AAUA,cAAU,QACR,QACA,OACA,MAAiB;AAEjB,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,MAAM;MACf;AAEA,YAAM,QAAQ,OAAO,KAAK;AAE1B,UAAI,MAAM,SAAS,SAAS;AAC1B,cAAM,OAAO,KAAK,MAAK;AACvB,mBAAW,OAAO,QAAQ,MAAM,QAAQ,GAAG,IAAI,GAAG;AAChD,iBAAO,QAAQ,QAAQ,QAAQ,GAAG,GAAG;QACvC;MACF,OAAO;AACL,aAAK,KAAK,KAAK;MACjB;AAEA,aAAO,QAAQ,QAAQ,QAAQ,GAAG,IAAI;IACxC;AAKA,aAAS,iBAAiB,QAAqB,WAAmB,MAAU;AAC1E,UAAI,SAAS;AACb,UAAI,YAAY;AAChB,UAAI,qBAAqB;AAEzB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,QAAQ,OAAO,CAAC;AAEtB,YAAI,MAAM,SAAS,QAAQ;AACzB,oBAAU,OAAO,MAAM,KAAK;AAC5B,uBAAa,MAAM;AACnB,iCAAA,qBAAuB,MAAM,MAAM,SAAS,SAAS;AACrD;QACF;AAEA,YAAI,MAAM,SAAS,WAAW,MAAM,SAAS,YAAY;AACvD,cAAI,CAAC,sBAAsB,CAAC,WAAW;AACrC,kBAAM,IAAI,UAAU,uBAAuB,MAAM,IAAI,MAAM,SAAS,EAAE;UACxE;AAEA,cAAI,MAAM,SAAS,SAAS;AAC1B,sBAAU,IAAI,OAAO,WAAW,qBAAqB,KAAK,SAAS,CAAC;UACtE,OAAO;AACL,sBAAU;UACZ;AAEA,eAAK,KAAK,KAAK;AACf,sBAAY;AACZ,+BAAqB;AACrB;QACF;MACF;AAEA,aAAO;IACT;AAEA,aAAS,OAAO,WAAmB,WAAiB;AAClD,UAAI,UAAU,SAAS,GAAG;AACxB,YAAI,UAAU,SAAS;AAAG,iBAAO,KAAK,OAAO,YAAY,SAAS,CAAC;AACnE,eAAO,SAAS,OAAO,SAAS,CAAC,MAAM,OAAO,SAAS,CAAC;MAC1D;AACA,UAAI,UAAU,SAAS,GAAG;AACxB,eAAO,SAAS,OAAO,SAAS,CAAC,MAAM,OAAO,SAAS,CAAC;MAC1D;AACA,aAAO,SAAS,OAAO,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC;IACxD;AAKA,aAAgB,UAAU,MAAe;AACvC,aAAO,KAAK,OACT,IAAI,SAAS,eAAe,OAAO,OAAO,QAAM;AAC/C,YAAI,MAAM,SAAS;AAAQ,iBAAO,WAAW,MAAM,KAAK;AACxD,YAAI,MAAM,SAAS,SAAS;AAC1B,iBAAO,IAAI,MAAM,OAAO,IAAI,cAAc,EAAE,KAAK,EAAE,CAAC;QACtD;AAEA,cAAM,SACJ,WAAW,MAAM,IAAI,KAAK,eAAe,OAAO,QAAQ,CAAC,CAAC;AAC5D,cAAM,MAAM,SAAS,MAAM,OAAO,KAAK,UAAU,MAAM,IAAI;AAE3D,YAAI,MAAM,SAAS;AAAS,iBAAO,IAAI,GAAG;AAC1C,YAAI,MAAM,SAAS;AAAY,iBAAO,IAAI,GAAG;AAC7C,cAAM,IAAI,UAAU,qBAAqB,KAAK,EAAE;MAClD,CAAC,EACA,KAAK,EAAE;IACZ;AAEA,aAAS,WAAW,MAAY;AAC9B,YAAM,CAAC,OAAO,GAAG,IAAI,IAAI;AACzB,UAAI,CAAC,SAAS,KAAK,KAAK;AAAG,eAAO;AAClC,aAAO,KAAK,MAAM,CAAC,SAAS,YAAY,KAAK,IAAI,CAAC;IACpD;AAEA,aAAS,eAAe,OAAwB;AAC9C,WAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,UAAS;AAAQ,eAAO;AACnC,aAAO,CAAC,YAAY,KAAK,MAAM,MAAM,CAAC,CAAC;IACzC;;;", "names": ["value", "tokens", "path", "data", "match", "regexp"]}