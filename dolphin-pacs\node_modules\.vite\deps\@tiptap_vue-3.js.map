{"version": 3, "sources": ["../../@tiptap/vue-3/src/Editor.ts", "../../@tiptap/vue-3/src/EditorContent.ts", "../../@tiptap/vue-3/src/NodeViewContent.ts", "../../@tiptap/vue-3/src/NodeViewWrapper.ts", "../../@tiptap/vue-3/src/useEditor.ts", "../../@tiptap/vue-3/src/VueMarkViewRenderer.ts", "../../@tiptap/vue-3/src/VueRenderer.ts", "../../@tiptap/vue-3/src/VueNodeViewRenderer.ts", "../../@tiptap/vue-3/src/index.ts"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport type { EditorOptions, Storage } from '@tiptap/core'\nimport { Editor as CoreEditor } from '@tiptap/core'\nimport type { EditorState, Plugin, PluginKey } from '@tiptap/pm/state'\nimport type { AppContext, ComponentInternalInstance, ComponentPublicInstance, Ref } from 'vue'\nimport { customRef, markRaw } from 'vue'\n\nfunction useDebouncedRef<T>(value: T) {\n  return customRef<T>((track, trigger) => {\n    return {\n      get() {\n        track()\n        return value\n      },\n      set(newValue) {\n        // update state\n        value = newValue\n\n        // update view as soon as possible\n        requestAnimationFrame(() => {\n          requestAnimationFrame(() => {\n            trigger()\n          })\n        })\n      },\n    }\n  })\n}\n\nexport type ContentComponent = ComponentInternalInstance & {\n  ctx: ComponentPublicInstance\n}\n\nexport class Editor extends CoreEditor {\n  private reactiveState: Ref<EditorState>\n\n  private reactiveExtensionStorage: Ref<Storage>\n\n  public contentComponent: ContentComponent | null = null\n\n  public appContext: AppContext | null = null\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super(options)\n\n    this.reactiveState = useDebouncedRef(this.view.state)\n    this.reactiveExtensionStorage = useDebouncedRef(this.extensionStorage)\n\n    this.on('beforeTransaction', ({ nextState }) => {\n      this.reactiveState.value = nextState\n      this.reactiveExtensionStorage.value = this.extensionStorage\n    })\n\n    return markRaw(this) // eslint-disable-line\n  }\n\n  get state() {\n    return this.reactiveState ? this.reactiveState.value : this.view.state\n  }\n\n  get storage() {\n    return this.reactiveExtensionStorage ? this.reactiveExtensionStorage.value : super.storage\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const nextState = super.registerPlugin(plugin, handlePlugins)\n\n    if (this.reactiveState) {\n      this.reactiveState.value = nextState\n    }\n\n    return nextState\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   */\n  public unregisterPlugin(nameOrPluginKey: string | PluginKey): EditorState | undefined {\n    const nextState = super.unregisterPlugin(nameOrPluginKey)\n\n    if (this.reactiveState && nextState) {\n      this.reactiveState.value = nextState\n    }\n\n    return nextState\n  }\n}\n", "import type { PropType, Ref } from 'vue'\nimport { defineComponent, getCurrentInstance, h, nextTick, onBeforeUnmount, ref, unref, watchEffect } from 'vue'\n\nimport type { Editor } from './Editor.js'\n\nexport const EditorContent = defineComponent({\n  name: 'EditorContent',\n\n  props: {\n    editor: {\n      default: null,\n      type: Object as PropType<Editor>,\n    },\n  },\n\n  setup(props) {\n    const rootEl: Ref<Element | undefined> = ref()\n    const instance = getCurrentInstance()\n\n    watchEffect(() => {\n      const editor = props.editor\n\n      if (editor && editor.options.element && rootEl.value) {\n        nextTick(() => {\n          if (!rootEl.value || !editor.options.element?.firstChild) {\n            return\n          }\n\n          // TODO using the new editor.mount method might allow us to remove this\n          const element = unref(rootEl.value)\n\n          rootEl.value.append(...editor.options.element.childNodes)\n\n          // @ts-ignore\n          editor.contentComponent = instance.ctx._\n\n          if (instance) {\n            editor.appContext = {\n              ...instance.appContext,\n              // <PERSON><PERSON> internally uses prototype chain to forward/shadow injects across the entire component chain\n              // so don't use object spread operator or 'Object.assign' and just set `provides` as is on editor's appContext\n              // @ts-expect-error forward instance's 'provides' into appContext\n              provides: instance.provides,\n            }\n          }\n\n          editor.setOptions({\n            element,\n          })\n\n          editor.createNodeViews()\n        })\n      }\n    })\n\n    onBeforeUnmount(() => {\n      const editor = props.editor\n\n      if (!editor) {\n        return\n      }\n\n      editor.contentComponent = null\n      editor.appContext = null\n    })\n\n    return { rootEl }\n  },\n\n  render() {\n    return h('div', {\n      ref: (el: any) => {\n        this.rootEl = el\n      },\n    })\n  },\n})\n", "import { defineComponent, h } from 'vue'\n\nexport const NodeViewContent = defineComponent({\n  name: 'NodeViewContent',\n\n  props: {\n    as: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  render() {\n    return h(this.as, {\n      style: {\n        whiteSpace: 'pre-wrap',\n      },\n      'data-node-view-content': '',\n    })\n  },\n})\n", "import { defineComponent, h } from 'vue'\n\nexport const NodeViewWrapper = defineComponent({\n  name: 'NodeViewWrapper',\n\n  props: {\n    as: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  inject: ['onDragStart', 'decorationClasses'],\n\n  render() {\n    return h(\n      this.as,\n      {\n        // @ts-ignore\n        class: this.decorationClasses,\n        style: {\n          whiteSpace: 'normal',\n        },\n        'data-node-view-wrapper': '',\n        // @ts-ignore (https://github.com/vuejs/vue-next/issues/3031)\n        onDragstart: this.onDragStart,\n      },\n      this.$slots.default?.(),\n    )\n  },\n})\n", "import type { EditorOptions } from '@tiptap/core'\nimport { onBeforeUnmount, onMounted, shallowRef } from 'vue'\n\nimport { Editor } from './Editor.js'\n\nexport const useEditor = (options: Partial<EditorOptions> = {}) => {\n  const editor = shallowRef<Editor>()\n\n  onMounted(() => {\n    editor.value = new Editor(options)\n  })\n\n  onBeforeUnmount(() => {\n    // Cloning root node (and its children) to avoid content being lost by destroy\n    const nodes = editor.value?.options.element\n    const newEl = nodes?.cloneNode(true) as HTMLElement\n\n    nodes?.parentNode?.replaceChild(newEl, nodes)\n\n    editor.value?.destroy()\n  })\n\n  return editor\n}\n", "/* eslint-disable no-underscore-dangle */\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mark<PERSON>iewRendererOptions } from '@tiptap/core'\nimport { MarkView } from '@tiptap/core'\nimport type { Component, PropType } from 'vue'\nimport { defineComponent, h, toRaw } from 'vue'\n\nimport type { Editor } from './Editor.js'\nimport { VueRenderer } from './VueRenderer.js'\n\nexport interface VueMarkViewRendererOptions extends MarkViewRendererOptions {\n  as?: string\n  className?: string\n  attrs?: { [key: string]: string }\n}\n\nexport const markViewProps = {\n  editor: {\n    type: Object as PropType<MarkViewProps['editor']>,\n    required: true as const,\n  },\n  mark: {\n    type: Object as PropType<MarkViewProps['mark']>,\n    required: true as const,\n  },\n  extension: {\n    type: Object as PropType<MarkViewProps['extension']>,\n    required: true as const,\n  },\n  inline: {\n    type: Boolean as PropType<MarkViewProps['inline']>,\n    required: true as const,\n  },\n  view: {\n    type: Object as PropType<MarkViewProps['view']>,\n    required: true as const,\n  },\n  updateAttributes: {\n    type: Function as PropType<MarkViewProps['updateAttributes']>,\n    required: true as const,\n  },\n  HTMLAttributes: {\n    type: Object as PropType<MarkViewProps['HTMLAttributes']>,\n    required: true as const,\n  },\n}\n\nexport const MarkViewContent = defineComponent({\n  name: 'MarkViewContent',\n\n  props: {\n    as: {\n      type: String,\n      default: 'span',\n    },\n  },\n\n  render() {\n    return h(this.as, {\n      style: {\n        whiteSpace: 'inherit',\n      },\n      'data-mark-view-content': '',\n    })\n  },\n})\n\nexport class VueMarkView extends MarkView<Component, VueMarkViewRendererOptions> {\n  renderer: VueRenderer\n\n  constructor(component: Component, props: MarkViewProps, options?: Partial<VueMarkViewRendererOptions>) {\n    super(component, props, options)\n\n    const componentProps = { ...props, updateAttributes: this.updateAttributes.bind(this) } satisfies MarkViewProps\n\n    // Create extended component with provide\n    const extendedComponent = defineComponent({\n      extends: { ...component },\n      props: Object.keys(componentProps),\n      template: (this.component as any).template,\n      setup: reactiveProps => {\n        return (component as any).setup?.(reactiveProps, {\n          expose: () => undefined,\n        })\n      },\n      // Add support for scoped styles\n      __scopeId: (component as any).__scopeId,\n      __cssModules: (component as any).__cssModules,\n      __name: (component as any).__name,\n      __file: (component as any).__file,\n    })\n    this.renderer = new VueRenderer(extendedComponent, {\n      editor: this.editor,\n      props: componentProps,\n    })\n  }\n\n  get dom() {\n    return this.renderer.element as HTMLElement\n  }\n\n  get contentDOM() {\n    return this.dom.querySelector('[data-mark-view-content]') as HTMLElement | null\n  }\n\n  updateAttributes(attrs: Record<string, any>): void {\n    // since this.mark is now an proxy, we need to get the actual mark from it\n    const unproxiedMark = toRaw(this.mark)\n    super.updateAttributes(attrs, unproxiedMark)\n  }\n\n  destroy() {\n    this.renderer.destroy()\n  }\n}\n\nexport function VueMarkViewRenderer(\n  component: Component,\n  options: Partial<VueMarkViewRendererOptions> = {},\n): MarkViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as Editor).contentComponent) {\n      return {} as unknown as MarkView<any, any>\n    }\n\n    return new VueMarkView(component, props, options)\n  }\n}\n", "import type { Editor } from '@tiptap/core'\nimport type { Component, DefineComponent } from 'vue'\nimport { h, markRaw, reactive, render } from 'vue'\n\nimport type { Editor as ExtendedEditor } from './Editor.js'\n\nexport interface VueRendererOptions {\n  editor: Editor\n  props?: Record<string, any>\n}\n\ntype ExtendedVNode = ReturnType<typeof h> | null\n\ninterface RenderedComponent {\n  vNode: ExtendedVNode\n  destroy: () => void\n  el: Element | null\n}\n\n/**\n * This class is used to render Vue components inside the editor.\n */\nexport class VueRenderer {\n  renderedComponent!: RenderedComponent\n\n  editor: ExtendedEditor\n\n  component: Component\n\n  el: Element | null\n\n  props: Record<string, any>\n\n  constructor(component: Component, { props = {}, editor }: VueRendererOptions) {\n    this.editor = editor as ExtendedEditor\n    this.component = markRaw(component)\n    this.el = document.createElement('div')\n    this.props = reactive(props)\n    this.renderedComponent = this.renderComponent()\n  }\n\n  get element(): Element | null {\n    return this.renderedComponent.el\n  }\n\n  get ref(): any {\n    // Composition API\n    if (this.renderedComponent.vNode?.component?.exposed) {\n      return this.renderedComponent.vNode.component.exposed\n    }\n    // Option API\n    return this.renderedComponent.vNode?.component?.proxy\n  }\n\n  renderComponent() {\n    let vNode: ExtendedVNode = h(this.component as DefineComponent, this.props)\n\n    if (this.editor.appContext) {\n      vNode.appContext = this.editor.appContext\n    }\n    if (typeof document !== 'undefined' && this.el) {\n      render(vNode, this.el)\n    }\n\n    const destroy = () => {\n      if (this.el) {\n        render(null, this.el)\n      }\n      this.el = null\n      vNode = null\n    }\n\n    return { vNode, destroy, el: this.el ? this.el.firstElementChild : null }\n  }\n\n  updateProps(props: Record<string, any> = {}): void {\n    Object.entries(props).forEach(([key, value]) => {\n      this.props[key] = value\n    })\n    this.renderComponent()\n  }\n\n  destroy(): void {\n    this.renderedComponent.destroy()\n  }\n}\n", "/* eslint-disable no-underscore-dangle */\nimport type { DecorationWithType, NodeViewProps, NodeViewRenderer, NodeViewRendererOptions } from '@tiptap/core'\nimport { NodeView } from '@tiptap/core'\nimport type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport type { Component, PropType, Ref } from 'vue'\nimport { defineComponent, provide, ref } from 'vue'\n\nimport type { Editor } from './Editor.js'\nimport { VueRenderer } from './VueRenderer.js'\n\nexport const nodeViewProps = {\n  editor: {\n    type: Object as PropType<NodeViewProps['editor']>,\n    required: true as const,\n  },\n  node: {\n    type: Object as PropType<NodeViewProps['node']>,\n    required: true as const,\n  },\n  decorations: {\n    type: Object as PropType<NodeViewProps['decorations']>,\n    required: true as const,\n  },\n  selected: {\n    type: Boolean as PropType<NodeViewProps['selected']>,\n    required: true as const,\n  },\n  extension: {\n    type: Object as PropType<NodeViewProps['extension']>,\n    required: true as const,\n  },\n  getPos: {\n    type: Function as PropType<NodeViewProps['getPos']>,\n    required: true as const,\n  },\n  updateAttributes: {\n    type: Function as PropType<NodeViewProps['updateAttributes']>,\n    required: true as const,\n  },\n  deleteNode: {\n    type: Function as PropType<NodeViewProps['deleteNode']>,\n    required: true as const,\n  },\n  view: {\n    type: Object as PropType<NodeViewProps['view']>,\n    required: true as const,\n  },\n  innerDecorations: {\n    type: Object as PropType<NodeViewProps['innerDecorations']>,\n    required: true as const,\n  },\n  HTMLAttributes: {\n    type: Object as PropType<NodeViewProps['HTMLAttributes']>,\n    required: true as const,\n  },\n}\n\nexport interface VueNodeViewRendererOptions extends NodeViewRendererOptions {\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode\n        oldDecorations: readonly Decoration[]\n        oldInnerDecorations: DecorationSource\n        newNode: ProseMirrorNode\n        newDecorations: readonly Decoration[]\n        innerDecorations: DecorationSource\n        updateProps: () => void\n      }) => boolean)\n    | null\n}\n\nclass VueNodeView extends NodeView<Component, Editor, VueNodeViewRendererOptions> {\n  renderer!: VueRenderer\n\n  decorationClasses!: Ref<string>\n\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n    } satisfies NodeViewProps\n\n    const onDragStart = this.onDragStart.bind(this)\n\n    this.decorationClasses = ref(this.getDecorationClasses())\n\n    const extendedComponent = defineComponent({\n      extends: { ...this.component },\n      props: Object.keys(props),\n      template: (this.component as any).template,\n      setup: reactiveProps => {\n        provide('onDragStart', onDragStart)\n        provide('decorationClasses', this.decorationClasses)\n\n        return (this.component as any).setup?.(reactiveProps, {\n          expose: () => undefined,\n        })\n      },\n      // add support for scoped styles\n      // @ts-ignore\n      // eslint-disable-next-line\n      __scopeId: this.component.__scopeId,\n      // add support for CSS Modules\n      // @ts-ignore\n      // eslint-disable-next-line\n      __cssModules: this.component.__cssModules,\n      // add support for vue devtools\n      // @ts-ignore\n      // eslint-disable-next-line\n      __name: this.component.__name,\n      // @ts-ignore\n      // eslint-disable-next-line\n      __file: this.component.__file,\n    })\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n\n    this.renderer = new VueRenderer(extendedComponent, {\n      editor: this.editor,\n      props,\n    })\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (!this.renderer.element || !this.renderer.element.hasAttribute('data-node-view-wrapper')) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.dom.querySelector('[data-node-view-content]') as HTMLElement | null\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(node: ProseMirrorNode, decorations: readonly Decoration[], innerDecorations: DecorationSource): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.decorationClasses.value = this.getDecorationClasses()\n      this.renderer.updateProps(props)\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    if (this.renderer.element) {\n      this.renderer.element.classList.add('ProseMirror-selectednode')\n    }\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    if (this.renderer.element) {\n      this.renderer.element.classList.remove('ProseMirror-selectednode')\n    }\n  }\n\n  getDecorationClasses() {\n    return (\n      this.decorations\n        // @ts-ignore\n        .map(item => item.type.attrs.class)\n        .flat()\n        .join(' ')\n    )\n  }\n\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n  }\n}\n\nexport function VueNodeViewRenderer(\n  component: Component<NodeViewProps>,\n  options?: Partial<VueNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as Editor).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n    // check for class-component and normalize if neccessary\n    const normalizedComponent =\n      typeof component === 'function' && '__vccOpts' in component ? (component.__vccOpts as Component) : component\n\n    return new VueNodeView(normalizedComponent, props, options)\n  }\n}\n", "export { Editor } from './Editor.js'\nexport * from './EditorContent.js'\nexport * from './NodeViewContent.js'\nexport * from './NodeViewWrapper.js'\nexport * from './useEditor.js'\nexport * from './VueMarkViewRenderer.js'\nexport * from './VueNodeViewRenderer.js'\nexport * from './VueRenderer.js'\nexport * from '@tiptap/core'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,gBAAmB,OAAU;AACpC,SAAO,UAAa,CAAC,OAAO,YAAY;AACtC,WAAO;MACL,MAAM;AACJ,cAAM;AACN,eAAO;MACT;MACA,IAAI,UAAU;AAEZ,gBAAQ;AAGR,8BAAsB,MAAM;AAC1B,gCAAsB,MAAM;AAC1B,oBAAQ;UACV,CAAC;QACH,CAAC;MACH;IACF;EACF,CAAC;AACH;AAMO,IAAMA,UAAN,cAAqB,OAAW;EASrC,YAAY,UAAkC,CAAC,GAAG;AAChD,UAAM,OAAO;AALf,SAAO,mBAA4C;AAEnD,SAAO,aAAgC;AAKrC,SAAK,gBAAgB,gBAAgB,KAAK,KAAK,KAAK;AACpD,SAAK,2BAA2B,gBAAgB,KAAK,gBAAgB;AAErE,SAAK,GAAG,qBAAqB,CAAC,EAAE,UAAU,MAAM;AAC9C,WAAK,cAAc,QAAQ;AAC3B,WAAK,yBAAyB,QAAQ,KAAK;IAC7C,CAAC;AAED,WAAO,QAAQ,IAAI;EACrB;EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,gBAAgB,KAAK,cAAc,QAAQ,KAAK,KAAK;EACnE;EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,2BAA2B,KAAK,yBAAyB,QAAQ,MAAM;EACrF;;;;EAKO,eACL,QACA,eACa;AACb,UAAM,YAAY,MAAM,eAAe,QAAQ,aAAa;AAE5D,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAQ;IAC7B;AAEA,WAAO;EACT;;;;EAKO,iBAAiB,iBAA8D;AACpF,UAAM,YAAY,MAAM,iBAAiB,eAAe;AAExD,QAAI,KAAK,iBAAiB,WAAW;AACnC,WAAK,cAAc,QAAQ;IAC7B;AAEA,WAAO;EACT;AACF;ACvFO,IAAM,gBAAgB,gBAAgB;EAC3C,MAAM;EAEN,OAAO;IACL,QAAQ;MACN,SAAS;MACT,MAAM;IACR;EACF;EAEA,MAAM,OAAO;AACX,UAAM,SAAmC,IAAI;AAC7C,UAAM,WAAW,mBAAmB;AAEpC,gBAAY,MAAM;AAChB,YAAM,SAAS,MAAM;AAErB,UAAI,UAAU,OAAO,QAAQ,WAAW,OAAO,OAAO;AACpD,iBAAS,MAAM;AAvBvB,cAAA;AAwBU,cAAI,CAAC,OAAO,SAAS,GAAC,KAAA,OAAO,QAAQ,YAAf,OAAA,SAAA,GAAwB,aAAY;AACxD;UACF;AAGA,gBAAM,UAAU,MAAM,OAAO,KAAK;AAElC,iBAAO,MAAM,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAGxD,iBAAO,mBAAmB,SAAS,IAAI;AAEvC,cAAI,UAAU;AACZ,mBAAO,aAAa;cAClB,GAAG,SAAS;;;;cAIZ,UAAU,SAAS;YACrB;UACF;AAEA,iBAAO,WAAW;YAChB;UACF,CAAC;AAED,iBAAO,gBAAgB;QACzB,CAAC;MACH;IACF,CAAC;AAED,oBAAgB,MAAM;AACpB,YAAM,SAAS,MAAM;AAErB,UAAI,CAAC,QAAQ;AACX;MACF;AAEA,aAAO,mBAAmB;AAC1B,aAAO,aAAa;IACtB,CAAC;AAED,WAAO,EAAE,OAAO;EAClB;EAEA,SAAS;AACP,WAAO,EAAE,OAAO;MACd,KAAK,CAAC,OAAY;AAChB,aAAK,SAAS;MAChB;IACF,CAAC;EACH;AACF,CAAC;AC1EM,IAAM,kBAAkBC,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACX;EACF;EAEA,SAAS;AACP,WAAOC,EAAE,KAAK,IAAI;MAChB,OAAO;QACL,YAAY;MACd;MACA,0BAA0B;IAC5B,CAAC;EACH;AACF,CAAC;AClBM,IAAM,kBAAkBD,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACX;EACF;EAEA,QAAQ,CAAC,eAAe,mBAAmB;EAE3C,SAAS;AAdX,QAAA,IAAA;AAeI,WAAOC;MACL,KAAK;MACL;;QAEE,OAAO,KAAK;QACZ,OAAO;UACL,YAAY;QACd;QACA,0BAA0B;;QAE1B,aAAa,KAAK;MACpB;OACA,MAAA,KAAA,KAAK,QAAO,YAAZ,OAAA,SAAA,GAAA,KAAA,EAAA;IACF;EACF;AACF,CAAC;ACzBM,IAAM,YAAY,CAAC,UAAkC,CAAC,MAAM;AACjE,QAAM,SAAS,WAAmB;AAElC,YAAU,MAAM;AACd,WAAO,QAAQ,IAAIF,QAAO,OAAO;EACnC,CAAC;AAEDG,kBAAgB,MAAM;AAZxB,QAAA,IAAA,IAAA;AAcI,UAAM,SAAQ,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,QAAQ;AACpC,UAAM,QAAQ,SAAA,OAAA,SAAA,MAAO,UAAU,IAAA;AAE/B,KAAA,KAAA,SAAA,OAAA,SAAA,MAAO,eAAP,OAAA,SAAA,GAAmB,aAAa,OAAO,KAAA;AAEvC,KAAA,KAAA,OAAO,UAAP,OAAA,SAAA,GAAc,QAAA;EAChB,CAAC;AAED,SAAO;AACT;AEDO,IAAM,cAAN,MAAkB;EAWvB,YAAY,WAAsB,EAAE,QAAQ,CAAC,GAAG,OAAO,GAAuB;AAC5E,SAAK,SAAS;AACd,SAAK,YAAYC,QAAQ,SAAS;AAClC,SAAK,KAAK,SAAS,cAAc,KAAK;AACtC,SAAK,QAAQ,SAAS,KAAK;AAC3B,SAAK,oBAAoB,KAAK,gBAAgB;EAChD;EAEA,IAAI,UAA0B;AAC5B,WAAO,KAAK,kBAAkB;EAChC;EAEA,IAAI,MAAW;AA7CjB,QAAA,IAAA,IAAA,IAAA;AA+CI,SAAI,MAAA,KAAA,KAAK,kBAAkB,UAAvB,OAAA,SAAA,GAA8B,cAA9B,OAAA,SAAA,GAAyC,SAAS;AACpD,aAAO,KAAK,kBAAkB,MAAM,UAAU;IAChD;AAEA,YAAO,MAAA,KAAA,KAAK,kBAAkB,UAAvB,OAAA,SAAA,GAA8B,cAA9B,OAAA,SAAA,GAAyC;EAClD;EAEA,kBAAkB;AAChB,QAAI,QAAuBF,EAAE,KAAK,WAA8B,KAAK,KAAK;AAE1E,QAAI,KAAK,OAAO,YAAY;AAC1B,YAAM,aAAa,KAAK,OAAO;IACjC;AACA,QAAI,OAAO,aAAa,eAAe,KAAK,IAAI;AAC9C,aAAO,OAAO,KAAK,EAAE;IACvB;AAEA,UAAM,UAAU,MAAM;AACpB,UAAI,KAAK,IAAI;AACX,eAAO,MAAM,KAAK,EAAE;MACtB;AACA,WAAK,KAAK;AACV,cAAQ;IACV;AAEA,WAAO,EAAE,OAAO,SAAS,IAAI,KAAK,KAAK,KAAK,GAAG,oBAAoB,KAAK;EAC1E;EAEA,YAAY,QAA6B,CAAC,GAAS;AACjD,WAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC9C,WAAK,MAAM,GAAG,IAAI;IACpB,CAAC;AACD,SAAK,gBAAgB;EACvB;EAEA,UAAgB;AACd,SAAK,kBAAkB,QAAQ;EACjC;AACF;ADtEO,IAAM,gBAAgB;EAC3B,QAAQ;IACN,MAAM;IACN,UAAU;EACZ;EACA,MAAM;IACJ,MAAM;IACN,UAAU;EACZ;EACA,WAAW;IACT,MAAM;IACN,UAAU;EACZ;EACA,QAAQ;IACN,MAAM;IACN,UAAU;EACZ;EACA,MAAM;IACJ,MAAM;IACN,UAAU;EACZ;EACA,kBAAkB;IAChB,MAAM;IACN,UAAU;EACZ;EACA,gBAAgB;IACd,MAAM;IACN,UAAU;EACZ;AACF;AAEO,IAAM,kBAAkBD,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACX;EACF;EAEA,SAAS;AACP,WAAOC,EAAE,KAAK,IAAI;MAChB,OAAO;QACL,YAAY;MACd;MACA,0BAA0B;IAC5B,CAAC;EACH;AACF,CAAC;AAEM,IAAM,cAAN,cAA0B,SAAgD;EAG/E,YAAY,WAAsB,OAAsB,SAA+C;AACrG,UAAM,WAAW,OAAO,OAAO;AAE/B,UAAM,iBAAiB,EAAE,GAAG,OAAO,kBAAkB,KAAK,iBAAiB,KAAK,IAAI,EAAE;AAGtF,UAAM,oBAAoBD,gBAAgB;MACxC,SAAS,EAAE,GAAG,UAAU;MACxB,OAAO,OAAO,KAAK,cAAc;MACjC,UAAW,KAAK,UAAkB;MAClC,OAAO,CAAA,kBAAiB;AA/E9B,YAAA;AAgFQ,gBAAQ,KAAA,UAAkB,UAAlB,OAAA,SAAA,GAAA,KAAA,WAA0B,eAAe;UAC/C,QAAQ,MAAM;QAChB,CAAA;MACF;;MAEA,WAAY,UAAkB;MAC9B,cAAe,UAAkB;MACjC,QAAS,UAAkB;MAC3B,QAAS,UAAkB;IAC7B,CAAC;AACD,SAAK,WAAW,IAAI,YAAY,mBAAmB;MACjD,QAAQ,KAAK;MACb,OAAO;IACT,CAAC;EACH;EAEA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS;EACvB;EAEA,IAAI,aAAa;AACf,WAAO,KAAK,IAAI,cAAc,0BAA0B;EAC1D;EAEA,iBAAiB,OAAkC;AAEjD,UAAM,gBAAgB,MAAM,KAAK,IAAI;AACrC,UAAM,iBAAiB,OAAO,aAAa;EAC7C;EAEA,UAAU;AACR,SAAK,SAAS,QAAQ;EACxB;AACF;AAEO,SAAS,oBACd,WACA,UAA+C,CAAC,GAC9B;AAClB,SAAO,CAAA,UAAS;AAId,QAAI,CAAE,MAAM,OAAkB,kBAAkB;AAC9C,aAAO,CAAC;IACV;AAEA,WAAO,IAAI,YAAY,WAAW,OAAO,OAAO;EAClD;AACF;AEtHO,IAAM,gBAAgB;EAC3B,QAAQ;IACN,MAAM;IACN,UAAU;EACZ;EACA,MAAM;IACJ,MAAM;IACN,UAAU;EACZ;EACA,aAAa;IACX,MAAM;IACN,UAAU;EACZ;EACA,UAAU;IACR,MAAM;IACN,UAAU;EACZ;EACA,WAAW;IACT,MAAM;IACN,UAAU;EACZ;EACA,QAAQ;IACN,MAAM;IACN,UAAU;EACZ;EACA,kBAAkB;IAChB,MAAM;IACN,UAAU;EACZ;EACA,YAAY;IACV,MAAM;IACN,UAAU;EACZ;EACA,MAAM;IACJ,MAAM;IACN,UAAU;EACZ;EACA,kBAAkB;IAChB,MAAM;IACN,UAAU;EACZ;EACA,gBAAgB;IACd,MAAM;IACN,UAAU;EACZ;AACF;AAgBA,IAAM,cAAN,cAA0B,SAAwD;EAKhF,QAAQ;AACN,UAAM,QAAQ;MACZ,QAAQ,KAAK;MACb,MAAM,KAAK;MACX,aAAa,KAAK;MAClB,kBAAkB,KAAK;MACvB,MAAM,KAAK;MACX,UAAU;MACV,WAAW,KAAK;MAChB,gBAAgB,KAAK;MACrB,QAAQ,MAAM,KAAK,OAAO;MAC1B,kBAAkB,CAAC,aAAa,CAAC,MAAM,KAAK,iBAAiB,UAAU;MACvE,YAAY,MAAM,KAAK,WAAW;IACpC;AAEA,UAAM,cAAc,KAAK,YAAY,KAAK,IAAI;AAE9C,SAAK,oBAAoBI,IAAI,KAAK,qBAAqB,CAAC;AAExD,UAAM,oBAAoBJ,gBAAgB;MACxC,SAAS,EAAE,GAAG,KAAK,UAAU;MAC7B,OAAO,OAAO,KAAK,KAAK;MACxB,UAAW,KAAK,UAAkB;MAClC,OAAO,CAAA,kBAAiB;AApG9B,YAAA,IAAA;AAqGQ,gBAAQ,eAAe,WAAW;AAClC,gBAAQ,qBAAqB,KAAK,iBAAiB;AAEnD,gBAAQ,MAAA,KAAA,KAAK,WAAkB,UAAvB,OAAA,SAAA,GAAA,KAAA,IAA+B,eAAe;UACpD,QAAQ,MAAM;QAChB,CAAA;MACF;;;;MAIA,WAAW,KAAK,UAAU;;;;MAI1B,cAAc,KAAK,UAAU;;;;MAI7B,QAAQ,KAAK,UAAU;;;MAGvB,QAAQ,KAAK,UAAU;IACzB,CAAC;AAED,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,OAAO,GAAG,mBAAmB,KAAK,qBAAqB;AAE5D,SAAK,WAAW,IAAI,YAAY,mBAAmB;MACjD,QAAQ,KAAK;MACb;IACF,CAAC;EACH;;;;;EAMA,IAAI,MAAM;AACR,QAAI,CAAC,KAAK,SAAS,WAAW,CAAC,KAAK,SAAS,QAAQ,aAAa,wBAAwB,GAAG;AAC3F,YAAM,MAAM,8DAA8D;IAC5E;AAEA,WAAO,KAAK,SAAS;EACvB;;;;;EAMA,IAAI,aAAa;AACf,QAAI,KAAK,KAAK,QAAQ;AACpB,aAAO;IACT;AAEA,WAAO,KAAK,IAAI,cAAc,0BAA0B;EAC1D;;;;;EAMA,wBAAwB;AACtB,UAAM,EAAE,MAAM,GAAG,IAAI,KAAK,OAAO,MAAM;AACvC,UAAM,MAAM,KAAK,OAAO;AAExB,QAAI,OAAO,QAAQ,UAAU;AAC3B;IACF;AAEA,QAAI,QAAQ,OAAO,MAAM,MAAM,KAAK,KAAK,UAAU;AACjD,UAAI,KAAK,SAAS,MAAM,UAAU;AAChC;MACF;AAEA,WAAK,WAAW;IAClB,OAAO;AACL,UAAI,CAAC,KAAK,SAAS,MAAM,UAAU;AACjC;MACF;AAEA,WAAK,aAAa;IACpB;EACF;;;;;EAMA,OAAO,MAAuB,aAAoC,kBAA6C;AAC7G,UAAM,oBAAoB,CAAC,UAAgC;AACzD,WAAK,kBAAkB,QAAQ,KAAK,qBAAqB;AACzD,WAAK,SAAS,YAAY,KAAK;IACjC;AAEA,QAAI,OAAO,KAAK,QAAQ,WAAW,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,sBAAsB,KAAK;AAEjC,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAExB,aAAO,KAAK,QAAQ,OAAO;QACzB;QACA;QACA,SAAS;QACT,gBAAgB;QAChB;QACA;QACA,aAAa,MAAM,kBAAkB,EAAE,MAAM,aAAa,iBAAiB,CAAC;MAC9E,CAAC;IACH;AAEA,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;IACT;AAEA,QAAI,SAAS,KAAK,QAAQ,KAAK,gBAAgB,eAAe,KAAK,qBAAqB,kBAAkB;AACxG,aAAO;IACT;AAEA,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,sBAAkB,EAAE,MAAM,aAAa,iBAAiB,CAAC;AAEzD,WAAO;EACT;;;;;EAMA,aAAa;AACX,SAAK,SAAS,YAAY;MACxB,UAAU;IACZ,CAAC;AACD,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,IAAI,0BAA0B;IAChE;EACF;;;;;EAMA,eAAe;AACb,SAAK,SAAS,YAAY;MACxB,UAAU;IACZ,CAAC;AACD,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,OAAO,0BAA0B;IACnE;EACF;EAEA,uBAAuB;AACrB,WACE,KAAK,YAEF,IAAI,CAAA,SAAQ,KAAK,KAAK,MAAM,KAAK,EACjC,KAAK,EACL,KAAK,GAAG;EAEf;EAEA,UAAU;AACR,SAAK,SAAS,QAAQ;AACtB,SAAK,OAAO,IAAI,mBAAmB,KAAK,qBAAqB;EAC/D;AACF;AAEO,SAAS,oBACd,WACA,SACkB;AAClB,SAAO,CAAA,UAAS;AAId,QAAI,CAAE,MAAM,OAAkB,kBAAkB;AAC9C,aAAO,CAAC;IACV;AAEA,UAAM,sBACJ,OAAO,cAAc,cAAc,eAAe,YAAa,UAAU,YAA0B;AAErG,WAAO,IAAI,YAAY,qBAAqB,OAAO,OAAO;EAC5D;AACF;", "names": ["Editor", "defineComponent", "h", "onBeforeUnmount", "mark<PERSON>aw", "ref"]}