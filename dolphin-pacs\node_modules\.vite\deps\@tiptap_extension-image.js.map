{"version": 3, "sources": ["../../@tiptap/extension-image/src/image.ts", "../../@tiptap/extension-image/src/index.ts"], "sourcesContent": ["import { mergeAttributes, Node, nodeInputRule } from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\nexport interface SetImageOptions {\n  src: string\n  alt?: string\n  title?: string\n  width?: number\n  height?: number\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: SetImageOptions) => ReturnType\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n      width: {\n        default: null,\n      },\n      height: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64 ? 'img[src]' : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage:\n        options =>\n        ({ commands }) => {\n          return commands.insertContent({\n            type: this.name,\n            attrs: options,\n          })\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [, , alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n", "import { Image } from './image.js'\n\nexport * from './image.js'\n\nexport default Image\n"], "mappings": ";;;;;;;;AAqDO,IAAM,aAAa;AAMnB,IAAM,QAAQ,MAAK,OAAqB;EAC7C,MAAM;EAEN,aAAa;AACX,WAAO;MACL,QAAQ;MACR,aAAa;MACb,gBAAgB,CAAC;IACnB;EACF;EAEA,SAAS;AACP,WAAO,KAAK,QAAQ;EACtB;EAEA,QAAQ;AACN,WAAO,KAAK,QAAQ,SAAS,WAAW;EAC1C;EAEA,WAAW;EAEX,gBAAgB;AACd,WAAO;MACL,KAAK;QACH,SAAS;MACX;MACA,KAAK;QACH,SAAS;MACX;MACA,OAAO;QACL,SAAS;MACX;MACA,OAAO;QACL,SAAS;MACX;MACA,QAAQ;QACN,SAAS;MACX;IACF;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK,KAAK,QAAQ,cAAc,aAAa;MAC/C;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,OAAO,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;EAC7E;EAEA,cAAc;AACZ,WAAO;MACL,UACE,CAAA,YACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,cAAc;UAC5B,MAAM,KAAK;UACX,OAAO;QACT,CAAC;MACH;IACJ;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;QACX,eAAe,CAAA,UAAS;AACtB,gBAAM,CAAC,EAAE,EAAE,KAAK,KAAK,KAAK,IAAI;AAE9B,iBAAO,EAAE,KAAK,KAAK,MAAM;QAC3B;MACF,CAAC;IACH;EACF;AACF,CAAC;ACtID,IAAO,gBAAQ;", "names": []}