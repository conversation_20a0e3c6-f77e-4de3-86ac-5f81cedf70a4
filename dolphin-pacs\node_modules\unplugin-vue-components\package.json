{"name": "unplugin-vue-components", "type": "module", "version": "28.8.0", "packageManager": "pnpm@10.12.4", "description": "Components auto importing for Vue", "author": "antfu <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unplugin/unplugin-vue-components#readme", "repository": {"type": "git", "url": "https://github.com/unplugin/unplugin-vue-components.git"}, "bugs": "https://github.com/unplugin/unplugin-vue-components/issues", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./nuxt": {"import": "./dist/nuxt.js", "require": "./dist/nuxt.cjs"}, "./resolvers": {"import": "./dist/resolvers.js", "require": "./dist/resolvers.cjs"}, "./rollup": {"import": "./dist/rollup.js", "require": "./dist/rollup.cjs"}, "./types": {"import": "./dist/types.js", "require": "./dist/types.cjs"}, "./vite": {"import": "./dist/vite.js", "require": "./dist/vite.cjs"}, "./webpack": {"import": "./dist/webpack.js", "require": "./dist/webpack.cjs"}, "./rspack": {"import": "./dist/rspack.js", "require": "./dist/rspack.cjs"}, "./esbuild": {"import": "./dist/esbuild.js", "require": "./dist/esbuild.cjs"}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "index.d.ts", "typesVersions": {"*": {"*": ["./dist/*"]}}, "files": ["dist"], "engines": {"node": ">=14"}, "scripts": {"build": "tsdown", "dev": "tsdown -w", "example:build": "npm -C examples/vite-vue3 run build", "example:dev": "npm -C examples/vite-vue3 run dev", "prepublishOnly": "npm run build", "lint": "eslint .", "typecheck": "tsc", "release": "bumpp && npm publish", "test": "vitest", "test:update": "vitest --u"}, "peerDependencies": {"@babel/parser": "^7.15.8", "@nuxt/kit": "^3.2.2 || ^4.0.0", "vue": "2 || 3"}, "peerDependenciesMeta": {"@babel/parser": {"optional": true}, "@nuxt/kit": {"optional": true}}, "dependencies": {"chokidar": "^3.6.0", "debug": "^4.4.1", "local-pkg": "^1.1.1", "magic-string": "^0.30.17", "mlly": "^1.7.4", "tinyglobby": "^0.2.14", "unplugin": "^2.3.5", "unplugin-utils": "^0.2.4"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@antfu/utils": "^9.2.0", "@babel/parser": "^7.27.7", "@babel/types": "^7.27.7", "@nuxt/kit": "^3.17.5", "@nuxt/schema": "^3.17.5", "@types/debug": "^4.1.12", "@types/minimatch": "^5.1.2", "@types/node": "^24.0.7", "bumpp": "^10.2.0", "compare-versions": "^6.1.1", "element-plus": "^2.10.2", "eslint": "^9.30.0", "eslint-plugin-format": "^1.0.1", "esno": "^4.8.0", "estree-walker": "^3.0.3", "minimatch": "^10.0.3", "pathe": "^2.0.3", "rollup": "^4.44.1", "tsdown": "^0.12.9", "typescript": "^5.8.3", "vite": "^7.0.0", "vitest": "^3.2.4", "vue": "3.2.45", "vue-tsc": "^2.2.10"}}