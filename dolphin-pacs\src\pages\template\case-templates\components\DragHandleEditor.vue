<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import Image from '@tiptap/extension-image'
import TextAlign from '@tiptap/extension-text-align'
import { ElMessage } from 'element-plus'

// 注意：需要安装 @tiptap/extension-drag-handle
// npm install @tiptap/extension-drag-handle

defineOptions({
  name: 'DragHandleEditor'
})

interface Props {
  modelValue?: string
  placeholder?: string
  editable?: boolean
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  editable: true,
  height: '500px'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'save': [content: string]
  'change': [content: string]
}>()

// 拖拽手柄状态
const dragHandleState = ref({
  isVisible: false,
  isDragging: false,
  currentNode: null as any
})

// 编辑器实例
const editor = useEditor({
  content: props.modelValue,
  editable: props.editable,
  extensions: [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3, 4, 5, 6]
      }
    }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 50
    }),
    TableRow,
    TableHeader,
    TableCell,
    Image.configure({
      inline: true,
      allowBase64: true,
      HTMLAttributes: {
        class: 'editor-image',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
      alignments: ['left', 'center', 'right', 'justify'],
      defaultAlignment: 'left',
    }),
    // 添加拖拽手柄扩展
    // DragHandle.configure({
    //   render: () => {
    //     const element = document.createElement('div')
    //     element.classList.add('custom-drag-handle')
    //     element.innerHTML = '⋮⋮' // 或使用图标
    //     return element
    //   },
    //   onNodeChange: ({ node, editor, pos }) => {
    //     dragHandleState.value.currentNode = node
    //     dragHandleState.value.isVisible = !!node
    //   },
    //   tippyOptions: {
    //     placement: 'left',
    //     offset: [0, 0],
    //   }
    // })
  ],
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
    emit('change', html)
  },
  editorProps: {
    attributes: {
      class: 'tiptap-editor-content drag-enabled'
    }
  }
})

// 工具栏状态
const toolbarState = reactive({
  isBold: false,
  isItalic: false,
  isUnderline: false,
  isStrike: false,
  isCode: false,
  isBulletList: false,
  isOrderedList: false,
  isBlockquote: false,
  currentHeading: 0,
  textAlign: 'left'
})

// 更新工具栏状态
const updateToolbarState = () => {
  if (!editor.value) return

  toolbarState.isBold = editor.value.isActive('bold')
  toolbarState.isItalic = editor.value.isActive('italic')
  toolbarState.isStrike = editor.value.isActive('strike')
  toolbarState.isCode = editor.value.isActive('code')
  toolbarState.isBulletList = editor.value.isActive('bulletList')
  toolbarState.isOrderedList = editor.value.isActive('orderedList')
  toolbarState.isBlockquote = editor.value.isActive('blockquote')

  // 检查当前标题级别
  for (let level = 1; level <= 6; level++) {
    if (editor.value.isActive('heading', { level })) {
      toolbarState.currentHeading = level
      break
    } else {
      toolbarState.currentHeading = 0
    }
  }

  // 检查文本对齐状态
  if (editor.value.isActive({ textAlign: 'left' })) {
    toolbarState.textAlign = 'left'
  } else if (editor.value.isActive({ textAlign: 'center' })) {
    toolbarState.textAlign = 'center'
  } else if (editor.value.isActive({ textAlign: 'right' })) {
    toolbarState.textAlign = 'right'
  } else if (editor.value.isActive({ textAlign: 'justify' })) {
    toolbarState.textAlign = 'justify'
  } else {
    toolbarState.textAlign = 'left'
  }
}

// 监听编辑器选择变化
watch(() => editor.value?.state.selection, () => {
  updateToolbarState()
}, { deep: true })

// 拖拽手柄控制方法
const lockDragHandle = () => {
  editor.value?.commands.lockDragHandle()
}

const unlockDragHandle = () => {
  editor.value?.commands.unlockDragHandle()
}

const toggleDragHandle = () => {
  editor.value?.commands.toggleDragHandle()
}

// 工具栏操作方法
const toggleBold = () => editor.value?.chain().focus().toggleBold().run()
const toggleItalic = () => editor.value?.chain().focus().toggleItalic().run()
const toggleStrike = () => editor.value?.chain().focus().toggleStrike().run()
const toggleCode = () => editor.value?.chain().focus().toggleCode().run()
const toggleBulletList = () => editor.value?.chain().focus().toggleBulletList().run()
const toggleOrderedList = () => editor.value?.chain().focus().toggleOrderedList().run()
const toggleBlockquote = () => editor.value?.chain().focus().toggleBlockquote().run()

const setHeading = (level: number) => {
  if (level === 0) {
    editor.value?.chain().focus().setParagraph().run()
  } else {
    const validLevel = Math.max(1, Math.min(6, level)) as 1 | 2 | 3 | 4 | 5 | 6
    editor.value?.chain().focus().toggleHeading({ level: validLevel }).run()
  }
}

const setTextAlign = (alignment: string) => {
  toolbarState.textAlign = alignment
  editor.value?.chain().focus().setTextAlign(alignment).run()
}

const undo = () => {
  editor.value?.chain().focus().undo().run()
}
const redo = () => {
  editor.value?.chain().focus().redo().run()
}

const addHorizontalRule = () => editor.value?.chain().focus().setHorizontalRule().run()

// 表格操作
const insertTable = () => {
  editor.value?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

const addColumnBefore = () => editor.value?.chain().focus().addColumnBefore().run()
const addColumnAfter = () => editor.value?.chain().focus().addColumnAfter().run()
const deleteColumn = () => editor.value?.chain().focus().deleteColumn().run()
const addRowBefore = () => editor.value?.chain().focus().addRowBefore().run()
const addRowAfter = () => editor.value?.chain().focus().addRowAfter().run()
const deleteRow = () => editor.value?.chain().focus().deleteRow().run()
const deleteTable = () => editor.value?.chain().focus().deleteTable().run()
const mergeCells = () => editor.value?.chain().focus().mergeCells().run()
const splitCell = () => editor.value?.chain().focus().splitCell().run()

// 链接操作
const addLink = () => {
  const url = window.prompt('请输入链接地址:')
  if (url) {
    editor.value?.chain().focus().setLink({ href: url }).run()
  }
}

const removeLink = () => editor.value?.chain().focus().unsetLink().run()

// 图片操作
const addImage = () => {
  const url = window.prompt('请输入图片地址:')
  if (url) {
    editor.value?.chain().focus().setImage({ src: url, alt: '插入的图片' }).run()
  }
}

// 保存操作
const handleSave = () => {
  const content = editor.value?.getHTML() || ''
  emit('save', content)
}

// 导出操作
const handleExport = () => {
  const content = editor.value?.getHTML() || ''
  return content
}

// 导入操作
const handleImport = (content: string) => {
  editor.value?.commands.setContent(content)
}

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue)
  }
})

watch(() => props.editable, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(newValue)
  }
})

onMounted(() => {
  updateToolbarState()
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})

// 暴露方法给父组件
defineExpose({
  // 历史操作
  undo,
  redo,
  canUndo: () => editor.value?.can().undo() || false,
  canRedo: () => editor.value?.can().redo() || false,

  // 拖拽手柄控制
  lockDragHandle,
  unlockDragHandle,
  toggleDragHandle,

  // 文件操作
  handleSave,
  handleExport,
  handleImport,

  // 文本格式
  toggleBold,
  toggleItalic,
  toggleStrike,
  toggleCode,

  // 文本对齐
  setTextAlign,

  // 标题
  setHeading,

  // 列表和引用
  toggleBulletList,
  toggleOrderedList,
  toggleBlockquote,

  // 表格操作
  insertTable,
  addColumnBefore,
  addColumnAfter,
  deleteColumn,
  addRowBefore,
  addRowAfter,
  deleteRow,
  deleteTable,
  mergeCells,
  splitCell,

  // 插入操作
  addLink,
  removeLink,
  addImage,
  addHorizontalRule,

  // 工具栏状态
  getToolbarState: () => toolbarState,

  // 编辑器实例
  getEditor: () => editor.value
})
</script>

<template>
  <div class="drag-handle-editor">
    <!-- 编辑器内容区域 -->
    <div class="editor-content-wrapper" :style="{ height: typeof props.height === 'number' ? `${props.height}px` : props.height }">
      <EditorContent
        :editor="editor"
        class="editor-content"
        :placeholder="props.placeholder"
      />
    </div>

    <!-- 状态栏 -->
    <div class="editor-status-bar">
      <div class="status-left">
        <span class="word-count">
          字符数: {{ editor?.storage.characterCount?.characters() || 0 }}
        </span>
        <span class="word-count">
          单词数: {{ editor?.storage.characterCount?.words() || 0 }}
        </span>
      </div>
      <div class="status-right">
        <span class="editor-mode">
          {{ props.editable ? '📝 编辑模式' : '👁️ 只读模式' }}
        </span>
        <span v-if="dragHandleState.isVisible" class="drag-status">
          🎯 拖拽可用
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.drag-handle-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: white;
  position: relative;

  .editor-content-wrapper {
    position: relative;
    overflow-y: auto;

    .editor-content {
      height: 100%;

      :deep(.tiptap-editor-content) {
        padding: 20px;
        outline: none;
        min-height: 100%;

        // 拖拽相关样式
        &.drag-enabled {
          .ProseMirror-selectednode {
            outline: 2px solid var(--el-color-primary);
            outline-offset: 2px;
          }
        }

        // 基础样式
        p {
          margin: 0 0 16px 0;
          line-height: 1.6;
          position: relative;

          &:last-child {
            margin-bottom: 0;
          }

          // 悬停时显示拖拽提示
          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 50%;
              transform: translateY(-50%);
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 标题样式
        h1, h2, h3, h4, h5, h6 {
          margin: 24px 0 16px 0;
          font-weight: 600;
          line-height: 1.4;
          position: relative;

          &:first-child {
            margin-top: 0;
          }

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 50%;
              transform: translateY(-50%);
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1.1em; }
        h5 { font-size: 1em; }
        h6 { font-size: 0.9em; }

        // 列表样式
        ul, ol {
          margin: 16px 0;
          padding-left: 24px;
          position: relative;

          li {
            margin: 4px 0;
            line-height: 1.6;
          }

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 8px;
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 引用样式
        blockquote {
          margin: 16px 0;
          padding: 12px 16px;
          border-left: 4px solid var(--el-color-primary);
          background: var(--el-bg-color-page);
          font-style: italic;
          position: relative;

          p {
            margin: 0;
          }

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 50%;
              transform: translateY(-50%);
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 代码样式
        code {
          background: var(--el-bg-color-page);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }

        pre {
          background: var(--el-bg-color-page);
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 16px 0;
          position: relative;

          code {
            background: none;
            padding: 0;
          }

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 16px;
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 分割线样式
        hr {
          margin: 24px 0;
          border: none;
          border-top: 2px solid var(--el-border-color);
          position: relative;

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: -10px;
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 链接样式
        a {
          color: var(--el-color-primary);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        // 图片样式
        img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 16px 0;
          position: relative;

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 16px;
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 表格样式
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 16px 0;
          position: relative;

          th, td {
            border: 1px solid var(--el-border-color);
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
          }

          th {
            background: var(--el-bg-color-page);
            font-weight: 600;
          }

          tr:nth-child(even) {
            background: var(--el-bg-color-page);
          }

          &:hover {
            &::before {
              content: '';
              position: absolute;
              left: -30px;
              top: 16px;
              width: 20px;
              height: 20px;
              background: var(--el-color-primary-light-7);
              border-radius: 4px;
              opacity: 0.5;
              transition: opacity 0.2s;
            }
          }
        }

        // 占位符样式
        .is-empty::before {
          content: attr(data-placeholder);
          float: left;
          color: var(--el-text-color-placeholder);
          pointer-events: none;
          height: 0;
        }
      }
    }
  }

  .editor-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color);
    font-size: 12px;
    color: var(--el-text-color-secondary);

    .status-left {
      display: flex;
      gap: 16px;
    }

    .status-right {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .word-count {
      font-family: monospace;
    }

    .editor-mode {
      font-weight: 500;
    }

    .drag-status {
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
}

// 自定义拖拽手柄样式
:deep(.custom-drag-handle) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 4px;
  cursor: grab;
  font-size: 12px;
  line-height: 1;
  transition: all 0.2s;

  &:hover {
    background: var(--el-color-primary-dark-2);
    transform: scale(1.1);
  }

  &:active {
    cursor: grabbing;
    transform: scale(0.95);
  }
}

// 拖拽时的样式
:deep(.ProseMirror-dragging) {
  opacity: 0.5;
  transform: rotate(5deg);
}

// 拖拽目标位置指示器
:deep(.ProseMirror-drop-cursor) {
  border-left: 2px solid var(--el-color-primary);
  height: 20px;
  margin-left: -1px;
  pointer-events: none;
}
</style>
