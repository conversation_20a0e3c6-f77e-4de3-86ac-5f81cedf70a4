{"name": "@tiptap/extension-text-style", "description": "text style extension for tiptap", "version": "3.0.9", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap extension"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "type": "module", "exports": {".": {"types": {"import": "./dist/index.d.ts", "require": "./dist/index.d.cts"}, "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./background-color": {"types": {"import": "./dist/background-color/index.d.ts", "require": "./dist/background-color/index.d.cts"}, "import": "./dist/background-color/index.js", "require": "./dist/background-color/index.cjs"}, "./color": {"types": {"import": "./dist/color/index.d.ts", "require": "./dist/color/index.d.cts"}, "import": "./dist/color/index.js", "require": "./dist/color/index.cjs"}, "./font-family": {"types": {"import": "./dist/font-family/index.d.ts", "require": "./dist/font-family/index.d.cts"}, "import": "./dist/font-family/index.js", "require": "./dist/font-family/index.cjs"}, "./font-size": {"types": {"import": "./dist/font-size/index.d.ts", "require": "./dist/font-size/index.d.cts"}, "import": "./dist/font-size/index.js", "require": "./dist/font-size/index.cjs"}, "./line-height": {"types": {"import": "./dist/line-height/index.d.ts", "require": "./dist/line-height/index.d.cts"}, "import": "./dist/line-height/index.js", "require": "./dist/line-height/index.cjs"}, "./text-style": {"types": {"import": "./dist/text-style/index.d.ts", "require": "./dist/text-style/index.d.cts"}, "import": "./dist/text-style/index.js", "require": "./dist/text-style/index.cjs"}, "./text-style-kit": {"types": {"import": "./dist/text-style-kit/index.d.ts", "require": "./dist/text-style-kit/index.d.cts"}, "import": "./dist/text-style-kit/index.js", "require": "./dist/text-style-kit/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["src", "dist"], "devDependencies": {"@tiptap/core": "^3.0.9"}, "peerDependencies": {"@tiptap/core": "^3.0.9"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/extension-text-style"}, "scripts": {"build": "tsup", "lint": "prettier ./src/ --check && eslint --cache --quiet --no-error-on-unmatched-pattern ./src/"}}