{"version": 3, "sources": ["../../@tiptap/extension-text-style/src/text-style/index.ts", "../../@tiptap/extension-text-style/src/background-color/background-color.ts", "../../@tiptap/extension-text-style/src/color/color.ts", "../../@tiptap/extension-text-style/src/font-family/font-family.ts", "../../@tiptap/extension-text-style/src/font-size/font-size.ts", "../../@tiptap/extension-text-style/src/line-height/line-height.ts", "../../@tiptap/extension-text-style/src/text-style-kit/index.ts"], "sourcesContent": ["import { Mark, mergeAttributes } from '@tiptap/core'\n\nimport type { TextStyleAttributes } from '../index.js'\n\nexport interface TextStyleOptions {\n  /**\n   * HTML attributes to add to the span element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n  /**\n   * When enabled, merges the styles of nested spans into the child span during HTML parsing.\n   * This prioritizes the style of the child span.\n   * Used when parsing content created in other editors.\n   * (Fix for ProseMirror's default behavior.)\n   * @default true\n   */\n  mergeNestedSpanStyles: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textStyle: {\n      /**\n       * Remove spans without inline style attributes.\n       * @example editor.commands.removeEmptyTextStyle()\n       */\n      removeEmptyTextStyle: () => ReturnType\n      /**\n       * Toggle a text style\n       * @param attributes The text style attributes\n       * @example editor.commands.toggleTextStyle({ fontWeight: 'bold' })\n       */\n      toggleTextStyle: (attributes?: TextStyleAttributes) => ReturnType\n    }\n  }\n}\n\nconst mergeNestedSpanStyles = (element: HTMLElement) => {\n  if (!element.children.length) {\n    return\n  }\n  const childSpans = element.querySelectorAll('span')\n\n  if (!childSpans) {\n    return\n  }\n\n  childSpans.forEach(childSpan => {\n    const childStyle = childSpan.getAttribute('style')\n    const closestParentSpanStyleOfChild = childSpan.parentElement?.closest('span')?.getAttribute('style')\n\n    childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`)\n  })\n}\n\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `text-color` and `font-family` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nexport const TextStyle = Mark.create<TextStyleOptions>({\n  name: 'textStyle',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      mergeNestedSpanStyles: true,\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'span',\n        consuming: false,\n        getAttrs: element => {\n          const hasStyles = (element as HTMLElement).hasAttribute('style')\n\n          if (!hasStyles) {\n            return false\n          }\n\n          if (this.options.mergeNestedSpanStyles) {\n            mergeNestedSpanStyles(element)\n          }\n\n          return {}\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleTextStyle:\n        attributes =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name, attributes)\n        },\n      removeEmptyTextStyle:\n        () =>\n        ({ tr }) => {\n          const { selection } = tr\n\n          // Gather all of the nodes within the selection range.\n          // We would need to go through each node individually\n          // to check if it has any inline style attributes.\n          // Otherwise, calling commands.unsetMark(this.name)\n          // removes everything from all the nodes\n          // within the selection range.\n          tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n            // Check if it's a paragraph element, if so, skip this node as we apply\n            // the text style to inline text nodes only (span).\n            if (node.isTextblock) {\n              return true\n            }\n\n            // Check if the node has no inline style attributes.\n            // Filter out non-`textStyle` marks.\n            if (\n              !node.marks\n                .filter(mark => mark.type === this.type)\n                .some(mark => Object.values(mark.attrs).some(value => !!value))\n            ) {\n              // Proceed with the removal of the `textStyle` mark for this node only\n              tr.removeMark(pos, pos + node.nodeSize, this.type)\n            }\n          })\n\n          return true\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type BackgroundColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    backgroundColor: {\n      /**\n       * Set the text color\n       * @param backgroundColor The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setBackgroundColor: (backgroundColor: string) => ReturnType\n\n      /**\n       * Unset the text backgroundColor\n       * @example editor.commands.unsetBackgroundColor()\n       */\n      unsetBackgroundColor: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    backgroundColor?: string | null\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/background-color\n */\nexport const BackgroundColor = Extension.create<BackgroundColorOptions>({\n  name: 'backgroundColor',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          backgroundColor: {\n            default: null,\n            parseHTML: element => element.style.backgroundColor?.replace(/['\"]+/g, ''),\n            renderHTML: attributes => {\n              if (!attributes.backgroundColor) {\n                return {}\n              }\n\n              return {\n                style: `background-color: ${attributes.backgroundColor}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setBackgroundColor:\n        backgroundColor =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { backgroundColor }).run()\n        },\n      unsetBackgroundColor:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { backgroundColor: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type ColorOptions = {\n  /**\n   * The types where the color can be applied\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    color: {\n      /**\n       * Set the text color\n       * @param color The color to set\n       * @example editor.commands.setColor('red')\n       */\n      setColor: (color: string) => ReturnType\n\n      /**\n       * Unset the text color\n       * @example editor.commands.unsetColor()\n       */\n      unsetColor: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    color?: string | null\n  }\n}\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nexport const Color = Extension.create<ColorOptions>({\n  name: 'color',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          color: {\n            default: null,\n            parseHTML: element => element.style.color?.replace(/['\"]+/g, ''),\n            renderHTML: attributes => {\n              if (!attributes.color) {\n                return {}\n              }\n\n              return {\n                style: `color: ${attributes.color}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setColor:\n        color =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { color }).run()\n        },\n      unsetColor:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { color: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type FontFamilyOptions = {\n  /**\n   * A list of node names where the font family can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    fontFamily: {\n      /**\n       * Set the font family\n       * @param fontFamily The font family\n       * @example editor.commands.setFontFamily('Arial')\n       */\n      setFontFamily: (fontFamily: string) => ReturnType\n      /**\n       * Unset the font family\n       * @example editor.commands.unsetFontFamily()\n       */\n      unsetFontFamily: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    fontFamily?: string | null\n  }\n}\n\n/**\n * This extension allows you to set a font family for text.\n * @see https://www.tiptap.dev/api/extensions/font-family\n */\nexport const FontFamily = Extension.create<FontFamilyOptions>({\n  name: 'fontFamily',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          fontFamily: {\n            default: null,\n            parseHTML: element => element.style.fontFamily,\n            renderHTML: attributes => {\n              if (!attributes.fontFamily) {\n                return {}\n              }\n\n              return {\n                style: `font-family: ${attributes.fontFamily}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setFontFamily:\n        fontFamily =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontFamily }).run()\n        },\n      unsetFontFamily:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontFamily: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type FontSizeOptions = {\n  /**\n   * A list of node names where the font size can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    fontSize: {\n      /**\n       * Set the font size\n       * @param fontSize The font size\n       * @example editor.commands.setFontSize('Arial')\n       */\n      setFontSize: (fontSize: string) => ReturnType\n      /**\n       * Unset the font size\n       * @example editor.commands.unsetFontSize()\n       */\n      unsetFontSize: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    fontSize?: string | null\n  }\n}\n\n/**\n * This extension allows you to set a font size for text.\n * @see https://www.tiptap.dev/api/extensions/font-size\n */\nexport const FontSize = Extension.create<FontSizeOptions>({\n  name: 'fontSize',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          fontSize: {\n            default: null,\n            parseHTML: element => element.style.fontSize,\n            renderHTML: attributes => {\n              if (!attributes.fontSize) {\n                return {}\n              }\n\n              return {\n                style: `font-size: ${attributes.fontSize}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setFontSize:\n        fontSize =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontSize }).run()\n        },\n      unsetFontSize:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { fontSize: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import '../text-style/index.js'\n\nimport { Extension } from '@tiptap/core'\n\nexport type LineHeightOptions = {\n  /**\n   * A list of node names where the line height can be applied.\n   * @default ['textStyle']\n   * @example ['heading', 'paragraph']\n   */\n  types: string[]\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    lineHeight: {\n      /**\n       * Set the line height\n       * @param lineHeight The line height\n       * @example editor.commands.setLineHeight('1.5')\n       */\n      setLineHeight: (lineHeight: string) => ReturnType\n      /**\n       * Unset the line height\n       * @example editor.commands.unsetLineHeight()\n       */\n      unsetLineHeight: () => ReturnType\n    }\n  }\n}\n\n// @ts-ignore because the module is not found during dts build\ndeclare module '@tiptap/extension-text-style' {\n  interface TextStyleAttributes {\n    lineHeight?: string | null\n  }\n}\n\n/**\n * This extension allows you to set the line-height for text.\n * @see https://www.tiptap.dev/api/extensions/line-height\n */\nexport const LineHeight = Extension.create<LineHeightOptions>({\n  name: 'lineHeight',\n\n  addOptions() {\n    return {\n      types: ['textStyle'],\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          lineHeight: {\n            default: null,\n            parseHTML: element => element.style.lineHeight,\n            renderHTML: attributes => {\n              if (!attributes.lineHeight) {\n                return {}\n              }\n\n              return {\n                style: `line-height: ${attributes.lineHeight}`,\n              }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setLineHeight:\n        lineHeight =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { lineHeight }).run()\n        },\n      unsetLineHeight:\n        () =>\n        ({ chain }) => {\n          return chain().setMark('textStyle', { lineHeight: null }).removeEmptyTextStyle().run()\n        },\n    }\n  },\n})\n", "import { Extension } from '@tiptap/core'\n\nimport type { BackgroundColorOptions } from '../background-color/index.js'\nimport { BackgroundColor } from '../background-color/index.js'\nimport type { ColorOptions } from '../color/index.js'\nimport { Color } from '../color/index.js'\nimport type { FontFamilyOptions } from '../font-family/index.js'\nimport { FontFamily } from '../font-family/index.js'\nimport type { FontSizeOptions } from '../font-size/index.js'\nimport { FontSize } from '../font-size/index.js'\nimport type { LineHeightOptions } from '../line-height/index.js'\nimport { LineHeight } from '../line-height/index.js'\nimport type { TextStyleOptions } from '../text-style/index.js'\nimport { TextStyle } from '../text-style/index.js'\n\nexport interface TextStyleKitOptions {\n  /**\n   * If set to false, the background color extension will not be registered\n   * @example backgroundColor: false\n   */\n  backgroundColor: Partial<BackgroundColorOptions> | false\n  /**\n   * If set to false, the color extension will not be registered\n   * @example color: false\n   */\n  color: Partial<ColorOptions> | false\n  /**\n   * If set to false, the font family extension will not be registered\n   * @example fontFamily: false\n   */\n  fontFamily: Partial<FontFamilyOptions> | false\n  /**\n   * If set to false, the font size extension will not be registered\n   * @example fontSize: false\n   */\n  fontSize: Partial<FontSizeOptions> | false\n  /**\n   * If set to false, the line height extension will not be registered\n   * @example lineHeight: false\n   */\n  lineHeight: Partial<LineHeightOptions> | false\n  /**\n   * If set to false, the text style extension will not be registered (required for other text style extensions)\n   * @example textStyle: false\n   */\n  textStyle: Partial<TextStyleOptions> | false\n}\n\n/**\n * The table kit is a collection of table editor extensions.\n *\n * It’s a good starting point for building your own table in Tiptap.\n */\nexport const TextStyleKit = Extension.create<TextStyleKitOptions>({\n  name: 'textStyleKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.backgroundColor !== false) {\n      extensions.push(BackgroundColor.configure(this.options.backgroundColor))\n    }\n\n    if (this.options.color !== false) {\n      extensions.push(Color.configure(this.options.color))\n    }\n\n    if (this.options.fontFamily !== false) {\n      extensions.push(FontFamily.configure(this.options.fontFamily))\n    }\n\n    if (this.options.fontSize !== false) {\n      extensions.push(FontSize.configure(this.options.fontSize))\n    }\n\n    if (this.options.lineHeight !== false) {\n      extensions.push(LineHeight.configure(this.options.lineHeight))\n    }\n\n    if (this.options.textStyle !== false) {\n      extensions.push(TextStyle.configure(this.options.textStyle))\n    }\n\n    return extensions\n  },\n})\n"], "mappings": ";;;;;;;;AAuCA,IAAM,wBAAwB,CAAC,YAAyB;AACtD,MAAI,CAAC,QAAQ,SAAS,QAAQ;AAC5B;EACF;AACA,QAAM,aAAa,QAAQ,iBAAiB,MAAM;AAElD,MAAI,CAAC,YAAY;AACf;EACF;AAEA,aAAW,QAAQ,CAAA,cAAa;AAjDlC,QAAA,IAAA;AAkDI,UAAM,aAAa,UAAU,aAAa,OAAO;AACjD,UAAM,iCAAgC,MAAA,KAAA,UAAU,kBAAV,OAAA,SAAA,GAAyB,QAAQ,MAAA,MAAjC,OAAA,SAAA,GAA0C,aAAa,OAAA;AAE7F,cAAU,aAAa,SAAS,GAAG,6BAA6B,IAAI,UAAU,EAAE;EAClF,CAAC;AACH;AAOO,IAAM,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,UAAU;EAEV,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;MACjB,uBAAuB;IACzB;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;QACL,WAAW;QACX,UAAU,CAAA,YAAW;AACnB,gBAAM,YAAa,QAAwB,aAAa,OAAO;AAE/D,cAAI,CAAC,WAAW;AACd,mBAAO;UACT;AAEA,cAAI,KAAK,QAAQ,uBAAuB;AACtC,kCAAsB,OAAO;UAC/B;AAEA,iBAAO,CAAC;QACV;MACF;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EACjF;EAEA,cAAc;AACZ,WAAO;MACL,iBACE,CAAA,eACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,MAAM,UAAU;MAClD;MACF,sBACE,MACA,CAAC,EAAE,GAAG,MAAM;AACV,cAAM,EAAE,UAAU,IAAI;AAQtB,WAAG,IAAI,aAAa,UAAU,MAAM,UAAU,IAAI,CAAC,MAAM,QAAQ;AAG/D,cAAI,KAAK,aAAa;AACpB,mBAAO;UACT;AAIA,cACE,CAAC,KAAK,MACH,OAAO,CAAA,SAAQ,KAAK,SAAS,KAAK,IAAI,EACtC,KAAK,CAAA,SAAQ,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK,CAAA,UAAS,CAAC,CAAC,KAAK,CAAC,GAChE;AAEA,eAAG,WAAW,KAAK,MAAM,KAAK,UAAU,KAAK,IAAI;UACnD;QACF,CAAC;AAED,eAAO;MACT;IACJ;EACF;AACF,CAAC;AClGM,IAAM,kBAAkB,UAAU,OAA+B;EACtE,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO,CAAC,WAAW;IACrB;EACF;EAEA,sBAAsB;AACpB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,iBAAiB;YACf,SAAS;YACT,WAAW,CAAA,YAAQ;AA3D/B,kBAAA;AA2DkC,sBAAA,KAAA,QAAQ,MAAM,oBAAd,OAAA,SAAA,GAA+B,QAAQ,UAAU,EAAA;YAAA;YACvE,YAAY,CAAA,eAAc;AACxB,kBAAI,CAAC,WAAW,iBAAiB;AAC/B,uBAAO,CAAC;cACV;AAEA,qBAAO;gBACL,OAAO,qBAAqB,WAAW,eAAe;cACxD;YACF;UACF;QACF;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,oBACE,CAAA,oBACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,gBAAgB,CAAC,EAAE,IAAI;MAC/D;MACF,sBACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,iBAAiB,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;MAC5F;IACJ;EACF;AACF,CAAC;AC9CM,IAAM,QAAQA,UAAU,OAAqB;EAClD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO,CAAC,WAAW;IACrB;EACF;EAEA,sBAAsB;AACpB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,OAAO;YACL,SAAS;YACT,WAAW,CAAA,YAAQ;AA3D/B,kBAAA;AA2DkC,sBAAA,KAAA,QAAQ,MAAM,UAAd,OAAA,SAAA,GAAqB,QAAQ,UAAU,EAAA;YAAA;YAC7D,YAAY,CAAA,eAAc;AACxB,kBAAI,CAAC,WAAW,OAAO;AACrB,uBAAO,CAAC;cACV;AAEA,qBAAO;gBACL,OAAO,UAAU,WAAW,KAAK;cACnC;YACF;UACF;QACF;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,UACE,CAAA,UACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI;MACrD;MACF,YACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,OAAO,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;MAClF;IACJ;EACF;AACF,CAAC;AC/CM,IAAM,aAAaA,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO,CAAC,WAAW;IACrB;EACF;EAEA,sBAAsB;AACpB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,YAAY;YACV,SAAS;YACT,WAAW,CAAA,YAAW,QAAQ,MAAM;YACpC,YAAY,CAAA,eAAc;AACxB,kBAAI,CAAC,WAAW,YAAY;AAC1B,uBAAO,CAAC;cACV;AAEA,qBAAO;gBACL,OAAO,gBAAgB,WAAW,UAAU;cAC9C;YACF;UACF;QACF;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,eACE,CAAA,eACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,CAAC,EAAE,IAAI;MAC1D;MACF,iBACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,YAAY,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;MACvF;IACJ;EACF;AACF,CAAC;AC9CM,IAAM,WAAWA,UAAU,OAAwB;EACxD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO,CAAC,WAAW;IACrB;EACF;EAEA,sBAAsB;AACpB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,UAAU;YACR,SAAS;YACT,WAAW,CAAA,YAAW,QAAQ,MAAM;YACpC,YAAY,CAAA,eAAc;AACxB,kBAAI,CAAC,WAAW,UAAU;AACxB,uBAAO,CAAC;cACV;AAEA,qBAAO;gBACL,OAAO,cAAc,WAAW,QAAQ;cAC1C;YACF;UACF;QACF;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,aACE,CAAA,aACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,SAAS,CAAC,EAAE,IAAI;MACxD;MACF,eACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,UAAU,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;MACrF;IACJ;EACF;AACF,CAAC;AC9CM,IAAM,aAAaA,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO,CAAC,WAAW;IACrB;EACF;EAEA,sBAAsB;AACpB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,YAAY;YACV,SAAS;YACT,WAAW,CAAA,YAAW,QAAQ,MAAM;YACpC,YAAY,CAAA,eAAc;AACxB,kBAAI,CAAC,WAAW,YAAY;AAC1B,uBAAO,CAAC;cACV;AAEA,qBAAO;gBACL,OAAO,gBAAgB,WAAW,UAAU;cAC9C;YACF;UACF;QACF;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,eACE,CAAA,eACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,CAAC,EAAE,IAAI;MAC1D;MACF,iBACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,QAAQ,aAAa,EAAE,YAAY,KAAK,CAAC,EAAE,qBAAqB,EAAE,IAAI;MACvF;IACJ;EACF;AACF,CAAC;ACnCM,IAAM,eAAeA,UAAU,OAA4B;EAChE,MAAM;EAEN,gBAAgB;AACd,UAAM,aAAa,CAAC;AAEpB,QAAI,KAAK,QAAQ,oBAAoB,OAAO;AAC1C,iBAAW,KAAK,gBAAgB,UAAU,KAAK,QAAQ,eAAe,CAAC;IACzE;AAEA,QAAI,KAAK,QAAQ,UAAU,OAAO;AAChC,iBAAW,KAAK,MAAM,UAAU,KAAK,QAAQ,KAAK,CAAC;IACrD;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;IAC7D;AAEA,WAAO;EACT;AACF,CAAC;", "names": ["Extension"]}