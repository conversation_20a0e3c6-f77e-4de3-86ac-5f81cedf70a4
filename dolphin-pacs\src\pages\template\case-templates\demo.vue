<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { ArrowLeft, RefreshLeft, RefreshRight, Download, Upload, View, Check } from "@element-plus/icons-vue"
import TemplateEditor from "./components/TemplateEditor.vue"
import { ElMessage } from "element-plus"

defineOptions({
  name: "TemplateEditorDemo"
})

// 编辑器状态
const templateContent = ref(`
<div style="font-family: SimSun, serif; font-size: 14px; line-height: 1.6; padding: 20px; max-width: 600px; margin: 0 auto;">
  <div style="text-align: center; margin-bottom: 20px;">
    <h2 style="margin: 0; font-size: 16px; font-weight: bold;">XXX 医院心脏超声检查报告单</h2>
  </div>
  
  <div style="margin-bottom: 15px;">
    <strong>患者姓名：</strong>张三 &nbsp;&nbsp;&nbsp;&nbsp;
    <strong>性别：</strong>男 &nbsp;&nbsp;&nbsp;&nbsp;
    <strong>年龄：</strong>35岁
  </div>
  
  <div style="margin-bottom: 15px;">
    <strong>检查部位：</strong>心脏超声检查 &nbsp;&nbsp;&nbsp;&nbsp;
    <strong>检查日期：</strong>2024年01月01日
  </div>
  
  <div style="margin-bottom: 15px;">
    <strong>临床诊断：</strong>胸闷待查
  </div>
  
  <div style="margin-bottom: 15px;">
    <strong>超声所见：</strong>
    <p style="margin: 5px 0; text-indent: 2em;">
      1. 心脏形态：心脏大小正常，心房心室比例协调<br>
      2. 心脏功能：左室收缩功能正常，射血分数约65%<br>
      3. 心脏瓣膜：各瓣膜形态结构正常，启闭功能良好<br>
      4. 心包：心包无积液
    </p>
  </div>
  
  <div style="margin-bottom: 15px;">
    <strong>超声诊断：</strong>
    <p style="margin: 5px 0; text-indent: 2em;">
      心脏彩超检查未见明显异常
    </p>
  </div>
  
  <div style="margin-top: 30px; text-align: right;">
    <p style="margin: 5px 0;">检查医师：___________</p>
    <p style="margin: 5px 0;">报告日期：2024年01月01日</p>
  </div>
</div>
`)

// 编辑器引用
const editorRef = ref<InstanceType<typeof TemplateEditor>>()

// 工具栏状态
const toolbarState = ref({
  isBold: false,
  isItalic: false,
  isStrike: false,
  isCode: false,
  isBulletList: false,
  isOrderedList: false,
  isBlockquote: false,
  currentHeading: 0,
  textAlign: 'left'
})

// 更新工具栏状态
const updateToolbarState = () => {
  if (editorRef.value) {
    const state = editorRef.value.getToolbarState()
    toolbarState.value = { ...state }
  }
}

// 监听内容变化来更新工具栏状态
watch(templateContent, () => {
  setTimeout(updateToolbarState, 100)
})

// 历史操作
const handleUndo = () => {
  editorRef.value?.undo()
  updateToolbarState()
}

const handleRedo = () => {
  editorRef.value?.redo()
  updateToolbarState()
}

// 文件操作
const handleSave = () => {
  editorRef.value?.handleSave()
  ElMessage.success('保存成功')
}

const handleExport = () => {
  const content = editorRef.value?.handleExport() || templateContent.value
  const blob = new Blob([content], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = '心脏超声检查报告模板.html'
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('导出成功')
}

const handleImport = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.html'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        editorRef.value?.handleImport(content)
        templateContent.value = content
        ElMessage.success('导入成功')
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 文本格式
const toggleBold = () => {
  editorRef.value?.toggleBold()
  updateToolbarState()
}

const toggleItalic = () => {
  editorRef.value?.toggleItalic()
  updateToolbarState()
}

const toggleStrike = () => {
  editorRef.value?.toggleStrike()
  updateToolbarState()
}

const toggleCode = () => {
  editorRef.value?.toggleCode()
  updateToolbarState()
}

// 文本对齐
const setTextAlign = (alignment: string) => {
  editorRef.value?.setTextAlign(alignment)
  updateToolbarState()
}

// 标题
const setHeading = (level: number) => {
  editorRef.value?.setHeading(level)
  updateToolbarState()
}

// 列表和引用
const toggleBulletList = () => {
  editorRef.value?.toggleBulletList()
  updateToolbarState()
}

const toggleOrderedList = () => {
  editorRef.value?.toggleOrderedList()
  updateToolbarState()
}

const toggleBlockquote = () => {
  editorRef.value?.toggleBlockquote()
  updateToolbarState()
}

// 表格操作
const handleTableCommand = (command: string) => {
  switch (command) {
    case 'insert':
      editorRef.value?.insertTable()
      break
    case 'addColumnBefore':
      editorRef.value?.addColumnBefore()
      break
    case 'addColumnAfter':
      editorRef.value?.addColumnAfter()
      break
    case 'deleteColumn':
      editorRef.value?.deleteColumn()
      break
    case 'addRowBefore':
      editorRef.value?.addRowBefore()
      break
    case 'addRowAfter':
      editorRef.value?.addRowAfter()
      break
    case 'deleteRow':
      editorRef.value?.deleteRow()
      break
    case 'mergeCells':
      editorRef.value?.mergeCells()
      break
    case 'splitCell':
      editorRef.value?.splitCell()
      break
    case 'deleteTable':
      editorRef.value?.deleteTable()
      break
  }
}

// 插入操作
const addLink = () => {
  editorRef.value?.addLink()
}

const addImage = () => {
  editorRef.value?.addImage()
}

const addHorizontalRule = () => {
  editorRef.value?.addHorizontalRule()
}

const handlePreview = () => {
  console.log('预览模板')
}

const goBack = () => {
  console.log('返回')
}
</script>

<template>
  <div class="template-editor-demo">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" size="default">
          返回
        </el-button>
        <div class="template-info">
          <h1 class="template-title">心脏超声检查报告</h1>
        </div>
      </div>
      <div class="header-right">
        <!-- 历史操作 -->
        <div class="action-group">
          <el-button
            @click="handleUndo"
            :icon="RefreshLeft"
            size="small"
            :disabled="!editorRef?.canUndo()"
            title="撤销"
          />
          <el-button
            @click="handleRedo"
            :icon="RefreshRight"
            size="small"
            :disabled="!editorRef?.canRedo()"
            title="重做"
          />
        </div>

        <!-- 标题选择 -->
        <div class="action-group">
          <el-select
            :model-value="toolbarState.currentHeading"
            @change="setHeading"
            size="small"
            style="width: 100px"
            placeholder="标题"
          >
            <el-option :value="0" label="正文" />
            <el-option :value="1" label="标题1" />
            <el-option :value="2" label="标题2" />
            <el-option :value="3" label="标题3" />
            <el-option :value="4" label="标题4" />
            <el-option :value="5" label="标题5" />
            <el-option :value="6" label="标题6" />
          </el-select>
        </div>

        <!-- 文本格式 -->
        <div class="action-group">
          <div
            class="icon-button"
            :class="{ active: toolbarState.isBold }"
            @click="toggleBold"
            title="粗体"
          >
            <SvgIcon name="md_icons/粗体" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.isItalic }"
            @click="toggleItalic"
            title="斜体"
          >
            <SvgIcon name="md_icons/字体斜体" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.isStrike }"
            @click="toggleStrike"
            title="删除线"
          >
            <SvgIcon name="md_icons/删除线" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.isCode }"
            @click="toggleCode"
            title="代码"
          >
            <SvgIcon name="md_icons/源代码 下游代码" :size="16" />
          </div>
        </div>

        <!-- 文本对齐 -->
        <div class="action-group">
          <div
            class="icon-button"
            :class="{ active: toolbarState.textAlign === 'left' }"
            @click="setTextAlign('left')"
            title="左对齐"
          >
            <SvgIcon name="md_icons/左对齐" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.textAlign === 'center' }"
            @click="setTextAlign('center')"
            title="居中对齐"
          >
            <SvgIcon name="md_icons/居中对齐" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.textAlign === 'right' }"
            @click="setTextAlign('right')"
            title="右对齐"
          >
            <SvgIcon name="md_icons/右对齐" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.textAlign === 'justify' }"
            @click="setTextAlign('justify')"
            title="两端对齐"
          >
            <SvgIcon name="md_icons/两端对齐" :size="16" />
          </div>
        </div>

        <!-- 列表和引用 -->
        <div class="action-group">
          <div
            class="icon-button"
            :class="{ active: toolbarState.isBulletList }"
            @click="toggleBulletList"
            title="无序列表"
          >
            <SvgIcon name="md_icons/无序列表" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.isOrderedList }"
            @click="toggleOrderedList"
            title="有序列表"
          >
            <SvgIcon name="md_icons/有序列表" :size="16" />
          </div>
          <div
            class="icon-button"
            :class="{ active: toolbarState.isBlockquote }"
            @click="toggleBlockquote"
            title="引用"
          >
            <SvgIcon name="md_icons/引用" :size="16" />
          </div>
        </div>

        <!-- 表格操作 -->
        <div class="action-group">
          <el-dropdown @command="handleTableCommand">
            <div class="icon-button dropdown-trigger" title="表格">
              <SvgIcon name="md_icons/表格" :size="16" />
              <span class="dropdown-text">表格</span>
              <span class="dropdown-arrow">▼</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="insert">插入表格</el-dropdown-item>
                <el-dropdown-item command="addColumnBefore">在前面插入列</el-dropdown-item>
                <el-dropdown-item command="addColumnAfter">在后面插入列</el-dropdown-item>
                <el-dropdown-item command="deleteColumn">删除列</el-dropdown-item>
                <el-dropdown-item command="addRowBefore">在上面插入行</el-dropdown-item>
                <el-dropdown-item command="addRowAfter">在下面插入行</el-dropdown-item>
                <el-dropdown-item command="deleteRow">删除行</el-dropdown-item>
                <el-dropdown-item command="mergeCells">合并单元格</el-dropdown-item>
                <el-dropdown-item command="splitCell">拆分单元格</el-dropdown-item>
                <el-dropdown-item command="deleteTable" divided>删除表格</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 插入操作 -->
        <div class="action-group">
          <div class="icon-button" @click="addLink" title="插入链接">
            🔗
          </div>
          <div class="icon-button" @click="addImage" title="插入图片">
            🖼️
          </div>
          <div class="icon-button" @click="addHorizontalRule" title="分割线">
            ➖
          </div>
        </div>

        <!-- 文件操作 -->
        <div class="action-group">
          <el-button @click="handleImport" :icon="Upload" size="small" title="导入">
            导入
          </el-button>
          <el-button @click="handleExport" :icon="Download" size="small" title="导出">
            导出
          </el-button>
        </div>

        <!-- 主要操作 -->
        <div class="action-group">
          <el-button @click="handlePreview" :icon="View" size="default">
            预览
          </el-button>
          <el-button
            @click="handleSave"
            type="primary"
            :icon="Check"
            size="default"
          >
            保存模板
          </el-button>
        </div>
      </div>
    </div>

    <!-- 编辑器区域 -->
    <div class="editor-container">
      <TemplateEditor
        ref="editorRef"
        v-model="templateContent"
        :height="600"
        :editable="true"
        placeholder="请开始编辑您的病例报告模板..."
        @save="handleSave"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.template-editor-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .template-info {
      .template-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;

    .action-group {
      display: flex;
      align-items: center;
      gap: 6px;

      &:not(:last-child) {
        padding-right: 12px;
        border-right: 1px solid var(--el-border-color-light);
      }
    }

    // 图标按钮样式
    .icon-button {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 28px;
      height: 28px;
      padding: 4px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--el-text-color-regular);
      background: transparent;
      border: 1px solid transparent;

      &:hover {
        background: var(--el-fill-color-light);
        color: var(--el-color-primary);
      }

      &.active {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        border-color: var(--el-color-primary-light-7);
      }

      &.dropdown-trigger {
        gap: 4px;
        min-width: auto;
        padding: 4px 6px;

        .dropdown-text {
          font-size: 12px;
        }

        .dropdown-arrow {
          font-size: 10px;
          opacity: 0.7;
        }
      }
    }
  }
}

.editor-container {
  flex: 1;
  padding: 24px;
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}
</style>
