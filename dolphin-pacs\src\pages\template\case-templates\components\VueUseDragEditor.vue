<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, defineExpose, nextTick } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import { Table } from '@tiptap/extension-table'
import { TableRow } from '@tiptap/extension-table-row'
import { TableHeader } from '@tiptap/extension-table-header'
import { TableCell } from '@tiptap/extension-table-cell'
import Image from '@tiptap/extension-image'
import TextAlign from '@tiptap/extension-text-align'
import { useDraggable, useDropZone } from '@vueuse/core'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'VueUseDragEditor'
})

interface Props {
  modelValue?: string
  placeholder?: string
  editable?: boolean
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  editable: true,
  height: '500px'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'save': [content: string]
  'change': [content: string]
}>()

// 拖拽相关状态
const dragState = ref({
  isDragging: false,
  dragElement: null as HTMLElement | null,
  dragData: null as any,
  dropTarget: null as HTMLElement | null
})

// 编辑器容器引用
const editorContainer = ref<HTMLElement>()

// 编辑器实例
const editor = useEditor({
  content: props.modelValue,
  editable: props.editable,
  extensions: [
    StarterKit.configure({
      heading: {
        levels: [1, 2, 3, 4, 5, 6]
      }
    }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 50
    }),
    TableRow,
    TableHeader,
    TableCell,
    Image.configure({
      inline: true,
      allowBase64: true,
      HTMLAttributes: {
        class: 'editor-image',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
      alignments: ['left', 'center', 'right', 'justify'],
      defaultAlignment: 'left',
    })
  ],
  onUpdate: ({ editor }) => {
    const html = editor.getHTML()
    emit('update:modelValue', html)
    emit('change', html)
  },
  editorProps: {
    attributes: {
      class: 'tiptap-editor-content vueuse-drag-enabled'
    }
  }
})

// 设置拖拽区域
const { isOverDropZone } = useDropZone(editorContainer, {
  onDrop: (files, event) => {
    handleDrop(event)
  },
  onEnter: (files, event) => {
    event.preventDefault()
  },
  onOver: (files, event) => {
    event.preventDefault()
  },
  onLeave: () => {
    dragState.value.dropTarget = null
  }
})

// 工具栏状态
const toolbarState = reactive({
  isBold: false,
  isItalic: false,
  isUnderline: false,
  isStrike: false,
  isCode: false,
  isBulletList: false,
  isOrderedList: false,
  isBlockquote: false,
  currentHeading: 0,
  textAlign: 'left'
})

// 更新工具栏状态
const updateToolbarState = () => {
  if (!editor.value) return

  toolbarState.isBold = editor.value.isActive('bold')
  toolbarState.isItalic = editor.value.isActive('italic')
  toolbarState.isStrike = editor.value.isActive('strike')
  toolbarState.isCode = editor.value.isActive('code')
  toolbarState.isBulletList = editor.value.isActive('bulletList')
  toolbarState.isOrderedList = editor.value.isActive('orderedList')
  toolbarState.isBlockquote = editor.value.isActive('blockquote')

  // 检查当前标题级别
  for (let level = 1; level <= 6; level++) {
    if (editor.value.isActive('heading', { level })) {
      toolbarState.currentHeading = level
      break
    } else {
      toolbarState.currentHeading = 0
    }
  }

  // 检查文本对齐状态
  if (editor.value.isActive({ textAlign: 'left' })) {
    toolbarState.textAlign = 'left'
  } else if (editor.value.isActive({ textAlign: 'center' })) {
    toolbarState.textAlign = 'center'
  } else if (editor.value.isActive({ textAlign: 'right' })) {
    toolbarState.textAlign = 'right'
  } else if (editor.value.isActive({ textAlign: 'justify' })) {
    toolbarState.textAlign = 'justify'
  } else {
    toolbarState.textAlign = 'left'
  }
}

// 监听编辑器选择变化
watch(() => editor.value?.state.selection, () => {
  updateToolbarState()
}, { deep: true })

// 初始化拖拽功能
const initDragFeature = () => {
  nextTick(() => {
    if (!editorContainer.value) return

    const prosemirrorElement = editorContainer.value.querySelector('.ProseMirror')
    if (!prosemirrorElement) return

    // 为所有可拖拽元素添加拖拽手柄
    const addDragHandles = () => {
      const draggableElements = prosemirrorElement.querySelectorAll('p, h1, h2, h3, h4, h5, h6, ul, ol, blockquote, pre, table, hr')
      
      draggableElements.forEach((element: Element) => {
        const htmlElement = element as HTMLElement
        
        // 避免重复添加
        if (htmlElement.querySelector('.vueuse-drag-handle')) return

        // 创建拖拽手柄
        const dragHandle = document.createElement('div')
        dragHandle.className = 'vueuse-drag-handle'
        dragHandle.innerHTML = '⋮⋮'
        dragHandle.setAttribute('draggable', 'true')
        
        // 设置拖拽手柄位置
        htmlElement.style.position = 'relative'
        htmlElement.appendChild(dragHandle)

        // 使用 VueUse 的 useDraggable
        const { isDragging } = useDraggable(dragHandle, {
          preventDefault: true,
          stopPropagation: true,
          onStart: (position, event) => {
            dragState.value.isDragging = true
            dragState.value.dragElement = htmlElement
            dragState.value.dragData = {
              type: htmlElement.tagName.toLowerCase(),
              content: htmlElement.outerHTML,
              element: htmlElement
            }
            htmlElement.classList.add('dragging')
          },
          onMove: (position, event) => {
            // 查找拖拽目标
            const target = findDropTarget(event.clientX, event.clientY)
            if (target && target !== dragState.value.dropTarget) {
              dragState.value.dropTarget?.classList.remove('drop-target')
              target.classList.add('drop-target')
              dragState.value.dropTarget = target
            }
          },
          onEnd: (position, event) => {
            dragState.value.isDragging = false
            htmlElement.classList.remove('dragging')
            
            if (dragState.value.dropTarget) {
              handleElementDrop(dragState.value.dragElement!, dragState.value.dropTarget)
              dragState.value.dropTarget.classList.remove('drop-target')
            }
            
            // 重置状态
            dragState.value.dragElement = null
            dragState.value.dragData = null
            dragState.value.dropTarget = null
          }
        })

        // 监听拖拽状态
        watch(isDragging, (dragging) => {
          if (dragging) {
            htmlElement.classList.add('dragging')
          } else {
            htmlElement.classList.remove('dragging')
          }
        })
      })
    }

    // 初始添加拖拽手柄
    addDragHandles()

    // 监听内容变化，重新添加拖拽手柄
    const observer = new MutationObserver(() => {
      addDragHandles()
    })

    observer.observe(prosemirrorElement, {
      childList: true,
      subtree: true
    })

    // 清理函数
    onBeforeUnmount(() => {
      observer.disconnect()
    })
  })
}

// 查找拖拽目标
const findDropTarget = (x: number, y: number): HTMLElement | null => {
  const elements = document.elementsFromPoint(x, y)
  for (const element of elements) {
    const htmlElement = element as HTMLElement
    if (htmlElement.matches('p, h1, h2, h3, h4, h5, h6, ul, ol, blockquote, pre, table, hr') && 
        htmlElement !== dragState.value.dragElement) {
      return htmlElement
    }
  }
  return null
}

// 处理元素拖拽放置
const handleElementDrop = (dragElement: HTMLElement, dropTarget: HTMLElement) => {
  if (!editor.value || !dragElement || !dropTarget) return

  try {
    // 获取拖拽元素的内容
    const dragContent = dragElement.outerHTML
    
    // 删除原始元素
    dragElement.remove()
    
    // 在目标位置插入元素
    dropTarget.insertAdjacentHTML('beforebegin', dragContent)
    
    // 更新编辑器内容
    const newContent = editor.value.getHTML()
    editor.value.commands.setContent(newContent)
    
    ElMessage.success('元素移动成功')
  } catch (error) {
    console.error('拖拽移动失败:', error)
    ElMessage.error('元素移动失败')
  }
}

// 处理文件拖拽放置
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (dragState.value.isDragging) {
    // 处理元素拖拽
    return
  }

  // 处理文件拖拽
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    handleFileUpload(files[0])
  }
}

// 处理文件上传
const handleFileUpload = (file: File) => {
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      const url = e.target?.result as string
      editor.value?.chain().focus().setImage({ src: url, alt: file.name }).run()
      ElMessage.success('图片上传成功')
    }
    reader.readAsDataURL(file)
  } else {
    ElMessage.warning('仅支持图片文件')
  }
}

// 工具栏操作方法
const toggleBold = () => editor.value?.chain().focus().toggleBold().run()
const toggleItalic = () => editor.value?.chain().focus().toggleItalic().run()
const toggleStrike = () => editor.value?.chain().focus().toggleStrike().run()
const toggleCode = () => editor.value?.chain().focus().toggleCode().run()
const toggleBulletList = () => editor.value?.chain().focus().toggleBulletList().run()
const toggleOrderedList = () => editor.value?.chain().focus().toggleOrderedList().run()
const toggleBlockquote = () => editor.value?.chain().focus().toggleBlockquote().run()

const setHeading = (level: number) => {
  if (level === 0) {
    editor.value?.chain().focus().setParagraph().run()
  } else {
    const validLevel = Math.max(1, Math.min(6, level)) as 1 | 2 | 3 | 4 | 5 | 6
    editor.value?.chain().focus().toggleHeading({ level: validLevel }).run()
  }
}

const setTextAlign = (alignment: string) => {
  toolbarState.textAlign = alignment
  editor.value?.chain().focus().setTextAlign(alignment).run()
}

const undo = () => {
  editor.value?.chain().focus().undo().run()
}
const redo = () => {
  editor.value?.chain().focus().redo().run()
}

const addHorizontalRule = () => editor.value?.chain().focus().setHorizontalRule().run()

// 表格操作
const insertTable = () => {
  editor.value?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
}

const addColumnBefore = () => editor.value?.chain().focus().addColumnBefore().run()
const addColumnAfter = () => editor.value?.chain().focus().addColumnAfter().run()
const deleteColumn = () => editor.value?.chain().focus().deleteColumn().run()
const addRowBefore = () => editor.value?.chain().focus().addRowBefore().run()
const addRowAfter = () => editor.value?.chain().focus().addRowAfter().run()
const deleteRow = () => editor.value?.chain().focus().deleteRow().run()
const deleteTable = () => editor.value?.chain().focus().deleteTable().run()
const mergeCells = () => editor.value?.chain().focus().mergeCells().run()
const splitCell = () => editor.value?.chain().focus().splitCell().run()

// 链接操作
const addLink = () => {
  const url = window.prompt('请输入链接地址:')
  if (url) {
    editor.value?.chain().focus().setLink({ href: url }).run()
  }
}

const removeLink = () => editor.value?.chain().focus().unsetLink().run()

// 图片操作
const addImage = () => {
  const url = window.prompt('请输入图片地址:')
  if (url) {
    editor.value?.chain().focus().setImage({ src: url, alt: '插入的图片' }).run()
  }
}

// 保存操作
const handleSave = () => {
  const content = editor.value?.getHTML() || ''
  emit('save', content)
}

// 导出操作
const handleExport = () => {
  const content = editor.value?.getHTML() || ''
  return content
}

// 导入操作
const handleImport = (content: string) => {
  editor.value?.commands.setContent(content)
}

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && newValue !== editor.value.getHTML()) {
    editor.value.commands.setContent(newValue)
  }
})

watch(() => props.editable, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(newValue)
  }
})

onMounted(() => {
  updateToolbarState()
  initDragFeature()
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})

// 暴露方法给父组件
defineExpose({
  // 历史操作
  undo,
  redo,
  canUndo: () => editor.value?.can().undo() || false,
  canRedo: () => editor.value?.can().redo() || false,

  // 文件操作
  handleSave,
  handleExport,
  handleImport,

  // 文本格式
  toggleBold,
  toggleItalic,
  toggleStrike,
  toggleCode,

  // 文本对齐
  setTextAlign,

  // 标题
  setHeading,

  // 列表和引用
  toggleBulletList,
  toggleOrderedList,
  toggleBlockquote,

  // 表格操作
  insertTable,
  addColumnBefore,
  addColumnAfter,
  deleteColumn,
  addRowBefore,
  addRowAfter,
  deleteRow,
  deleteTable,
  mergeCells,
  splitCell,

  // 插入操作
  addLink,
  removeLink,
  addImage,
  addHorizontalRule,

  // 工具栏状态
  getToolbarState: () => toolbarState,

  // 编辑器实例
  getEditor: () => editor.value
})
</script>

<template>
  <div class="vueuse-drag-editor">
    <!-- 编辑器内容区域 -->
    <div 
      ref="editorContainer"
      class="editor-content-wrapper" 
      :class="{ 'drop-zone-active': isOverDropZone }"
      :style="{ height: typeof props.height === 'number' ? `${props.height}px` : props.height }"
    >
      <EditorContent
        :editor="editor"
        class="editor-content"
        :placeholder="props.placeholder"
      />
    </div>

    <!-- 状态栏 -->
    <div class="editor-status-bar">
      <div class="status-left">
        <span class="word-count">
          字符数: {{ editor?.storage.characterCount?.characters() || 0 }}
        </span>
        <span class="word-count">
          单词数: {{ editor?.storage.characterCount?.words() || 0 }}
        </span>
      </div>
      <div class="status-right">
        <span class="editor-mode">
          {{ props.editable ? '📝 编辑模式' : '👁️ 只读模式' }}
        </span>
        <span v-if="dragState.isDragging" class="drag-status">
          🎯 正在拖拽...
        </span>
        <span v-else class="drag-tip">
          💡 悬停元素显示拖拽手柄
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.vueuse-drag-editor {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: white;
  position: relative;

  .editor-content-wrapper {
    position: relative;
    overflow-y: auto;
    transition: all 0.3s ease;

    &.drop-zone-active {
      background: var(--el-color-primary-light-9);
      border: 2px dashed var(--el-color-primary);
    }

    .editor-content {
      height: 100%;

      :deep(.tiptap-editor-content) {
        padding: 20px;
        outline: none;
        min-height: 100%;

        &.vueuse-drag-enabled {
          .ProseMirror-selectednode {
            outline: 2px solid var(--el-color-primary);
            outline-offset: 2px;
          }
        }

        // 基础样式
        p, h1, h2, h3, h4, h5, h6, ul, ol, blockquote, pre, table, hr {
          position: relative;
          transition: all 0.2s ease;

          &:hover {
            .vueuse-drag-handle {
              opacity: 0.7;
            }
          }

          &.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          &.drop-target {
            border: 2px dashed var(--el-color-primary);
            background: var(--el-color-primary-light-9);
          }
        }

        p {
          margin: 0 0 16px 0;
          line-height: 1.6;

          &:last-child {
            margin-bottom: 0;
          }
        }

        // 标题样式
        h1, h2, h3, h4, h5, h6 {
          margin: 24px 0 16px 0;
          font-weight: 600;
          line-height: 1.4;

          &:first-child {
            margin-top: 0;
          }
        }

        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1.1em; }
        h5 { font-size: 1em; }
        h6 { font-size: 0.9em; }

        // 列表样式
        ul, ol {
          margin: 16px 0;
          padding-left: 24px;

          li {
            margin: 4px 0;
            line-height: 1.6;
          }
        }

        // 引用样式
        blockquote {
          margin: 16px 0;
          padding: 12px 16px;
          border-left: 4px solid var(--el-color-primary);
          background: var(--el-bg-color-page);
          font-style: italic;

          p {
            margin: 0;
          }
        }

        // 代码样式
        code {
          background: var(--el-bg-color-page);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }

        pre {
          background: var(--el-bg-color-page);
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 16px 0;

          code {
            background: none;
            padding: 0;
          }
        }

        // 分割线样式
        hr {
          margin: 24px 0;
          border: none;
          border-top: 2px solid var(--el-border-color);
        }

        // 链接样式
        a {
          color: var(--el-color-primary);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        // 图片样式
        img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          margin: 16px 0;
        }

        // 表格样式
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 16px 0;

          th, td {
            border: 1px solid var(--el-border-color);
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
          }

          th {
            background: var(--el-bg-color-page);
            font-weight: 600;
          }

          tr:nth-child(even) {
            background: var(--el-bg-color-page);
          }
        }

        // 占位符样式
        .is-empty::before {
          content: attr(data-placeholder);
          float: left;
          color: var(--el-text-color-placeholder);
          pointer-events: none;
          height: 0;
        }
      }
    }
  }

  .editor-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color);
    font-size: 12px;
    color: var(--el-text-color-secondary);

    .status-left {
      display: flex;
      gap: 16px;
    }

    .status-right {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .word-count {
      font-family: monospace;
    }

    .editor-mode {
      font-weight: 500;
    }

    .drag-status {
      color: var(--el-color-warning);
      font-weight: 500;
      animation: pulse 1s infinite;
    }

    .drag-tip {
      color: var(--el-color-primary);
      font-weight: 500;
      font-size: 11px;
    }
  }
}

// VueUse 拖拽手柄样式
:deep(.vueuse-drag-handle) {
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 4px;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  line-height: 1;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 10;
  user-select: none;

  &:hover {
    background: var(--el-color-primary-dark-2);
    transform: translateY(-50%) scale(1.1);
    opacity: 1 !important;
  }

  &:active {
    cursor: grabbing;
    transform: translateY(-50%) scale(0.95);
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
