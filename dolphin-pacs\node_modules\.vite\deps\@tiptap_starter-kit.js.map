{"version": 3, "sources": ["../../@tiptap/core/src/jsx-runtime.ts", "../../@tiptap/extension-blockquote/src/blockquote.tsx", "../../@tiptap/extension-blockquote/src/index.ts", "../../@tiptap/extension-bold/src/bold.tsx", "../../@tiptap/extension-bold/src/index.ts", "../../@tiptap/extension-code/src/code.ts", "../../@tiptap/extension-code/src/index.ts", "../../@tiptap/extension-code-block/src/code-block.ts", "../../@tiptap/extension-code-block/src/index.ts", "../../@tiptap/extension-document/src/document.ts", "../../@tiptap/extension-document/src/index.ts", "../../@tiptap/extension-hard-break/src/hard-break.ts", "../../@tiptap/extension-hard-break/src/index.ts", "../../@tiptap/extension-heading/src/heading.ts", "../../@tiptap/extension-heading/src/index.ts", "../../@tiptap/extension-horizontal-rule/src/horizontal-rule.ts", "../../@tiptap/extension-horizontal-rule/src/index.ts", "../../@tiptap/extension-italic/src/italic.ts", "../../@tiptap/extension-italic/src/index.ts", "../../linkifyjs/dist/linkify.mjs", "../../@tiptap/extension-link/src/link.ts", "../../@tiptap/extension-link/src/helpers/autolink.ts", "../../@tiptap/extension-link/src/helpers/whitespace.ts", "../../@tiptap/extension-link/src/helpers/clickHandler.ts", "../../@tiptap/extension-link/src/helpers/pasteHandler.ts", "../../@tiptap/extension-link/src/index.ts", "../../@tiptap/extension-list/src/bullet-list/bullet-list.ts", "../../@tiptap/extension-list/src/item/list-item.ts", "../../@tiptap/extension-list/src/keymap/list-keymap.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/index.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/findListItemPos.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/getNextListDepth.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/handleBackspace.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/hasListBefore.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/hasListItemBefore.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/listItemHasSubList.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/handleDelete.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/nextListIsDeeper.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/nextListIsHigher.ts", "../../@tiptap/extension-list/src/keymap/listHelpers/hasListItemAfter.ts", "../../@tiptap/extension-list/src/kit/index.ts", "../../@tiptap/extension-list/src/ordered-list/ordered-list.ts", "../../@tiptap/extension-list/src/task-item/task-item.ts", "../../@tiptap/extension-list/src/task-list/task-list.ts", "../../@tiptap/extension-paragraph/src/paragraph.ts", "../../@tiptap/extension-paragraph/src/index.ts", "../../@tiptap/extension-strike/src/strike.ts", "../../@tiptap/extension-strike/src/index.ts", "../../@tiptap/extension-text/src/text.ts", "../../@tiptap/extension-text/src/index.ts", "../../@tiptap/extension-underline/src/underline.ts", "../../@tiptap/extension-underline/src/index.ts", "../../prosemirror-dropcursor/dist/index.js", "../../prosemirror-gapcursor/dist/index.js", "../../rope-sequence/dist/index.js", "../../prosemirror-history/dist/index.js", "../../@tiptap/extensions/src/character-count/character-count.ts", "../../@tiptap/extensions/src/drop-cursor/drop-cursor.ts", "../../@tiptap/extensions/src/focus/focus.ts", "../../@tiptap/extensions/src/gap-cursor/gap-cursor.ts", "../../@tiptap/extensions/src/placeholder/placeholder.ts", "../../@tiptap/extensions/src/selection/selection.ts", "../../@tiptap/extensions/src/trailing-node/trailing-node.ts", "../../@tiptap/extensions/src/undo-redo/undo-redo.ts", "../../@tiptap/starter-kit/src/starter-kit.ts", "../../@tiptap/starter-kit/src/index.ts"], "sourcesContent": ["export type Attributes = Record<string, any>\n\nexport type DOMOutputSpecElement = 0 | Attributes | DOMOutputSpecArray\n/**\n * Better describes the output of a `renderHTML` function in prosemirror\n * @see https://prosemirror.net/docs/ref/#model.DOMOutputSpec\n */\nexport type DOMOutputSpecArray =\n  | [string]\n  | [string, Attributes]\n  | [string, 0]\n  | [string, Attributes, 0]\n  | [string, Attributes, DOMOutputSpecArray | 0]\n  | [string, DOMOutputSpecArray]\n\n// JSX types for Tiptap's JSX runtime\n// These types only apply when using @jsxImportSource @tiptap/core\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace JSX {\n  export type Element = DOMOutputSpecArray\n  export interface IntrinsicElements {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [key: string]: any\n  }\n  export interface ElementChildrenAttribute {\n    children: unknown\n  }\n}\n\nexport type JSXRenderer = (\n  tag: 'slot' | string | ((props?: Attributes) => DOMOutputSpecArray | DOMOutputSpecElement),\n  props?: Attributes,\n  ...children: JSXRenderer[]\n) => DOMOutputSpecArray | DOMOutputSpecElement\n\nexport function Fragment(props: { children: JSXRenderer[] }) {\n  return props.children\n}\n\nexport const h: JSXRenderer = (tag, attributes) => {\n  // Treat the slot tag as the Prosemirror hole to render content into\n  if (tag === 'slot') {\n    return 0\n  }\n\n  // If the tag is a function, call it with the props\n  if (tag instanceof Function) {\n    return tag(attributes)\n  }\n\n  const { children, ...rest } = attributes ?? {}\n\n  if (tag === 'svg') {\n    throw new Error('SVG elements are not supported in the JSX syntax, use the array syntax instead')\n  }\n\n  // Otherwise, return the tag, attributes, and children\n  return [tag, rest, children]\n}\n\n// See\n// https://esbuild.github.io/api/#jsx-import-source\n// https://www.typescriptlang.org/tsconfig/#jsxImportSource\n\nexport { h as createElement, h as jsx, h as jsxDEV, h as jsxs }\n", "/** @jsxImportSource @tiptap/core */\nimport { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nexport interface BlockquoteOptions {\n  /**\n   * HTML attributes to add to the blockquote element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blockQuote: {\n      /**\n       * Set a blockquote node\n       */\n      setBlockquote: () => ReturnType\n      /**\n       * Toggle a blockquote node\n       */\n      toggleBlockquote: () => ReturnType\n      /**\n       * Unset a blockquote node\n       */\n      unsetBlockquote: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a blockquote to a `>` as input.\n */\nexport const inputRegex = /^\\s*>\\s$/\n\n/**\n * This extension allows you to create blockquotes.\n * @see https://tiptap.dev/api/nodes/blockquote\n */\nexport const Blockquote = Node.create<BlockquoteOptions>({\n  name: 'blockquote',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  group: 'block',\n\n  defining: true,\n\n  parseHTML() {\n    return [{ tag: 'blockquote' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return (\n      <blockquote {...mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)}>\n        <slot />\n      </blockquote>\n    )\n  },\n\n  addCommands() {\n    return {\n      setBlockquote:\n        () =>\n        ({ commands }) => {\n          return commands.wrapIn(this.name)\n        },\n      toggleBlockquote:\n        () =>\n        ({ commands }) => {\n          return commands.toggleWrap(this.name)\n        },\n      unsetBlockquote:\n        () =>\n        ({ commands }) => {\n          return commands.lift(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Blockquote } from './blockquote.jsx'\n\nexport * from './blockquote.jsx'\n\nexport default Blockquote\n", "/** @jsxImportSource @tiptap/core */\nimport { Mark, markInputRule, markPasteRule, mergeAttributes } from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return (\n      <strong {...mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)}>\n        <slot />\n      </strong>\n    )\n  },\n\n  addCommands() {\n    return {\n      setBold:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleBold:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetBold:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Bold } from './bold.jsx'\n\nexport * from './bold.jsx'\n\nexport default Bold\n", "import { Mark, markInputRule, markPasteRule, mergeAttributes } from '@tiptap/core'\n\nexport interface CodeOptions {\n  /**\n   * The HTML attributes applied to the code element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    code: {\n      /**\n       * Set a code mark\n       */\n      setCode: () => ReturnType\n      /**\n       * Toggle inline code\n       */\n      toggleCode: () => ReturnType\n      /**\n       * Unset a code mark\n       */\n      unsetCode: () => ReturnType\n    }\n  }\n}\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nexport const inputRegex = /(^|[^`])`([^`]+)`(?!`)/\n\n/**\n * Matches inline code while pasting.\n */\nexport const pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g\n\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nexport const Code = Mark.create<CodeOptions>({\n  name: 'code',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  excludes: '_',\n\n  code: true,\n\n  exitable: true,\n\n  parseHTML() {\n    return [{ tag: 'code' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['code', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setCode:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleCode:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetCode:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-e': () => this.editor.commands.toggleCode(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Code } from './code.js'\n\nexport * from './code.js'\n\nexport default Code\n", "import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\nimport { Plug<PERSON>, Plugin<PERSON>ey, Selection, TextSelection } from '@tiptap/pm/state'\n\nexport interface CodeBlockOptions {\n  /**\n   * Adds a prefix to language classes that are applied to code tags.\n   * @default 'language-'\n   */\n  languageClassPrefix: string\n  /**\n   * Define whether the node should be exited on triple enter.\n   * @default true\n   */\n  exitOnTripleEnter: boolean\n  /**\n   * Define whether the node should be exited on arrow down if there is no node after it.\n   * @default true\n   */\n  exitOnArrowDown: boolean\n  /**\n   * The default language.\n   * @default null\n   * @example 'js'\n   */\n  defaultLanguage: string | null | undefined\n  /**\n   * Custom HTML attributes that should be added to the rendered HTML tag.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    codeBlock: {\n      /**\n       * Set a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.setCodeBlock({ language: 'javascript' })\n       */\n      setCodeBlock: (attributes?: { language: string }) => ReturnType\n      /**\n       * Toggle a code block\n       * @param attributes Code block attributes\n       * @example editor.commands.toggleCodeBlock({ language: 'javascript' })\n       */\n      toggleCodeBlock: (attributes?: { language: string }) => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a code block with backticks.\n */\nexport const backtickInputRegex = /^```([a-z]+)?[\\s\\n]$/\n\n/**\n * Matches a code block with tildes.\n */\nexport const tildeInputRegex = /^~~~([a-z]+)?[\\s\\n]$/\n\n/**\n * This extension allows you to create code blocks.\n * @see https://tiptap.dev/api/nodes/code-block\n */\nexport const CodeBlock = Node.create<CodeBlockOptions>({\n  name: 'codeBlock',\n\n  addOptions() {\n    return {\n      languageClassPrefix: 'language-',\n      exitOnTripleEnter: true,\n      exitOnArrowDown: true,\n      defaultLanguage: null,\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'text*',\n\n  marks: '',\n\n  group: 'block',\n\n  code: true,\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      language: {\n        default: this.options.defaultLanguage,\n        parseHTML: element => {\n          const { languageClassPrefix } = this.options\n          const classNames = [...(element.firstElementChild?.classList || [])]\n          const languages = classNames\n            .filter(className => className.startsWith(languageClassPrefix))\n            .map(className => className.replace(languageClassPrefix, ''))\n          const language = languages[0]\n\n          if (!language) {\n            return null\n          }\n\n          return language\n        },\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'pre',\n        preserveWhitespace: 'full',\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'pre',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),\n      [\n        'code',\n        {\n          class: node.attrs.language ? this.options.languageClassPrefix + node.attrs.language : null,\n        },\n        0,\n      ],\n    ]\n  },\n\n  addCommands() {\n    return {\n      setCodeBlock:\n        attributes =>\n        ({ commands }) => {\n          return commands.setNode(this.name, attributes)\n        },\n      toggleCodeBlock:\n        attributes =>\n        ({ commands }) => {\n          return commands.toggleNode(this.name, 'paragraph', attributes)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),\n\n      // remove code block when at start of document or code block is empty\n      Backspace: () => {\n        const { empty, $anchor } = this.editor.state.selection\n        const isAtStart = $anchor.pos === 1\n\n        if (!empty || $anchor.parent.type.name !== this.name) {\n          return false\n        }\n\n        if (isAtStart || !$anchor.parent.textContent.length) {\n          return this.editor.commands.clearNodes()\n        }\n\n        return false\n      },\n\n      // exit node on triple enter\n      Enter: ({ editor }) => {\n        if (!this.options.exitOnTripleEnter) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n        const endsWithDoubleNewline = $from.parent.textContent.endsWith('\\n\\n')\n\n        if (!isAtEnd || !endsWithDoubleNewline) {\n          return false\n        }\n\n        return editor\n          .chain()\n          .command(({ tr }) => {\n            tr.delete($from.pos - 2, $from.pos)\n\n            return true\n          })\n          .exitCode()\n          .run()\n      },\n\n      // exit node on arrow down\n      ArrowDown: ({ editor }) => {\n        if (!this.options.exitOnArrowDown) {\n          return false\n        }\n\n        const { state } = editor\n        const { selection, doc } = state\n        const { $from, empty } = selection\n\n        if (!empty || $from.parent.type !== this.type) {\n          return false\n        }\n\n        const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2\n\n        if (!isAtEnd) {\n          return false\n        }\n\n        const after = $from.after()\n\n        if (after === undefined) {\n          return false\n        }\n\n        const nodeAfter = doc.nodeAt(after)\n\n        if (nodeAfter) {\n          return editor.commands.command(({ tr }) => {\n            tr.setSelection(Selection.near(doc.resolve(after)))\n            return true\n          })\n        }\n\n        return editor.commands.exitCode()\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      textblockTypeInputRule({\n        find: backtickInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n      textblockTypeInputRule({\n        find: tildeInputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          language: match[1],\n        }),\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // this plugin creates a code block for pasted content from VS Code\n      // we can also detect the copied code language\n      new Plugin({\n        key: new PluginKey('codeBlockVSCodeHandler'),\n        props: {\n          handlePaste: (view, event) => {\n            if (!event.clipboardData) {\n              return false\n            }\n\n            // don’t create a new code block within code blocks\n            if (this.editor.isActive(this.type.name)) {\n              return false\n            }\n\n            const text = event.clipboardData.getData('text/plain')\n            const vscode = event.clipboardData.getData('vscode-editor-data')\n            const vscodeData = vscode ? JSON.parse(vscode) : undefined\n            const language = vscodeData?.mode\n\n            if (!text || !language) {\n              return false\n            }\n\n            const { tr, schema } = view.state\n\n            // prepare a text node\n            // strip carriage return chars from text pasted as code\n            // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd\n            const textNode = schema.text(text.replace(/\\r\\n?/g, '\\n'))\n\n            // create a code block with the text node\n            // replace selection with the code block\n            tr.replaceSelectionWith(this.type.create({ language }, textNode))\n\n            if (tr.selection.$from.parent.type !== this.type) {\n              // put cursor inside the newly created code block\n              tr.setSelection(TextSelection.near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))))\n            }\n\n            // store meta information\n            // this is useful for other plugins that depends on the paste event\n            // like the paste rule plugin\n            tr.setMeta('paste', true)\n\n            view.dispatch(tr)\n\n            return true\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { CodeBlock } from './code-block.js'\n\nexport * from './code-block.js'\n\nexport default CodeBlock\n", "import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n", "import { Document } from './document.js'\n\nexport * from './document.js'\n\nexport default Document\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [{ tag: 'br' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak:\n        () =>\n        ({ commands, chain, state, editor }) => {\n          return commands.first([\n            () => commands.exitCode(),\n            () =>\n              commands.command(() => {\n                const { selection, storedMarks } = state\n\n                if (selection.$from.parent.type.spec.isolating) {\n                  return false\n                }\n\n                const { keepMarks } = this.options\n                const { splittableMarks } = editor.extensionManager\n                const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n                return chain()\n                  .insertContent({ type: this.name })\n                  .command(({ tr, dispatch }) => {\n                    if (dispatch && marks && keepMarks) {\n                      const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n                      tr.ensureMarks(filteredMarks)\n                    }\n\n                    return true\n                  })\n                  .run()\n              }),\n          ])\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n", "import { HardBreak } from './hard-break.js'\n\nexport * from './hard-break.js'\n\nexport default HardBreak\n", "import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\n\n/**\n * The heading level options.\n */\nexport type Level = 1 | 2 | 3 | 4 | 5 | 6\n\nexport interface HeadingOptions {\n  /**\n   * The available heading levels.\n   * @default [1, 2, 3, 4, 5, 6]\n   * @example [1, 2, 3]\n   */\n  levels: Level[]\n\n  /**\n   * The HTML attributes for a heading node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    heading: {\n      /**\n       * Set a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.setHeading({ level: 1 })\n       */\n      setHeading: (attributes: { level: Level }) => ReturnType\n      /**\n       * Toggle a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.toggleHeading({ level: 1 })\n       */\n      toggleHeading: (attributes: { level: Level }) => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nexport const Heading = Node.create<HeadingOptions>({\n  name: 'heading',\n\n  addOptions() {\n    return {\n      levels: [1, 2, 3, 4, 5, 6],\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'inline*',\n\n  group: 'block',\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      level: {\n        default: 1,\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return this.options.levels.map((level: Level) => ({\n      tag: `h${level}`,\n      attrs: { level },\n    }))\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const hasLevel = this.options.levels.includes(node.attrs.level)\n    const level = hasLevel ? node.attrs.level : this.options.levels[0]\n\n    return [`h${level}`, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHeading:\n        attributes =>\n        ({ commands }) => {\n          if (!this.options.levels.includes(attributes.level)) {\n            return false\n          }\n\n          return commands.setNode(this.name, attributes)\n        },\n      toggleHeading:\n        attributes =>\n        ({ commands }) => {\n          if (!this.options.levels.includes(attributes.level)) {\n            return false\n          }\n\n          return commands.toggleNode(this.name, 'paragraph', attributes)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return this.options.levels.reduce(\n      (items, level) => ({\n        ...items,\n        ...{\n          [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n        },\n      }),\n      {},\n    )\n  },\n\n  addInputRules() {\n    return this.options.levels.map(level => {\n      return textblockTypeInputRule({\n        find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n        type: this.type,\n        getAttributes: {\n          level,\n        },\n      })\n    })\n  },\n})\n", "import { Heading } from './heading.js'\n\nexport * from './heading.js'\n\nexport default Heading\n", "import { canInsertNode, isNodeSelection, mergeAttributes, Node, nodeInputRule } from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () =>\n        ({ chain, state }) => {\n          // Check if we can insert the node at the current selection\n          if (!canInsertNode(state, state.schema.nodes[this.name])) {\n            return false\n          }\n\n          const { selection } = state\n          const { $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { HorizontalRule } from './horizontal-rule.js'\n\nexport * from './horizontal-rule.js'\n\nexport default HorizontalRule\n", "import { Mark, markInputRule, markPasteRule, mergeAttributes } from '@tiptap/core'\n\nexport interface ItalicOptions {\n  /**\n   * HTML attributes to add to the italic element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    italic: {\n      /**\n       * Set an italic mark\n       * @example editor.commands.setItalic()\n       */\n      setItalic: () => ReturnType\n      /**\n       * Toggle an italic mark\n       * @example editor.commands.toggleItalic()\n       */\n      toggleItalic: () => ReturnType\n      /**\n       * Unset an italic mark\n       * @example editor.commands.unsetItalic()\n       */\n      unsetItalic: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches an italic to a *italic* on input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))$/\n\n/**\n * Matches an italic to a *italic* on paste.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))/g\n\n/**\n * Matches an italic to a _italic_ on input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))$/\n\n/**\n * Matches an italic to a _italic_ on paste.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))/g\n\n/**\n * This extension allows you to create italic text.\n * @see https://www.tiptap.dev/api/marks/italic\n */\nexport const Italic = Mark.create<ItalicOptions>({\n  name: 'italic',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'em',\n      },\n      {\n        tag: 'i',\n        getAttrs: node => (node as HTMLElement).style.fontStyle !== 'normal' && null,\n      },\n      {\n        style: 'font-style=normal',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-style=italic',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['em', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setItalic:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleItalic:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetItalic:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-i': () => this.editor.commands.toggleItalic(),\n      'Mod-I': () => this.editor.commands.toggleItalic(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Italic } from './italic.js'\n\nexport * from './italic.js'\n\nexport default Italic\n", "// THIS FILE IS AUTOMATICALLY GENERATED DO NOT EDIT DIRECTLY\n// See update-tlds.js for encoding/decoding format\n// https://data.iana.org/TLD/tlds-alpha-by-domain.txt\nconst encodedTlds = 'aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0axi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2';\n// Internationalized domain names containing non-ASCII\nconst encodedUtlds = 'ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2';\n\n/**\n * Finite State Machine generation utilities\n */\n\n/**\n * @template T\n * @typedef {{ [group: string]: T[] }} Collections\n */\n\n/**\n * @typedef {{ [group: string]: true }} Flags\n */\n\n// Keys in scanner Collections instances\nconst numeric = 'numeric';\nconst ascii = 'ascii';\nconst alpha = 'alpha';\nconst asciinumeric = 'asciinumeric';\nconst alphanumeric = 'alphanumeric';\nconst domain = 'domain';\nconst emoji = 'emoji';\nconst scheme = 'scheme';\nconst slashscheme = 'slashscheme';\nconst whitespace = 'whitespace';\n\n/**\n * @template T\n * @param {string} name\n * @param {Collections<T>} groups to register in\n * @returns {T[]} Current list of tokens in the given collection\n */\nfunction registerGroup(name, groups) {\n  if (!(name in groups)) {\n    groups[name] = [];\n  }\n  return groups[name];\n}\n\n/**\n * @template T\n * @param {T} t token to add\n * @param {Collections<T>} groups\n * @param {Flags} flags\n */\nfunction addToGroups(t, flags, groups) {\n  if (flags[numeric]) {\n    flags[asciinumeric] = true;\n    flags[alphanumeric] = true;\n  }\n  if (flags[ascii]) {\n    flags[asciinumeric] = true;\n    flags[alpha] = true;\n  }\n  if (flags[asciinumeric]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alpha]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alphanumeric]) {\n    flags[domain] = true;\n  }\n  if (flags[emoji]) {\n    flags[domain] = true;\n  }\n  for (const k in flags) {\n    const group = registerGroup(k, groups);\n    if (group.indexOf(t) < 0) {\n      group.push(t);\n    }\n  }\n}\n\n/**\n * @template T\n * @param {T} t token to check\n * @param {Collections<T>} groups\n * @returns {Flags} group flags that contain this token\n */\nfunction flagsForToken(t, groups) {\n  const result = {};\n  for (const c in groups) {\n    if (groups[c].indexOf(t) >= 0) {\n      result[c] = true;\n    }\n  }\n  return result;\n}\n\n/**\n * @template T\n * @typedef {null | T } Transition\n */\n\n/**\n * Define a basic state machine state. j is the list of character transitions,\n * jr is the list of regex-match transitions, jd is the default state to\n * transition to t is the accepting token type, if any. If this is the terminal\n * state, then it does not emit a token.\n *\n * The template type T represents the type of the token this state accepts. This\n * should be a string (such as of the token exports in `text.js`) or a\n * MultiToken subclass (from `multi.js`)\n *\n * @template T\n * @param {T} [token] Token that this state emits\n */\nfunction State(token = null) {\n  // this.n = null; // DEBUG: State name\n  /** @type {{ [input: string]: State<T> }} j */\n  this.j = {}; // IMPLEMENTATION 1\n  // this.j = []; // IMPLEMENTATION 2\n  /** @type {[RegExp, State<T>][]} jr */\n  this.jr = [];\n  /** @type {?State<T>} jd */\n  this.jd = null;\n  /** @type {?T} t */\n  this.t = token;\n}\n\n/**\n * Scanner token groups\n * @type Collections<string>\n */\nState.groups = {};\nState.prototype = {\n  accepts() {\n    return !!this.t;\n  },\n  /**\n   * Follow an existing transition from the given input to the next state.\n   * Does not mutate.\n   * @param {string} input character or token type to transition on\n   * @returns {?State<T>} the next state, if any\n   */\n  go(input) {\n    const state = this;\n    const nextState = state.j[input];\n    if (nextState) {\n      return nextState;\n    }\n    for (let i = 0; i < state.jr.length; i++) {\n      const regex = state.jr[i][0];\n      const nextState = state.jr[i][1]; // note: might be empty to prevent default jump\n      if (nextState && regex.test(input)) {\n        return nextState;\n      }\n    }\n    // Nowhere left to jump! Return default, if any\n    return state.jd;\n  },\n  /**\n   * Whether the state has a transition for the given input. Set the second\n   * argument to true to only look for an exact match (and not a default or\n   * regular-expression-based transition)\n   * @param {string} input\n   * @param {boolean} exactOnly\n   */\n  has(input, exactOnly = false) {\n    return exactOnly ? input in this.j : !!this.go(input);\n  },\n  /**\n   * Short for \"transition all\"; create a transition from the array of items\n   * in the given list to the same final resulting state.\n   * @param {string | string[]} inputs Group of inputs to transition on\n   * @param {Transition<T> | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   */\n  ta(inputs, next, flags, groups) {\n    for (let i = 0; i < inputs.length; i++) {\n      this.tt(inputs[i], next, flags, groups);\n    }\n  },\n  /**\n   * Short for \"take regexp transition\"; defines a transition for this state\n   * when it encounters a token which matches the given regular expression\n   * @param {RegExp} regexp Regular expression transition (populate first)\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  tr(regexp, next, flags, groups) {\n    groups = groups || State.groups;\n    let nextState;\n    if (next && next.j) {\n      nextState = next;\n    } else {\n      // Token with maybe token groups\n      nextState = new State(next);\n      if (flags && groups) {\n        addToGroups(next, flags, groups);\n      }\n    }\n    this.jr.push([regexp, nextState]);\n    return nextState;\n  },\n  /**\n   * Short for \"take transitions\", will take as many sequential transitions as\n   * the length of the given input and returns the\n   * resulting final state.\n   * @param {string | string[]} input\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  ts(input, next, flags, groups) {\n    let state = this;\n    const len = input.length;\n    if (!len) {\n      return state;\n    }\n    for (let i = 0; i < len - 1; i++) {\n      state = state.tt(input[i]);\n    }\n    return state.tt(input[len - 1], next, flags, groups);\n  },\n  /**\n   * Short for \"take transition\", this is a method for building/working with\n   * state machines.\n   *\n   * If a state already exists for the given input, returns it.\n   *\n   * If a token is specified, that state will emit that token when reached by\n   * the linkify engine.\n   *\n   * If no state exists, it will be initialized with some default transitions\n   * that resemble existing default transitions.\n   *\n   * If a state is given for the second argument, that state will be\n   * transitioned to on the given input regardless of what that input\n   * previously did.\n   *\n   * Specify a token group flags to define groups that this token belongs to.\n   * The token will be added to corresponding entires in the given groups\n   * object.\n   *\n   * @param {string} input character, token type to transition on\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of groups\n   * @returns {State<T>} taken after the given input\n   */\n  tt(input, next, flags, groups) {\n    groups = groups || State.groups;\n    const state = this;\n\n    // Check if existing state given, just a basic transition\n    if (next && next.j) {\n      state.j[input] = next;\n      return next;\n    }\n    const t = next;\n\n    // Take the transition with the usual default mechanisms and use that as\n    // a template for creating the next state\n    let nextState,\n      templateState = state.go(input);\n    if (templateState) {\n      nextState = new State();\n      Object.assign(nextState.j, templateState.j);\n      nextState.jr.push.apply(nextState.jr, templateState.jr);\n      nextState.jd = templateState.jd;\n      nextState.t = templateState.t;\n    } else {\n      nextState = new State();\n    }\n    if (t) {\n      // Ensure newly token is in the same groups as the old token\n      if (groups) {\n        if (nextState.t && typeof nextState.t === 'string') {\n          const allFlags = Object.assign(flagsForToken(nextState.t, groups), flags);\n          addToGroups(t, allFlags, groups);\n        } else if (flags) {\n          addToGroups(t, flags, groups);\n        }\n      }\n      nextState.t = t; // overwrite anything that was previously there\n    }\n    state.j[input] = nextState;\n    return nextState;\n  }\n};\n\n// Helper functions to improve minification (not exported outside linkifyjs module)\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ta = (state, input, next, flags, groups) => state.ta(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {RegExp} regexp\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst tr = (state, regexp, next, flags, groups) => state.tr(regexp, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ts = (state, input, next, flags, groups) => state.ts(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string} input\n * @param {T | State<T>} [next]\n * @param {Collections<T>} [groups]\n * @param {Flags} [flags]\n */\nconst tt = (state, input, next, flags, groups) => state.tt(input, next, flags, groups);\n\n/******************************************************************************\nText Tokens\nIdentifiers for token outputs from the regexp scanner\n******************************************************************************/\n\n// A valid web domain token\nconst WORD = 'WORD'; // only contains a-z\nconst UWORD = 'UWORD'; // contains letters other than a-z, used for IDN\nconst ASCIINUMERICAL = 'ASCIINUMERICAL'; // contains a-z, 0-9\nconst ALPHANUMERICAL = 'ALPHANUMERICAL'; // contains numbers and letters other than a-z, used for IDN\n\n// Special case of word\nconst LOCALHOST = 'LOCALHOST';\n\n// Valid top-level domain, special case of WORD (see tlds.js)\nconst TLD = 'TLD';\n\n// Valid IDN TLD, special case of UWORD (see tlds.js)\nconst UTLD = 'UTLD';\n\n// The scheme portion of a web URI protocol. Supported types include: `mailto`,\n// `file`, and user-defined custom protocols. Limited to schemes that contain\n// only letters\nconst SCHEME = 'SCHEME';\n\n// Similar to SCHEME, except makes distinction for schemes that must always be\n// followed by `://`, not just `:`. Supported types include `http`, `https`,\n// `ftp`, `ftps`\nconst SLASH_SCHEME = 'SLASH_SCHEME';\n\n// Any sequence of digits 0-9\nconst NUM = 'NUM';\n\n// Any number of consecutive whitespace characters that are not newline\nconst WS = 'WS';\n\n// New line (unix style)\nconst NL = 'NL'; // \\n\n\n// Opening/closing bracket classes\n// TODO: Rename OPEN -> LEFT and CLOSE -> RIGHT in v5 to fit with Unicode names\n// Also rename angle brackes to LESSTHAN and GREATER THAN\nconst OPENBRACE = 'OPENBRACE'; // {\nconst CLOSEBRACE = 'CLOSEBRACE'; // }\nconst OPENBRACKET = 'OPENBRACKET'; // [\nconst CLOSEBRACKET = 'CLOSEBRACKET'; // ]\nconst OPENPAREN = 'OPENPAREN'; // (\nconst CLOSEPAREN = 'CLOSEPAREN'; // )\nconst OPENANGLEBRACKET = 'OPENANGLEBRACKET'; // <\nconst CLOSEANGLEBRACKET = 'CLOSEANGLEBRACKET'; // >\nconst FULLWIDTHLEFTPAREN = 'FULLWIDTHLEFTPAREN'; // （\nconst FULLWIDTHRIGHTPAREN = 'FULLWIDTHRIGHTPAREN'; // ）\nconst LEFTCORNERBRACKET = 'LEFTCORNERBRACKET'; // 「\nconst RIGHTCORNERBRACKET = 'RIGHTCORNERBRACKET'; // 」\nconst LEFTWHITECORNERBRACKET = 'LEFTWHITECORNERBRACKET'; // 『\nconst RIGHTWHITECORNERBRACKET = 'RIGHTWHITECORNERBRACKET'; // 』\nconst FULLWIDTHLESSTHAN = 'FULLWIDTHLESSTHAN'; // ＜\nconst FULLWIDTHGREATERTHAN = 'FULLWIDTHGREATERTHAN'; // ＞\n\n// Various symbols\nconst AMPERSAND = 'AMPERSAND'; // &\nconst APOSTROPHE = 'APOSTROPHE'; // '\nconst ASTERISK = 'ASTERISK'; // *\nconst AT = 'AT'; // @\nconst BACKSLASH = 'BACKSLASH'; // \\\nconst BACKTICK = 'BACKTICK'; // `\nconst CARET = 'CARET'; // ^\nconst COLON = 'COLON'; // :\nconst COMMA = 'COMMA'; // ,\nconst DOLLAR = 'DOLLAR'; // $\nconst DOT = 'DOT'; // .\nconst EQUALS = 'EQUALS'; // =\nconst EXCLAMATION = 'EXCLAMATION'; // !\nconst HYPHEN = 'HYPHEN'; // -\nconst PERCENT = 'PERCENT'; // %\nconst PIPE = 'PIPE'; // |\nconst PLUS = 'PLUS'; // +\nconst POUND = 'POUND'; // #\nconst QUERY = 'QUERY'; // ?\nconst QUOTE = 'QUOTE'; // \"\nconst FULLWIDTHMIDDLEDOT = 'FULLWIDTHMIDDLEDOT'; // ・\n\nconst SEMI = 'SEMI'; // ;\nconst SLASH = 'SLASH'; // /\nconst TILDE = 'TILDE'; // ~\nconst UNDERSCORE = 'UNDERSCORE'; // _\n\n// Emoji symbol\nconst EMOJI$1 = 'EMOJI';\n\n// Default token - anything that is not one of the above\nconst SYM = 'SYM';\n\nvar tk = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tALPHANUMERICAL: ALPHANUMERICAL,\n\tAMPERSAND: AMPERSAND,\n\tAPOSTROPHE: APOSTROPHE,\n\tASCIINUMERICAL: ASCIINUMERICAL,\n\tASTERISK: ASTERISK,\n\tAT: AT,\n\tBACKSLASH: BACKSLASH,\n\tBACKTICK: BACKTICK,\n\tCARET: CARET,\n\tCLOSEANGLEBRACKET: CLOSEANGLEBRACKET,\n\tCLOSEBRACE: CLOSEBRACE,\n\tCLOSEBRACKET: CLOSEBRACKET,\n\tCLOSEPAREN: CLOSEPAREN,\n\tCOLON: COLON,\n\tCOMMA: COMMA,\n\tDOLLAR: DOLLAR,\n\tDOT: DOT,\n\tEMOJI: EMOJI$1,\n\tEQUALS: EQUALS,\n\tEXCLAMATION: EXCLAMATION,\n\tFULLWIDTHGREATERTHAN: FULLWIDTHGREATERTHAN,\n\tFULLWIDTHLEFTPAREN: FULLWIDTHLEFTPAREN,\n\tFULLWIDTHLESSTHAN: FULLWIDTHLESSTHAN,\n\tFULLWIDTHMIDDLEDOT: FULLWIDTHMIDDLEDOT,\n\tFULLWIDTHRIGHTPAREN: FULLWIDTHRIGHTPAREN,\n\tHYPHEN: HYPHEN,\n\tLEFTCORNERBRACKET: LEFTCORNERBRACKET,\n\tLEFTWHITECORNERBRACKET: LEFTWHITECORNERBRACKET,\n\tLOCALHOST: LOCALHOST,\n\tNL: NL,\n\tNUM: NUM,\n\tOPENANGLEBRACKET: OPENANGLEBRACKET,\n\tOPENBRACE: OPENBRACE,\n\tOPENBRACKET: OPENBRACKET,\n\tOPENPAREN: OPENPAREN,\n\tPERCENT: PERCENT,\n\tPIPE: PIPE,\n\tPLUS: PLUS,\n\tPOUND: POUND,\n\tQUERY: QUERY,\n\tQUOTE: QUOTE,\n\tRIGHTCORNERBRACKET: RIGHTCORNERBRACKET,\n\tRIGHTWHITECORNERBRACKET: RIGHTWHITECORNERBRACKET,\n\tSCHEME: SCHEME,\n\tSEMI: SEMI,\n\tSLASH: SLASH,\n\tSLASH_SCHEME: SLASH_SCHEME,\n\tSYM: SYM,\n\tTILDE: TILDE,\n\tTLD: TLD,\n\tUNDERSCORE: UNDERSCORE,\n\tUTLD: UTLD,\n\tUWORD: UWORD,\n\tWORD: WORD,\n\tWS: WS\n});\n\n// Note that these two Unicode ones expand into a really big one with Babel\nconst ASCII_LETTER = /[a-z]/;\nconst LETTER = /\\p{L}/u; // Any Unicode character with letter data type\nconst EMOJI = /\\p{Emoji}/u; // Any Unicode emoji character\nconst EMOJI_VARIATION$1 = /\\ufe0f/;\nconst DIGIT = /\\d/;\nconst SPACE = /\\s/;\n\nvar regexp = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tASCII_LETTER: ASCII_LETTER,\n\tDIGIT: DIGIT,\n\tEMOJI: EMOJI,\n\tEMOJI_VARIATION: EMOJI_VARIATION$1,\n\tLETTER: LETTER,\n\tSPACE: SPACE\n});\n\n/**\n\tThe scanner provides an interface that takes a string of text as input, and\n\toutputs an array of tokens instances that can be used for easy URL parsing.\n*/\n\nconst CR = '\\r'; // carriage-return character\nconst LF = '\\n'; // line-feed character\nconst EMOJI_VARIATION = '\\ufe0f'; // Variation selector, follows heart and others\nconst EMOJI_JOINER = '\\u200d'; // zero-width joiner\nconst OBJECT_REPLACEMENT = '\\ufffc'; // whitespace placeholder that sometimes appears in rich text editors\n\nlet tlds = null,\n  utlds = null; // don't change so only have to be computed once\n\n/**\n * Scanner output token:\n * - `t` is the token name (e.g., 'NUM', 'EMOJI', 'TLD')\n * - `v` is the value of the token (e.g., '123', '❤️', 'com')\n * - `s` is the start index of the token in the original string\n * - `e` is the end index of the token in the original string\n * @typedef {{t: string, v: string, s: number, e: number}} Token\n */\n\n/**\n * @template T\n * @typedef {{ [collection: string]: T[] }} Collections\n */\n\n/**\n * Initialize the scanner character-based state machine for the given start\n * state\n * @param {[string, boolean][]} customSchemes List of custom schemes, where each\n * item is a length-2 tuple with the first element set to the string scheme, and\n * the second element set to `true` if the `://` after the scheme is optional\n */\nfunction init$2(customSchemes = []) {\n  // Frequently used states (name argument removed during minification)\n  /** @type Collections<string> */\n  const groups = {}; // of tokens\n  State.groups = groups;\n  /** @type State<string> */\n  const Start = new State();\n  if (tlds == null) {\n    tlds = decodeTlds(encodedTlds);\n  }\n  if (utlds == null) {\n    utlds = decodeTlds(encodedUtlds);\n  }\n\n  // States for special URL symbols that accept immediately after start\n  tt(Start, \"'\", APOSTROPHE);\n  tt(Start, '{', OPENBRACE);\n  tt(Start, '}', CLOSEBRACE);\n  tt(Start, '[', OPENBRACKET);\n  tt(Start, ']', CLOSEBRACKET);\n  tt(Start, '(', OPENPAREN);\n  tt(Start, ')', CLOSEPAREN);\n  tt(Start, '<', OPENANGLEBRACKET);\n  tt(Start, '>', CLOSEANGLEBRACKET);\n  tt(Start, '（', FULLWIDTHLEFTPAREN);\n  tt(Start, '）', FULLWIDTHRIGHTPAREN);\n  tt(Start, '「', LEFTCORNERBRACKET);\n  tt(Start, '」', RIGHTCORNERBRACKET);\n  tt(Start, '『', LEFTWHITECORNERBRACKET);\n  tt(Start, '』', RIGHTWHITECORNERBRACKET);\n  tt(Start, '＜', FULLWIDTHLESSTHAN);\n  tt(Start, '＞', FULLWIDTHGREATERTHAN);\n  tt(Start, '&', AMPERSAND);\n  tt(Start, '*', ASTERISK);\n  tt(Start, '@', AT);\n  tt(Start, '`', BACKTICK);\n  tt(Start, '^', CARET);\n  tt(Start, ':', COLON);\n  tt(Start, ',', COMMA);\n  tt(Start, '$', DOLLAR);\n  tt(Start, '.', DOT);\n  tt(Start, '=', EQUALS);\n  tt(Start, '!', EXCLAMATION);\n  tt(Start, '-', HYPHEN);\n  tt(Start, '%', PERCENT);\n  tt(Start, '|', PIPE);\n  tt(Start, '+', PLUS);\n  tt(Start, '#', POUND);\n  tt(Start, '?', QUERY);\n  tt(Start, '\"', QUOTE);\n  tt(Start, '/', SLASH);\n  tt(Start, ';', SEMI);\n  tt(Start, '~', TILDE);\n  tt(Start, '_', UNDERSCORE);\n  tt(Start, '\\\\', BACKSLASH);\n  tt(Start, '・', FULLWIDTHMIDDLEDOT);\n  const Num = tr(Start, DIGIT, NUM, {\n    [numeric]: true\n  });\n  tr(Num, DIGIT, Num);\n  const Asciinumeric = tr(Num, ASCII_LETTER, ASCIINUMERICAL, {\n    [asciinumeric]: true\n  });\n  const Alphanumeric = tr(Num, LETTER, ALPHANUMERICAL, {\n    [alphanumeric]: true\n  });\n\n  // State which emits a word token\n  const Word = tr(Start, ASCII_LETTER, WORD, {\n    [ascii]: true\n  });\n  tr(Word, DIGIT, Asciinumeric);\n  tr(Word, ASCII_LETTER, Word);\n  tr(Asciinumeric, DIGIT, Asciinumeric);\n  tr(Asciinumeric, ASCII_LETTER, Asciinumeric);\n\n  // Same as previous, but specific to non-fsm.ascii alphabet words\n  const UWord = tr(Start, LETTER, UWORD, {\n    [alpha]: true\n  });\n  tr(UWord, ASCII_LETTER); // Non-accepting\n  tr(UWord, DIGIT, Alphanumeric);\n  tr(UWord, LETTER, UWord);\n  tr(Alphanumeric, DIGIT, Alphanumeric);\n  tr(Alphanumeric, ASCII_LETTER); // Non-accepting\n  tr(Alphanumeric, LETTER, Alphanumeric); // Non-accepting\n\n  // Whitespace jumps\n  // Tokens of only non-newline whitespace are arbitrarily long\n  // If any whitespace except newline, more whitespace!\n  const Nl = tt(Start, LF, NL, {\n    [whitespace]: true\n  });\n  const Cr = tt(Start, CR, WS, {\n    [whitespace]: true\n  });\n  const Ws = tr(Start, SPACE, WS, {\n    [whitespace]: true\n  });\n  tt(Start, OBJECT_REPLACEMENT, Ws);\n  tt(Cr, LF, Nl); // \\r\\n\n  tt(Cr, OBJECT_REPLACEMENT, Ws);\n  tr(Cr, SPACE, Ws);\n  tt(Ws, CR); // non-accepting state to avoid mixing whitespaces\n  tt(Ws, LF); // non-accepting state to avoid mixing whitespaces\n  tr(Ws, SPACE, Ws);\n  tt(Ws, OBJECT_REPLACEMENT, Ws);\n\n  // Emoji tokens. They are not grouped by the scanner except in cases where a\n  // zero-width joiner is present\n  const Emoji = tr(Start, EMOJI, EMOJI$1, {\n    [emoji]: true\n  });\n  tt(Emoji, '#'); // no transition, emoji regex seems to match #\n  tr(Emoji, EMOJI, Emoji);\n  tt(Emoji, EMOJI_VARIATION, Emoji);\n  // tt(Start, EMOJI_VARIATION, Emoji); // This one is sketchy\n\n  const EmojiJoiner = tt(Emoji, EMOJI_JOINER);\n  tt(EmojiJoiner, '#');\n  tr(EmojiJoiner, EMOJI, Emoji);\n  // tt(EmojiJoiner, EMOJI_VARIATION, Emoji); // also sketchy\n\n  // Generates states for top-level domains\n  // Note that this is most accurate when tlds are in alphabetical order\n  const wordjr = [[ASCII_LETTER, Word], [DIGIT, Asciinumeric]];\n  const uwordjr = [[ASCII_LETTER, null], [LETTER, UWord], [DIGIT, Alphanumeric]];\n  for (let i = 0; i < tlds.length; i++) {\n    fastts(Start, tlds[i], TLD, WORD, wordjr);\n  }\n  for (let i = 0; i < utlds.length; i++) {\n    fastts(Start, utlds[i], UTLD, UWORD, uwordjr);\n  }\n  addToGroups(TLD, {\n    tld: true,\n    ascii: true\n  }, groups);\n  addToGroups(UTLD, {\n    utld: true,\n    alpha: true\n  }, groups);\n\n  // Collect the states generated by different protocols. NOTE: If any new TLDs\n  // get added that are also protocols, set the token to be the same as the\n  // protocol to ensure parsing works as expected.\n  fastts(Start, 'file', SCHEME, WORD, wordjr);\n  fastts(Start, 'mailto', SCHEME, WORD, wordjr);\n  fastts(Start, 'http', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'https', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftp', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftps', SLASH_SCHEME, WORD, wordjr);\n  addToGroups(SCHEME, {\n    scheme: true,\n    ascii: true\n  }, groups);\n  addToGroups(SLASH_SCHEME, {\n    slashscheme: true,\n    ascii: true\n  }, groups);\n\n  // Register custom schemes. Assumes each scheme is asciinumeric with hyphens\n  customSchemes = customSchemes.sort((a, b) => a[0] > b[0] ? 1 : -1);\n  for (let i = 0; i < customSchemes.length; i++) {\n    const sch = customSchemes[i][0];\n    const optionalSlashSlash = customSchemes[i][1];\n    const flags = optionalSlashSlash ? {\n      [scheme]: true\n    } : {\n      [slashscheme]: true\n    };\n    if (sch.indexOf('-') >= 0) {\n      flags[domain] = true;\n    } else if (!ASCII_LETTER.test(sch)) {\n      flags[numeric] = true; // numbers only\n    } else if (DIGIT.test(sch)) {\n      flags[asciinumeric] = true;\n    } else {\n      flags[ascii] = true;\n    }\n    ts(Start, sch, sch, flags);\n  }\n\n  // Localhost token\n  ts(Start, 'localhost', LOCALHOST, {\n    ascii: true\n  });\n\n  // Set default transition for start state (some symbol)\n  Start.jd = new State(SYM);\n  return {\n    start: Start,\n    tokens: Object.assign({\n      groups\n    }, tk)\n  };\n}\n\n/**\n\tGiven a string, returns an array of TOKEN instances representing the\n\tcomposition of that string.\n\n\t@method run\n\t@param {State<string>} start scanner starting state\n\t@param {string} str input string to scan\n\t@return {Token[]} list of tokens, each with a type and value\n*/\nfunction run$1(start, str) {\n  // State machine is not case sensitive, so input is tokenized in lowercased\n  // form (still returns regular case). Uses selective `toLowerCase` because\n  // lowercasing the entire string causes the length and character position to\n  // vary in some non-English strings with V8-based runtimes.\n  const iterable = stringToArray(str.replace(/[A-Z]/g, c => c.toLowerCase()));\n  const charCount = iterable.length; // <= len if there are emojis, etc\n  const tokens = []; // return value\n\n  // cursor through the string itself, accounting for characters that have\n  // width with length 2 such as emojis\n  let cursor = 0;\n\n  // Cursor through the array-representation of the string\n  let charCursor = 0;\n\n  // Tokenize the string\n  while (charCursor < charCount) {\n    let state = start;\n    let nextState = null;\n    let tokenLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    let charsSinceAccepts = -1;\n    while (charCursor < charCount && (nextState = state.go(iterable[charCursor]))) {\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        charsSinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts += iterable[charCursor].length;\n        charsSinceAccepts++;\n      }\n      tokenLength += iterable[charCursor].length;\n      cursor += iterable[charCursor].length;\n      charCursor++;\n    }\n\n    // Roll back to the latest accepting state\n    cursor -= sinceAccepts;\n    charCursor -= charsSinceAccepts;\n    tokenLength -= sinceAccepts;\n\n    // No more jumps, just make a new token from the last accepting one\n    tokens.push({\n      t: latestAccepting.t,\n      // token type/name\n      v: str.slice(cursor - tokenLength, cursor),\n      // string value\n      s: cursor - tokenLength,\n      // start index\n      e: cursor // end index (excluding)\n    });\n  }\n  return tokens;\n}\n\n/**\n * Convert a String to an Array of characters, taking into account that some\n * characters like emojis take up two string indexes.\n *\n * Adapted from core-js (MIT license)\n * https://github.com/zloirock/core-js/blob/2d69cf5f99ab3ea3463c395df81e5a15b68f49d9/packages/core-js/internals/string-multibyte.js\n *\n * @function stringToArray\n * @param {string} str\n * @returns {string[]}\n */\nfunction stringToArray(str) {\n  const result = [];\n  const len = str.length;\n  let index = 0;\n  while (index < len) {\n    let first = str.charCodeAt(index);\n    let second;\n    let char = first < 0xd800 || first > 0xdbff || index + 1 === len || (second = str.charCodeAt(index + 1)) < 0xdc00 || second > 0xdfff ? str[index] // single character\n    : str.slice(index, index + 2); // two-index characters\n    result.push(char);\n    index += char.length;\n  }\n  return result;\n}\n\n/**\n * Fast version of ts function for when transition defaults are well known\n * @param {State<string>} state\n * @param {string} input\n * @param {string} t\n * @param {string} defaultt\n * @param {[RegExp, State<string>][]} jr\n * @returns {State<string>}\n */\nfunction fastts(state, input, t, defaultt, jr) {\n  let next;\n  const len = input.length;\n  for (let i = 0; i < len - 1; i++) {\n    const char = input[i];\n    if (state.j[char]) {\n      next = state.j[char];\n    } else {\n      next = new State(defaultt);\n      next.jr = jr.slice();\n      state.j[char] = next;\n    }\n    state = next;\n  }\n  next = new State(t);\n  next.jr = jr.slice();\n  state.j[input[len - 1]] = next;\n  return next;\n}\n\n/**\n * Converts a string of Top-Level Domain names encoded in update-tlds.js back\n * into a list of strings.\n * @param {str} encoded encoded TLDs string\n * @returns {str[]} original TLDs list\n */\nfunction decodeTlds(encoded) {\n  const words = [];\n  const stack = [];\n  let i = 0;\n  let digits = '0123456789';\n  while (i < encoded.length) {\n    let popDigitCount = 0;\n    while (digits.indexOf(encoded[i + popDigitCount]) >= 0) {\n      popDigitCount++; // encountered some digits, have to pop to go one level up trie\n    }\n    if (popDigitCount > 0) {\n      words.push(stack.join('')); // whatever preceded the pop digits must be a word\n      for (let popCount = parseInt(encoded.substring(i, i + popDigitCount), 10); popCount > 0; popCount--) {\n        stack.pop();\n      }\n      i += popDigitCount;\n    } else {\n      stack.push(encoded[i]); // drop down a level into the trie\n      i++;\n    }\n  }\n  return words;\n}\n\n/**\n * An object where each key is a valid DOM Event Name such as `click` or `focus`\n * and each value is an event handler function.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Element#events\n * @typedef {?{ [event: string]: Function }} EventListeners\n */\n\n/**\n * All formatted properties required to render a link, including `tagName`,\n * `attributes`, `content` and `eventListeners`.\n * @typedef {{ tagName: any, attributes: {[attr: string]: any}, content: string,\n * eventListeners: EventListeners }} IntermediateRepresentation\n */\n\n/**\n * Specify either an object described by the template type `O` or a function.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `O`\n * @template O\n * @typedef {O | ((value: string, type: string, token: MultiToken) => O)} OptObj\n */\n\n/**\n * Specify either a function described by template type `F` or an object.\n *\n * Each key in the object should be a link type (`'url'`, `'hashtag`', etc.). Each\n * value should be a function with template type `F` that is called when the\n * corresponding link type is encountered.\n * @template F\n * @typedef {F | { [type: string]: F}} OptFn\n */\n\n/**\n * Specify either a value with template type `V`, a function that returns `V` or\n * an object where each value resolves to `V`.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `V`\n *\n * For the object, each key should be a link type (`'url'`, `'hashtag`', etc.).\n * Each value should either have type `V` or a function that returns V. This\n * function similarly takes a string value and a token.\n *\n * Example valid types for `Opt<string>`:\n *\n * ```js\n * 'hello'\n * (value, type, token) => 'world'\n * { url: 'hello', email: (value, token) => 'world'}\n * ```\n * @template V\n * @typedef {V | ((value: string, type: string, token: MultiToken) => V) | { [type: string]: V | ((value: string, token: MultiToken) => V) }} Opt\n */\n\n/**\n * See available options: https://linkify.js.org/docs/options.html\n * @typedef {{\n * \tdefaultProtocol?: string,\n *  events?: OptObj<EventListeners>,\n * \tformat?: Opt<string>,\n * \tformatHref?: Opt<string>,\n * \tnl2br?: boolean,\n * \ttagName?: Opt<any>,\n * \ttarget?: Opt<string>,\n * \trel?: Opt<string>,\n * \tvalidate?: Opt<boolean>,\n * \ttruncate?: Opt<number>,\n * \tclassName?: Opt<string>,\n * \tattributes?: OptObj<({ [attr: string]: any })>,\n *  ignoreTags?: string[],\n * \trender?: OptFn<((ir: IntermediateRepresentation) => any)>\n * }} Opts\n */\n\n/**\n * @type Required<Opts>\n */\nconst defaults = {\n  defaultProtocol: 'http',\n  events: null,\n  format: noop,\n  formatHref: noop,\n  nl2br: false,\n  tagName: 'a',\n  target: null,\n  rel: null,\n  validate: true,\n  truncate: Infinity,\n  className: null,\n  attributes: null,\n  ignoreTags: [],\n  render: null\n};\n\n/**\n * Utility class for linkify interfaces to apply specified\n * {@link Opts formatting and rendering options}.\n *\n * @param {Opts | Options} [opts] Option value overrides.\n * @param {(ir: IntermediateRepresentation) => any} [defaultRender] (For\n *   internal use) default render function that determines how to generate an\n *   HTML element based on a link token's derived tagName, attributes and HTML.\n *   Similar to render option\n */\nfunction Options(opts, defaultRender = null) {\n  let o = Object.assign({}, defaults);\n  if (opts) {\n    o = Object.assign(o, opts instanceof Options ? opts.o : opts);\n  }\n\n  // Ensure all ignored tags are uppercase\n  const ignoredTags = o.ignoreTags;\n  const uppercaseIgnoredTags = [];\n  for (let i = 0; i < ignoredTags.length; i++) {\n    uppercaseIgnoredTags.push(ignoredTags[i].toUpperCase());\n  }\n  /** @protected */\n  this.o = o;\n  if (defaultRender) {\n    this.defaultRender = defaultRender;\n  }\n  this.ignoreTags = uppercaseIgnoredTags;\n}\nOptions.prototype = {\n  o: defaults,\n  /**\n   * @type string[]\n   */\n  ignoreTags: [],\n  /**\n   * @param {IntermediateRepresentation} ir\n   * @returns {any}\n   */\n  defaultRender(ir) {\n    return ir;\n  },\n  /**\n   * Returns true or false based on whether a token should be displayed as a\n   * link based on the user options.\n   * @param {MultiToken} token\n   * @returns {boolean}\n   */\n  check(token) {\n    return this.get('validate', token.toString(), token);\n  },\n  // Private methods\n\n  /**\n   * Resolve an option's value based on the value of the option and the given\n   * params. If operator and token are specified and the target option is\n   * callable, automatically calls the function with the given argument.\n   * @template {keyof Opts} K\n   * @param {K} key Name of option to use\n   * @param {string} [operator] will be passed to the target option if it's a\n   * function. If not specified, RAW function value gets returned\n   * @param {MultiToken} [token] The token from linkify.tokenize\n   * @returns {Opts[K] | any}\n   */\n  get(key, operator, token) {\n    const isCallable = operator != null;\n    let option = this.o[key];\n    if (!option) {\n      return option;\n    }\n    if (typeof option === 'object') {\n      option = token.t in option ? option[token.t] : defaults[key];\n      if (typeof option === 'function' && isCallable) {\n        option = option(operator, token);\n      }\n    } else if (typeof option === 'function' && isCallable) {\n      option = option(operator, token.t, token);\n    }\n    return option;\n  },\n  /**\n   * @template {keyof Opts} L\n   * @param {L} key Name of options object to use\n   * @param {string} [operator]\n   * @param {MultiToken} [token]\n   * @returns {Opts[L] | any}\n   */\n  getObj(key, operator, token) {\n    let obj = this.o[key];\n    if (typeof obj === 'function' && operator != null) {\n      obj = obj(operator, token.t, token);\n    }\n    return obj;\n  },\n  /**\n   * Convert the given token to a rendered element that may be added to the\n   * calling-interface's DOM\n   * @param {MultiToken} token Token to render to an HTML element\n   * @returns {any} Render result; e.g., HTML string, DOM element, React\n   *   Component, etc.\n   */\n  render(token) {\n    const ir = token.render(this); // intermediate representation\n    const renderFn = this.get('render', null, token) || this.defaultRender;\n    return renderFn(ir, token.t, token);\n  }\n};\nfunction noop(val) {\n  return val;\n}\n\nvar options = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tOptions: Options,\n\tdefaults: defaults\n});\n\n/******************************************************************************\n\tMulti-Tokens\n\tTokens composed of arrays of TextTokens\n******************************************************************************/\n\n/**\n * @param {string} value\n * @param {Token[]} tokens\n */\nfunction MultiToken(value, tokens) {\n  this.t = 'token';\n  this.v = value;\n  this.tk = tokens;\n}\n\n/**\n * Abstract class used for manufacturing tokens of text tokens. That is rather\n * than the value for a token being a small string of text, it's value an array\n * of text tokens.\n *\n * Used for grouping together URLs, emails, hashtags, and other potential\n * creations.\n * @class MultiToken\n * @property {string} t\n * @property {string} v\n * @property {Token[]} tk\n * @abstract\n */\nMultiToken.prototype = {\n  isLink: false,\n  /**\n   * Return the string this token represents.\n   * @return {string}\n   */\n  toString() {\n    return this.v;\n  },\n  /**\n   * What should the value for this token be in the `href` HTML attribute?\n   * Returns the `.toString` value by default.\n   * @param {string} [scheme]\n   * @return {string}\n   */\n  toHref(scheme) {\n    return this.toString();\n  },\n  /**\n   * @param {Options} options Formatting options\n   * @returns {string}\n   */\n  toFormattedString(options) {\n    const val = this.toString();\n    const truncate = options.get('truncate', val, this);\n    const formatted = options.get('format', val, this);\n    return truncate && formatted.length > truncate ? formatted.substring(0, truncate) + '…' : formatted;\n  },\n  /**\n   *\n   * @param {Options} options\n   * @returns {string}\n   */\n  toFormattedHref(options) {\n    return options.get('formatHref', this.toHref(options.get('defaultProtocol')), this);\n  },\n  /**\n   * The start index of this token in the original input string\n   * @returns {number}\n   */\n  startIndex() {\n    return this.tk[0].s;\n  },\n  /**\n   * The end index of this token in the original input string (up to this\n   * index but not including it)\n   * @returns {number}\n   */\n  endIndex() {\n    return this.tk[this.tk.length - 1].e;\n  },\n  /**\n  \tReturns an object  of relevant values for this token, which includes keys\n  \t* type - Kind of token ('url', 'email', etc.)\n  \t* value - Original text\n  \t* href - The value that should be added to the anchor tag's href\n  \t\tattribute\n  \t\t@method toObject\n  \t@param {string} [protocol] `'http'` by default\n  */\n  toObject(protocol = defaults.defaultProtocol) {\n    return {\n      type: this.t,\n      value: this.toString(),\n      isLink: this.isLink,\n      href: this.toHref(protocol),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   *\n   * @param {Options} options Formatting option\n   */\n  toFormattedObject(options) {\n    return {\n      type: this.t,\n      value: this.toFormattedString(options),\n      isLink: this.isLink,\n      href: this.toFormattedHref(options),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   * Whether this token should be rendered as a link according to the given options\n   * @param {Options} options\n   * @returns {boolean}\n   */\n  validate(options) {\n    return options.get('validate', this.toString(), this);\n  },\n  /**\n   * Return an object that represents how this link should be rendered.\n   * @param {Options} options Formattinng options\n   */\n  render(options) {\n    const token = this;\n    const href = this.toHref(options.get('defaultProtocol'));\n    const formattedHref = options.get('formatHref', href, this);\n    const tagName = options.get('tagName', href, token);\n    const content = this.toFormattedString(options);\n    const attributes = {};\n    const className = options.get('className', href, token);\n    const target = options.get('target', href, token);\n    const rel = options.get('rel', href, token);\n    const attrs = options.getObj('attributes', href, token);\n    const eventListeners = options.getObj('events', href, token);\n    attributes.href = formattedHref;\n    if (className) {\n      attributes.class = className;\n    }\n    if (target) {\n      attributes.target = target;\n    }\n    if (rel) {\n      attributes.rel = rel;\n    }\n    if (attrs) {\n      Object.assign(attributes, attrs);\n    }\n    return {\n      tagName,\n      attributes,\n      content,\n      eventListeners\n    };\n  }\n};\n\n/**\n * Create a new token that can be emitted by the parser state machine\n * @param {string} type readable type of the token\n * @param {object} props properties to assign or override, including isLink = true or false\n * @returns {new (value: string, tokens: Token[]) => MultiToken} new token class\n */\nfunction createTokenClass(type, props) {\n  class Token extends MultiToken {\n    constructor(value, tokens) {\n      super(value, tokens);\n      this.t = type;\n    }\n  }\n  for (const p in props) {\n    Token.prototype[p] = props[p];\n  }\n  Token.t = type;\n  return Token;\n}\n\n/**\n\tRepresents a list of tokens making up a valid email address\n*/\nconst Email = createTokenClass('email', {\n  isLink: true,\n  toHref() {\n    return 'mailto:' + this.toString();\n  }\n});\n\n/**\n\tRepresents some plain text\n*/\nconst Text = createTokenClass('text');\n\n/**\n\tMulti-linebreak token - represents a line break\n\t@class Nl\n*/\nconst Nl = createTokenClass('nl');\n\n/**\n\tRepresents a list of text tokens making up a valid URL\n\t@class Url\n*/\nconst Url = createTokenClass('url', {\n  isLink: true,\n  /**\n  \tLowercases relevant parts of the domain and adds the protocol if\n  \trequired. Note that this will not escape unsafe HTML characters in the\n  \tURL.\n  \t\t@param {string} [scheme] default scheme (e.g., 'https')\n  \t@return {string} the full href\n  */\n  toHref(scheme = defaults.defaultProtocol) {\n    // Check if already has a prefix scheme\n    return this.hasProtocol() ? this.v : `${scheme}://${this.v}`;\n  },\n  /**\n   * Check whether this URL token has a protocol\n   * @return {boolean}\n   */\n  hasProtocol() {\n    const tokens = this.tk;\n    return tokens.length >= 2 && tokens[0].t !== LOCALHOST && tokens[1].t === COLON;\n  }\n});\n\nvar multi = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tBase: MultiToken,\n\tEmail: Email,\n\tMultiToken: MultiToken,\n\tNl: Nl,\n\tText: Text,\n\tUrl: Url,\n\tcreateTokenClass: createTokenClass\n});\n\n/**\n\tNot exactly parser, more like the second-stage scanner (although we can\n\ttheoretically hotswap the code here with a real parser in the future... but\n\tfor a little URL-finding utility abstract syntax trees may be a little\n\toverkill).\n\n\tURL format: http://en.wikipedia.org/wiki/URI_scheme\n\tEmail format: http://en.wikipedia.org/wiki/EmailAddress (links to RFC in\n\treference)\n\n\t@module linkify\n\t@submodule parser\n\t@main run\n*/\n\nconst makeState = arg => new State(arg);\n\n/**\n * Generate the parser multi token-based state machine\n * @param {{ groups: Collections<string> }} tokens\n */\nfunction init$1({\n  groups\n}) {\n  // Types of characters the URL can definitely end in\n  const qsAccepting = groups.domain.concat([AMPERSAND, ASTERISK, AT, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, NUM, PERCENT, PIPE, PLUS, POUND, SLASH, SYM, TILDE, UNDERSCORE]);\n\n  // Types of tokens that can follow a URL and be part of the query string\n  // but cannot be the very last characters\n  // Characters that cannot appear in the URL at all should be excluded\n  const qsNonAccepting = [APOSTROPHE, COLON, COMMA, DOT, EXCLAMATION, PERCENT, QUERY, QUOTE, SEMI, OPENANGLEBRACKET, CLOSEANGLEBRACKET, OPENBRACE, CLOSEBRACE, CLOSEBRACKET, OPENBRACKET, OPENPAREN, CLOSEPAREN, FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN, LEFTCORNERBRACKET, RIGHTCORNERBRACKET, LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET, FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN];\n\n  // For addresses without the mailto prefix\n  // Tokens allowed in the localpart of the email\n  const localpartAccepting = [AMPERSAND, APOSTROPHE, ASTERISK, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, OPENBRACE, CLOSEBRACE, PERCENT, PIPE, PLUS, POUND, QUERY, SLASH, SYM, TILDE, UNDERSCORE];\n\n  // The universal starting state.\n  /**\n   * @type State<Token>\n   */\n  const Start = makeState();\n  const Localpart = tt(Start, TILDE); // Local part of the email address\n  ta(Localpart, localpartAccepting, Localpart);\n  ta(Localpart, groups.domain, Localpart);\n  const Domain = makeState(),\n    Scheme = makeState(),\n    SlashScheme = makeState();\n  ta(Start, groups.domain, Domain); // parsed string ends with a potential domain name (A)\n  ta(Start, groups.scheme, Scheme); // e.g., 'mailto'\n  ta(Start, groups.slashscheme, SlashScheme); // e.g., 'http'\n\n  ta(Domain, localpartAccepting, Localpart);\n  ta(Domain, groups.domain, Domain);\n  const LocalpartAt = tt(Domain, AT); // Local part of the email address plus @\n\n  tt(Localpart, AT, LocalpartAt); // close to an email address now\n\n  // Local part of an email address can be e.g. 'http' or 'mailto'\n  tt(Scheme, AT, LocalpartAt);\n  tt(SlashScheme, AT, LocalpartAt);\n  const LocalpartDot = tt(Localpart, DOT); // Local part of the email address plus '.' (localpart cannot end in .)\n  ta(LocalpartDot, localpartAccepting, Localpart);\n  ta(LocalpartDot, groups.domain, Localpart);\n  const EmailDomain = makeState();\n  ta(LocalpartAt, groups.domain, EmailDomain); // parsed string starts with local email info + @ with a potential domain name\n  ta(EmailDomain, groups.domain, EmailDomain);\n  const EmailDomainDot = tt(EmailDomain, DOT); // domain followed by DOT\n  ta(EmailDomainDot, groups.domain, EmailDomain);\n  const Email$1 = makeState(Email); // Possible email address (could have more tlds)\n  ta(EmailDomainDot, groups.tld, Email$1);\n  ta(EmailDomainDot, groups.utld, Email$1);\n  tt(LocalpartAt, LOCALHOST, Email$1);\n\n  // Hyphen can jump back to a domain name\n  const EmailDomainHyphen = tt(EmailDomain, HYPHEN); // parsed string starts with local email info + @ with a potential domain name\n  tt(EmailDomainHyphen, HYPHEN, EmailDomainHyphen);\n  ta(EmailDomainHyphen, groups.domain, EmailDomain);\n  ta(Email$1, groups.domain, EmailDomain);\n  tt(Email$1, DOT, EmailDomainDot);\n  tt(Email$1, HYPHEN, EmailDomainHyphen);\n\n  // Final possible email states\n  const EmailColon = tt(Email$1, COLON); // URL followed by colon (potential port number here)\n  /*const EmailColonPort = */\n  ta(EmailColon, groups.numeric, Email); // URL followed by colon and port number\n\n  // Account for dots and hyphens. Hyphens are usually parts of domain names\n  // (but not TLDs)\n  const DomainHyphen = tt(Domain, HYPHEN); // domain followed by hyphen\n  const DomainDot = tt(Domain, DOT); // domain followed by DOT\n  tt(DomainHyphen, HYPHEN, DomainHyphen);\n  ta(DomainHyphen, groups.domain, Domain);\n  ta(DomainDot, localpartAccepting, Localpart);\n  ta(DomainDot, groups.domain, Domain);\n  const DomainDotTld = makeState(Url); // Simplest possible URL with no query string\n  ta(DomainDot, groups.tld, DomainDotTld);\n  ta(DomainDot, groups.utld, DomainDotTld);\n  ta(DomainDotTld, groups.domain, Domain);\n  ta(DomainDotTld, localpartAccepting, Localpart);\n  tt(DomainDotTld, DOT, DomainDot);\n  tt(DomainDotTld, HYPHEN, DomainHyphen);\n  tt(DomainDotTld, AT, LocalpartAt);\n  const DomainDotTldColon = tt(DomainDotTld, COLON); // URL followed by colon (potential port number here)\n  const DomainDotTldColonPort = makeState(Url); // TLD followed by a port number\n  ta(DomainDotTldColon, groups.numeric, DomainDotTldColonPort);\n\n  // Long URL with optional port and maybe query string\n  const Url$1 = makeState(Url);\n\n  // URL with extra symbols at the end, followed by an opening bracket\n  const UrlNonaccept = makeState(); // URL followed by some symbols (will not be part of the final URL)\n\n  // Query strings\n  ta(Url$1, qsAccepting, Url$1);\n  ta(Url$1, qsNonAccepting, UrlNonaccept);\n  ta(UrlNonaccept, qsAccepting, Url$1);\n  ta(UrlNonaccept, qsNonAccepting, UrlNonaccept);\n\n  // Become real URLs after `SLASH` or `COLON NUM SLASH`\n  // Here works with or without scheme:// prefix\n  tt(DomainDotTld, SLASH, Url$1);\n  tt(DomainDotTldColonPort, SLASH, Url$1);\n\n  // Note that domains that begin with schemes are treated slighly differently\n  const SchemeColon = tt(Scheme, COLON); // e.g., 'mailto:'\n  const SlashSchemeColon = tt(SlashScheme, COLON); // e.g., 'http:'\n  const SlashSchemeColonSlash = tt(SlashSchemeColon, SLASH); // e.g., 'http:/'\n\n  const UriPrefix = tt(SlashSchemeColonSlash, SLASH); // e.g., 'http://'\n\n  // Scheme states can transition to domain states\n  ta(Scheme, groups.domain, Domain);\n  tt(Scheme, DOT, DomainDot);\n  tt(Scheme, HYPHEN, DomainHyphen);\n  ta(SlashScheme, groups.domain, Domain);\n  tt(SlashScheme, DOT, DomainDot);\n  tt(SlashScheme, HYPHEN, DomainHyphen);\n\n  // Force URL with scheme prefix followed by anything sane\n  ta(SchemeColon, groups.domain, Url$1);\n  tt(SchemeColon, SLASH, Url$1);\n  tt(SchemeColon, QUERY, Url$1);\n  ta(UriPrefix, groups.domain, Url$1);\n  ta(UriPrefix, qsAccepting, Url$1);\n  tt(UriPrefix, SLASH, Url$1);\n  const bracketPairs = [[OPENBRACE, CLOSEBRACE],\n  // {}\n  [OPENBRACKET, CLOSEBRACKET],\n  // []\n  [OPENPAREN, CLOSEPAREN],\n  // ()\n  [OPENANGLEBRACKET, CLOSEANGLEBRACKET],\n  // <>\n  [FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN],\n  // （）\n  [LEFTCORNERBRACKET, RIGHTCORNERBRACKET],\n  // 「」\n  [LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET],\n  // 『』\n  [FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN] // ＜＞\n  ];\n  for (let i = 0; i < bracketPairs.length; i++) {\n    const [OPEN, CLOSE] = bracketPairs[i];\n    const UrlOpen = tt(Url$1, OPEN); // URL followed by open bracket\n\n    // Continue not accepting for open brackets\n    tt(UrlNonaccept, OPEN, UrlOpen);\n\n    // Closing bracket component. This character WILL be included in the URL\n    tt(UrlOpen, CLOSE, Url$1);\n\n    // URL that beings with an opening bracket, followed by a symbols.\n    // Note that the final state can still be `UrlOpen` (if the URL has a\n    // single opening bracket for some reason).\n    const UrlOpenQ = makeState(Url);\n    ta(UrlOpen, qsAccepting, UrlOpenQ);\n    const UrlOpenSyms = makeState(); // UrlOpen followed by some symbols it cannot end it\n    ta(UrlOpen, qsNonAccepting);\n\n    // URL that begins with an opening bracket, followed by some symbols\n    ta(UrlOpenQ, qsAccepting, UrlOpenQ);\n    ta(UrlOpenQ, qsNonAccepting, UrlOpenSyms);\n    ta(UrlOpenSyms, qsAccepting, UrlOpenQ);\n    ta(UrlOpenSyms, qsNonAccepting, UrlOpenSyms);\n\n    // Close brace/bracket to become regular URL\n    tt(UrlOpenQ, CLOSE, Url$1);\n    tt(UrlOpenSyms, CLOSE, Url$1);\n  }\n  tt(Start, LOCALHOST, DomainDotTld); // localhost is a valid URL state\n  tt(Start, NL, Nl); // single new line\n\n  return {\n    start: Start,\n    tokens: tk\n  };\n}\n\n/**\n * Run the parser state machine on a list of scanned string-based tokens to\n * create a list of multi tokens, each of which represents a URL, email address,\n * plain text, etc.\n *\n * @param {State<MultiToken>} start parser start state\n * @param {string} input the original input used to generate the given tokens\n * @param {Token[]} tokens list of scanned tokens\n * @returns {MultiToken[]}\n */\nfunction run(start, input, tokens) {\n  let len = tokens.length;\n  let cursor = 0;\n  let multis = [];\n  let textTokens = [];\n  while (cursor < len) {\n    let state = start;\n    let secondState = null;\n    let nextState = null;\n    let multiLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    while (cursor < len && !(secondState = state.go(tokens[cursor].t))) {\n      // Starting tokens with nowhere to jump to.\n      // Consider these to be just plain text\n      textTokens.push(tokens[cursor++]);\n    }\n    while (cursor < len && (nextState = secondState || state.go(tokens[cursor].t))) {\n      // Get the next state\n      secondState = null;\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts++;\n      }\n      cursor++;\n      multiLength++;\n    }\n    if (sinceAccepts < 0) {\n      // No accepting state was found, part of a regular text token add\n      // the first text token to the text tokens array and try again from\n      // the next\n      cursor -= multiLength;\n      if (cursor < len) {\n        textTokens.push(tokens[cursor]);\n        cursor++;\n      }\n    } else {\n      // Accepting state!\n      // First close off the textTokens (if available)\n      if (textTokens.length > 0) {\n        multis.push(initMultiToken(Text, input, textTokens));\n        textTokens = [];\n      }\n\n      // Roll back to the latest accepting state\n      cursor -= sinceAccepts;\n      multiLength -= sinceAccepts;\n\n      // Create a new multitoken\n      const Multi = latestAccepting.t;\n      const subtokens = tokens.slice(cursor - multiLength, cursor);\n      multis.push(initMultiToken(Multi, input, subtokens));\n    }\n  }\n\n  // Finally close off the textTokens (if available)\n  if (textTokens.length > 0) {\n    multis.push(initMultiToken(Text, input, textTokens));\n  }\n  return multis;\n}\n\n/**\n * Utility function for instantiating a new multitoken with all the relevant\n * fields during parsing.\n * @param {new (value: string, tokens: Token[]) => MultiToken} Multi class to instantiate\n * @param {string} input original input string\n * @param {Token[]} tokens consecutive tokens scanned from input string\n * @returns {MultiToken}\n */\nfunction initMultiToken(Multi, input, tokens) {\n  const startIdx = tokens[0].s;\n  const endIdx = tokens[tokens.length - 1].e;\n  const value = input.slice(startIdx, endIdx);\n  return new Multi(value, tokens);\n}\n\nconst warn = typeof console !== 'undefined' && console && console.warn || (() => {});\nconst warnAdvice = 'until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.';\n\n// Side-effect initialization state\nconst INIT = {\n  scanner: null,\n  parser: null,\n  tokenQueue: [],\n  pluginQueue: [],\n  customSchemes: [],\n  initialized: false\n};\n\n/**\n * @typedef {{\n * \tstart: State<string>,\n * \ttokens: { groups: Collections<string> } & typeof tk\n * }} ScannerInit\n */\n\n/**\n * @typedef {{\n * \tstart: State<MultiToken>,\n * \ttokens: typeof multi\n * }} ParserInit\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit }) => void} TokenPlugin\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit, parser: ParserInit }) => void} Plugin\n */\n\n/**\n * De-register all plugins and reset the internal state-machine. Used for\n * testing; not required in practice.\n * @private\n */\nfunction reset() {\n  State.groups = {};\n  INIT.scanner = null;\n  INIT.parser = null;\n  INIT.tokenQueue = [];\n  INIT.pluginQueue = [];\n  INIT.customSchemes = [];\n  INIT.initialized = false;\n  return INIT;\n}\n\n/**\n * Register a token plugin to allow the scanner to recognize additional token\n * types before the parser state machine is constructed from the results.\n * @param {string} name of plugin to register\n * @param {TokenPlugin} plugin function that accepts the scanner state machine\n * and available scanner tokens and collections and extends the state machine to\n * recognize additional tokens or groups.\n */\nfunction registerTokenPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid token plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    if (name === INIT.tokenQueue[i][0]) {\n      warn(`linkifyjs: token plugin \"${name}\" already registered - will be overwritten`);\n      INIT.tokenQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.tokenQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register token plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Register a linkify plugin\n * @param {string} name of plugin to register\n * @param {Plugin} plugin function that accepts the parser state machine and\n * extends the parser to recognize additional link types\n */\nfunction registerPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    if (name === INIT.pluginQueue[i][0]) {\n      warn(`linkifyjs: plugin \"${name}\" already registered - will be overwritten`);\n      INIT.pluginQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.pluginQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Detect URLs with the following additional protocol. Anything with format\n * \"protocol://...\" will be considered a link. If `optionalSlashSlash` is set to\n * `true`, anything with format \"protocol:...\" will be considered a link.\n * @param {string} scheme\n * @param {boolean} [optionalSlashSlash]\n */\nfunction registerCustomProtocol(scheme, optionalSlashSlash = false) {\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register custom scheme \"${scheme}\" ${warnAdvice}`);\n  }\n  if (!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(scheme)) {\n    throw new Error(`linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or \"-\"\n2. Cannot start or end with \"-\"\n3. \"-\" cannot repeat`);\n  }\n  INIT.customSchemes.push([scheme, optionalSlashSlash]);\n}\n\n/**\n * Initialize the linkify state machine. Called automatically the first time\n * linkify is called on a string, but may be called manually as well.\n */\nfunction init() {\n  // Initialize scanner state machine and plugins\n  INIT.scanner = init$2(INIT.customSchemes);\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    INIT.tokenQueue[i][1]({\n      scanner: INIT.scanner\n    });\n  }\n\n  // Initialize parser state machine and plugins\n  INIT.parser = init$1(INIT.scanner.tokens);\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    INIT.pluginQueue[i][1]({\n      scanner: INIT.scanner,\n      parser: INIT.parser\n    });\n  }\n  INIT.initialized = true;\n  return INIT;\n}\n\n/**\n * Parse a string into tokens that represent linkable and non-linkable sub-components\n * @param {string} str\n * @return {MultiToken[]} tokens\n */\nfunction tokenize(str) {\n  if (!INIT.initialized) {\n    init();\n  }\n  return run(INIT.parser.start, str, run$1(INIT.scanner.start, str));\n}\ntokenize.scan = run$1; // for testing\n\n/**\n * Find a list of linkable items in the given string.\n * @param {string} str string to find links in\n * @param {string | Opts} [type] either formatting options or specific type of\n * links to find, e.g., 'url' or 'email'\n * @param {Opts} [opts] formatting options for final output. Cannot be specified\n * if opts already provided in `type` argument\n */\nfunction find(str, type = null, opts = null) {\n  if (type && typeof type === 'object') {\n    if (opts) {\n      throw Error(`linkifyjs: Invalid link type ${type}; must be a string`);\n    }\n    opts = type;\n    type = null;\n  }\n  const options = new Options(opts);\n  const tokens = tokenize(str);\n  const filtered = [];\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.isLink && (!type || token.t === type) && options.check(token)) {\n      filtered.push(token.toFormattedObject(options));\n    }\n  }\n  return filtered;\n}\n\n/**\n * Is the given string valid linkable text of some sort. Note that this does not\n * trim the text for you.\n *\n * Optionally pass in a second `type` param, which is the type of link to test\n * for.\n *\n * For example,\n *\n *     linkify.test(str, 'email');\n *\n * Returns `true` if str is a valid email.\n * @param {string} str string to test for links\n * @param {string} [type] optional specific link type to look for\n * @returns boolean true/false\n */\nfunction test(str, type = null) {\n  const tokens = tokenize(str);\n  return tokens.length === 1 && tokens[0].isLink && (!type || tokens[0].t === type);\n}\n\nexport { MultiToken, Options, State, createTokenClass, find, init, multi, options, regexp, registerCustomProtocol, registerPlugin, registerTokenPlugin, reset, stringToArray, test, multi as text, tokenize };\n", "import type { PasteRuleMatch } from '@tiptap/core'\nimport { <PERSON>, markPasteRule, mergeAttributes } from '@tiptap/core'\nimport type { Plugin } from '@tiptap/pm/state'\nimport { find, registerCustomProtocol, reset } from 'linkifyjs'\n\nimport { autolink } from './helpers/autolink.js'\nimport { clickHandler } from './helpers/clickHandler.js'\nimport { pasteHandler } from './helpers/pasteHandler.js'\nimport { UNICODE_WHITESPACE_REGEX_GLOBAL } from './helpers/whitespace.js'\n\nexport interface LinkProtocolOptions {\n  /**\n   * The protocol scheme to be registered.\n   * @default '''\n   * @example 'ftp'\n   * @example 'git'\n   */\n  scheme: string\n\n  /**\n   * If enabled, it allows optional slashes after the protocol.\n   * @default false\n   * @example true\n   */\n  optionalSlashes?: boolean\n}\n\nexport const pasteRegex =\n  /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi\n\n/**\n * @deprecated The default behavior is now to open links when the editor is not editable.\n */\ntype DeprecatedOpenWhenNotEditable = 'whenNotEditable'\n\nexport interface LinkOptions {\n  /**\n   * If enabled, the extension will automatically add links as you type.\n   * @default true\n   * @example false\n   */\n  autolink: boolean\n\n  /**\n   * An array of custom protocols to be registered with linkifyjs.\n   * @default []\n   * @example ['ftp', 'git']\n   */\n  protocols: Array<LinkProtocolOptions | string>\n\n  /**\n   * Default protocol to use when no protocol is specified.\n   * @default 'http'\n   */\n  defaultProtocol: string\n  /**\n   * If enabled, links will be opened on click.\n   * @default true\n   * @example false\n   */\n  openOnClick: boolean | DeprecatedOpenWhenNotEditable\n  /**\n   * If enabled, the link will be selected when clicked.\n   * @default false\n   * @example true\n   */\n  enableClickSelection: boolean\n  /**\n   * Adds a link to the current selection if the pasted content only contains an url.\n   * @default true\n   * @example false\n   */\n  linkOnPaste: boolean\n\n  /**\n   * HTML attributes to add to the link element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * @deprecated Use the `shouldAutoLink` option instead.\n   * A validation function that modifies link verification for the auto linker.\n   * @param url - The url to be validated.\n   * @returns - True if the url is valid, false otherwise.\n   */\n  validate: (url: string) => boolean\n\n  /**\n   * A validation function which is used for configuring link verification for preventing XSS attacks.\n   * Only modify this if you know what you're doing.\n   *\n   * @returns {boolean} `true` if the URL is valid, `false` otherwise.\n   *\n   * @example\n   * isAllowedUri: (url, { defaultValidate, protocols, defaultProtocol }) => {\n   * return url.startsWith('./') || defaultValidate(url)\n   * }\n   */\n  isAllowedUri: (\n    /**\n     * The URL to be validated.\n     */\n    url: string,\n    ctx: {\n      /**\n       * The default validation function.\n       */\n      defaultValidate: (url: string) => boolean\n      /**\n       * An array of allowed protocols for the URL (e.g., \"http\", \"https\"). As defined in the `protocols` option.\n       */\n      protocols: Array<LinkProtocolOptions | string>\n      /**\n       * A string that represents the default protocol (e.g., 'http'). As defined in the `defaultProtocol` option.\n       */\n      defaultProtocol: string\n    },\n  ) => boolean\n\n  /**\n   * Determines whether a valid link should be automatically linked in the content.\n   *\n   * @param {string} url - The URL that has already been validated.\n   * @returns {boolean} - True if the link should be auto-linked; false if it should not be auto-linked.\n   */\n  shouldAutoLink: (url: string) => boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    link: {\n      /**\n       * Set a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.setLink({ href: 'https://tiptap.dev' })\n       */\n      setLink: (attributes: {\n        href: string\n        target?: string | null\n        rel?: string | null\n        class?: string | null\n      }) => ReturnType\n      /**\n       * Toggle a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.toggleLink({ href: 'https://tiptap.dev' })\n       */\n      toggleLink: (attributes?: {\n        href: string\n        target?: string | null\n        rel?: string | null\n        class?: string | null\n      }) => ReturnType\n      /**\n       * Unset a link mark\n       * @example editor.commands.unsetLink()\n       */\n      unsetLink: () => ReturnType\n    }\n  }\n}\n\nexport function isAllowedUri(uri: string | undefined, protocols?: LinkOptions['protocols']) {\n  const allowedProtocols: string[] = ['http', 'https', 'ftp', 'ftps', 'mailto', 'tel', 'callto', 'sms', 'cid', 'xmpp']\n\n  if (protocols) {\n    protocols.forEach(protocol => {\n      const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme\n\n      if (nextProtocol) {\n        allowedProtocols.push(nextProtocol)\n      }\n    })\n  }\n\n  return (\n    !uri ||\n    uri.replace(UNICODE_WHITESPACE_REGEX_GLOBAL, '').match(\n      new RegExp(\n        // eslint-disable-next-line no-useless-escape\n        `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`,\n        'i',\n      ),\n    )\n  )\n}\n\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nexport const Link = Mark.create<LinkOptions>({\n  name: 'link',\n\n  priority: 1000,\n\n  keepOnSplit: false,\n\n  exitable: true,\n\n  onCreate() {\n    if (this.options.validate && !this.options.shouldAutoLink) {\n      // Copy the validate function to the shouldAutoLink option\n      this.options.shouldAutoLink = this.options.validate\n      console.warn('The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.')\n    }\n    this.options.protocols.forEach(protocol => {\n      if (typeof protocol === 'string') {\n        registerCustomProtocol(protocol)\n        return\n      }\n      registerCustomProtocol(protocol.scheme, protocol.optionalSlashes)\n    })\n  },\n\n  onDestroy() {\n    reset()\n  },\n\n  inclusive() {\n    return this.options.autolink\n  },\n\n  addOptions() {\n    return {\n      openOnClick: true,\n      enableClickSelection: false,\n      linkOnPaste: true,\n      autolink: true,\n      protocols: [],\n      defaultProtocol: 'http',\n      HTMLAttributes: {\n        target: '_blank',\n        rel: 'noopener noreferrer nofollow',\n        class: null,\n      },\n      isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n      validate: url => !!url,\n      shouldAutoLink: url => !!url,\n    }\n  },\n\n  addAttributes() {\n    return {\n      href: {\n        default: null,\n        parseHTML(element) {\n          return element.getAttribute('href')\n        },\n      },\n      target: {\n        default: this.options.HTMLAttributes.target,\n      },\n      rel: {\n        default: this.options.HTMLAttributes.rel,\n      },\n      class: {\n        default: this.options.HTMLAttributes.class,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'a[href]',\n        getAttrs: dom => {\n          const href = (dom as HTMLElement).getAttribute('href')\n\n          // prevent XSS attacks\n          if (\n            !href ||\n            !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n          return null\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    // prevent XSS attacks\n    if (\n      !this.options.isAllowedUri(HTMLAttributes.href, {\n        defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n        protocols: this.options.protocols,\n        defaultProtocol: this.options.defaultProtocol,\n      })\n    ) {\n      // strip out the href\n      return ['a', mergeAttributes(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }), 0]\n    }\n\n    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setLink:\n        attributes =>\n        ({ chain }) => {\n          const { href } = attributes\n\n          if (\n            !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n\n          return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run()\n        },\n\n      toggleLink:\n        attributes =>\n        ({ chain }) => {\n          const { href } = attributes || {}\n\n          if (\n            href &&\n            !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n\n          return chain()\n            .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n\n      unsetLink:\n        () =>\n        ({ chain }) => {\n          return chain().unsetMark(this.name, { extendEmptyMarkRange: true }).setMeta('preventAutolink', true).run()\n        },\n    }\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: text => {\n          const foundLinks: PasteRuleMatch[] = []\n\n          if (text) {\n            const { protocols, defaultProtocol } = this.options\n            const links = find(text).filter(\n              item =>\n                item.isLink &&\n                this.options.isAllowedUri(item.value, {\n                  defaultValidate: href => !!isAllowedUri(href, protocols),\n                  protocols,\n                  defaultProtocol,\n                }),\n            )\n\n            if (links.length) {\n              links.forEach(link =>\n                foundLinks.push({\n                  text: link.value,\n                  data: {\n                    href: link.href,\n                  },\n                  index: link.start,\n                }),\n              )\n            }\n          }\n\n          return foundLinks\n        },\n        type: this.type,\n        getAttributes: match => {\n          return {\n            href: match.data?.href,\n          }\n        },\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    const plugins: Plugin[] = []\n    const { protocols, defaultProtocol } = this.options\n\n    if (this.options.autolink) {\n      plugins.push(\n        autolink({\n          type: this.type,\n          defaultProtocol: this.options.defaultProtocol,\n          validate: url =>\n            this.options.isAllowedUri(url, {\n              defaultValidate: href => !!isAllowedUri(href, protocols),\n              protocols,\n              defaultProtocol,\n            }),\n          shouldAutoLink: this.options.shouldAutoLink,\n        }),\n      )\n    }\n\n    if (this.options.openOnClick === true) {\n      plugins.push(\n        clickHandler({\n          type: this.type,\n          editor: this.editor,\n          enableClickSelection: this.options.enableClickSelection,\n        }),\n      )\n    }\n\n    if (this.options.linkOnPaste) {\n      plugins.push(\n        pasteHandler({\n          editor: this.editor,\n          defaultProtocol: this.options.defaultProtocol,\n          type: this.type,\n        }),\n      )\n    }\n\n    return plugins\n  },\n})\n", "import type { NodeWithPos } from '@tiptap/core'\nimport { combineTransactionSteps, findChildrenInRange, getChangedRanges, getMarksBetween } from '@tiptap/core'\nimport type { MarkType } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport type { MultiToken } from 'linkifyjs'\nimport { tokenize } from 'linkifyjs'\n\nimport { UNICODE_WHITESPACE_REGEX, UNICODE_WHITESPACE_REGEX_END } from './whitespace.js'\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens: Array<ReturnType<MultiToken['toObject']>>) {\n  if (tokens.length === 1) {\n    return tokens[0].isLink\n  }\n\n  if (tokens.length === 3 && tokens[1].isLink) {\n    return ['()', '[]'].includes(tokens[0].value + tokens[2].value)\n  }\n\n  return false\n}\n\ntype AutolinkOptions = {\n  type: MarkType\n  defaultProtocol: string\n  validate: (url: string) => boolean\n  shouldAutoLink: (url: string) => boolean\n}\n\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nexport function autolink(options: AutolinkOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('autolink'),\n    appendTransaction: (transactions, oldState, newState) => {\n      /**\n       * Does the transaction change the document?\n       */\n      const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n      /**\n       * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n       */\n      const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'))\n\n      /**\n       * Prevent autolink if the transaction is not a document change\n       * or if the transaction has the meta `preventAutolink`.\n       */\n      if (!docChanges || preventAutolink) {\n        return\n      }\n\n      const { tr } = newState\n      const transform = combineTransactionSteps(oldState.doc, [...transactions])\n      const changes = getChangedRanges(transform)\n\n      changes.forEach(({ newRange }) => {\n        // Now let’s see if we can add new links.\n        const nodesInChangedRanges = findChildrenInRange(newState.doc, newRange, node => node.isTextblock)\n\n        let textBlock: NodeWithPos | undefined\n        let textBeforeWhitespace: string | undefined\n\n        if (nodesInChangedRanges.length > 1) {\n          // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            textBlock.pos + textBlock.node.nodeSize,\n            undefined,\n            ' ',\n          )\n        } else if (nodesInChangedRanges.length) {\n          const endText = newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ')\n          if (!UNICODE_WHITESPACE_REGEX_END.test(endText)) {\n            return\n          }\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, newRange.to, undefined, ' ')\n        }\n\n        if (textBlock && textBeforeWhitespace) {\n          const wordsBeforeWhitespace = textBeforeWhitespace.split(UNICODE_WHITESPACE_REGEX).filter(Boolean)\n\n          if (wordsBeforeWhitespace.length <= 0) {\n            return false\n          }\n\n          const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1]\n          const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace)\n\n          if (!lastWordBeforeSpace) {\n            return false\n          }\n\n          const linksBeforeSpace = tokenize(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol))\n\n          if (!isValidLinkStructure(linksBeforeSpace)) {\n            return false\n          }\n\n          linksBeforeSpace\n            .filter(link => link.isLink)\n            // Calculate link position.\n            .map(link => ({\n              ...link,\n              from: lastWordAndBlockOffset + link.start + 1,\n              to: lastWordAndBlockOffset + link.end + 1,\n            }))\n            // ignore link inside code mark\n            .filter(link => {\n              if (!newState.schema.marks.code) {\n                return true\n              }\n\n              return !newState.doc.rangeHasMark(link.from, link.to, newState.schema.marks.code)\n            })\n            // validate link\n            .filter(link => options.validate(link.value))\n            // check whether should autolink\n            .filter(link => options.shouldAutoLink(link.value))\n            // Add link mark.\n            .forEach(link => {\n              if (getMarksBetween(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                return\n              }\n\n              tr.addMark(\n                link.from,\n                link.to,\n                options.type.create({\n                  href: link.href,\n                }),\n              )\n            })\n        }\n      })\n\n      if (!tr.steps.length) {\n        return\n      }\n\n      return tr\n    },\n  })\n}\n", "// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.ts\nexport const UNICODE_WHITESPACE_PATTERN = '[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]'\n\nexport const UNICODE_WHITESPACE_REGEX = new RegExp(UNICODE_WHITESPACE_PATTERN)\nexport const UNICODE_WHITESPACE_REGEX_END = new RegExp(`${UNICODE_WHITESPACE_PATTERN}$`)\nexport const UNICODE_WHITESPACE_REGEX_GLOBAL = new RegExp(UNICODE_WHITESPACE_PATTERN, 'g')\n", "import type { Editor } from '@tiptap/core'\nimport { getAttributes } from '@tiptap/core'\nimport type { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\n\ntype ClickHandlerOptions = {\n  type: MarkType\n  editor: Editor\n  enableClickSelection?: boolean\n}\n\nexport function clickHandler(options: ClickHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handleClickLink'),\n    props: {\n      handleClick: (view, pos, event) => {\n        if (event.button !== 0) {\n          return false\n        }\n\n        if (!view.editable) {\n          return false\n        }\n\n        let link: HTMLAnchorElement | null = null\n\n        if (event.target instanceof HTMLAnchorElement) {\n          link = event.target\n        } else {\n          let a = event.target as HTMLElement\n          const els = []\n\n          while (a.nodeName !== 'DIV') {\n            els.push(a)\n            a = a.parentNode as HTMLElement\n          }\n          link = els.find(value => value.nodeName === 'A') as HTMLAnchorElement\n        }\n\n        if (!link) {\n          return false\n        }\n\n        const attrs = getAttributes(view.state, options.type.name)\n        const href = link?.href ?? attrs.href\n        const target = link?.target ?? attrs.target\n\n        if (options.enableClickSelection) {\n          options.editor.commands.extendMarkRange(options.type.name)\n        }\n\n        if (link && href) {\n          window.open(href, target)\n\n          return true\n        }\n\n        return false\n      },\n    },\n  })\n}\n", "import type { Editor } from '@tiptap/core'\nimport type { MarkType } from '@tiptap/pm/model'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { find } from 'linkifyjs'\n\ntype PasteHandlerOptions = {\n  editor: Editor\n  defaultProtocol: string\n  type: MarkType\n}\n\nexport function pasteHandler(options: PasteHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handlePasteLink'),\n    props: {\n      handlePaste: (view, event, slice) => {\n        const { state } = view\n        const { selection } = state\n        const { empty } = selection\n\n        if (empty) {\n          return false\n        }\n\n        let textContent = ''\n\n        slice.content.forEach(node => {\n          textContent += node.textContent\n        })\n\n        const link = find(textContent, { defaultProtocol: options.defaultProtocol }).find(\n          item => item.isLink && item.value === textContent,\n        )\n\n        if (!textContent || !link) {\n          return false\n        }\n\n        return options.editor.commands.setMark(options.type, {\n          href: link.href,\n        })\n      },\n    },\n  })\n}\n", "import { Link } from './link.js'\n\nexport * from './link.js'\n\nexport default Link\n", "import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface BulletListOptions {\n  /**\n   * The node name for the list items\n   * @default 'listItem'\n   * @example 'paragraph'\n   */\n  itemTypeName: string\n\n  /**\n   * HTML attributes to add to the bullet list element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Keep the marks when splitting the list\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean\n\n  /**\n   * Keep the attributes when splitting the list\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bulletList: {\n      /**\n       * Toggle a bullet list\n       */\n      toggleBulletList: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nexport const bulletListInputRegex = /^\\s*([-+*])\\s$/\n\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nexport const BulletList = Node.create<BulletListOptions>({\n  name: 'bulletList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [{ tag: 'ul' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleBulletList:\n        () =>\n        ({ commands, chain }) => {\n          if (this.options.keepAttributes) {\n            return chain()\n              .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n              .updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName))\n              .run()\n          }\n          return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: bulletListInputRegex,\n      type: this.type,\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: bulletListInputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: () => {\n          return this.editor.getAttributes(TextStyleName)\n        },\n        editor: this.editor,\n      })\n    }\n    return [inputRule]\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n", "import { Extension } from '@tiptap/core'\n\nimport { handleBackspace, handleDelete } from './listHelpers/index.js'\n\nexport type ListKeymapOptions = {\n  /**\n   * An array of list types. This is used for item and wrapper list matching.\n   * @default []\n   * @example [{ itemName: 'listItem', wrapperNames: ['bulletList', 'orderedList'] }]\n   */\n  listTypes: Array<{\n    itemName: string\n    wrapperNames: string[]\n  }>\n}\n\n/**\n * This extension registers custom keymaps to change the behaviour of the backspace and delete keys.\n * By default Prosemirror keyhandling will always lift or sink items so paragraphs are joined into\n * the adjacent or previous list item. This extension will prevent this behaviour and instead will\n * try to join paragraphs from two list items into a single list item.\n * @see https://www.tiptap.dev/api/extensions/list-keymap\n */\nexport const ListKeymap = Extension.create<ListKeymapOptions>({\n  name: 'listKeymap',\n\n  addOptions() {\n    return {\n      listTypes: [\n        {\n          itemName: 'listItem',\n          wrapperNames: ['bulletList', 'orderedList'],\n        },\n        {\n          itemName: 'taskItem',\n          wrapperNames: ['taskList'],\n        },\n      ],\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Delete: ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleDelete(editor, itemName)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      'Mod-Delete': ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleDelete(editor, itemName)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      Backspace: ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName, wrapperNames }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleBackspace(editor, itemName, wrapperNames)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      'Mod-Backspace': ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName, wrapperNames }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleBackspace(editor, itemName, wrapperNames)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n    }\n  },\n})\n", "export * from './findListItemPos.js'\nexport * from './getNextListDepth.js'\nexport * from './handleBackspace.js'\nexport * from './handleDelete.js'\nexport * from './hasListBefore.js'\nexport * from './hasListItemAfter.js'\nexport * from './hasListItemBefore.js'\nexport * from './listItemHasSubList.js'\nexport * from './nextListIsDeeper.js'\nexport * from './nextListIsHigher.js'\n", "import { getNodeType } from '@tiptap/core'\nimport type { NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nexport const findListItemPos = (typeOrName: string | NodeType, state: EditorState) => {\n  const { $from } = state.selection\n  const nodeType = getNodeType(typeOrName, state.schema)\n\n  let currentNode = null\n  let currentDepth = $from.depth\n  let currentPos = $from.pos\n  let targetDepth: number | null = null\n\n  while (currentDepth > 0 && targetDepth === null) {\n    currentNode = $from.node(currentDepth)\n\n    if (currentNode.type === nodeType) {\n      targetDepth = currentDepth\n    } else {\n      currentDepth -= 1\n      currentPos -= 1\n    }\n  }\n\n  if (targetDepth === null) {\n    return null\n  }\n\n  return { $pos: state.doc.resolve(currentPos), depth: targetDepth }\n}\n", "import { getNodeAtPosition } from '@tiptap/core'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\n\nexport const getNextListDepth = (typeOrName: string, state: EditorState) => {\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos) {\n    return false\n  }\n\n  const [, depth] = getNodeAtPosition(state, typeOrName, listItemPos.$pos.pos + 4)\n\n  return depth\n}\n", "import type { Editor } from '@tiptap/core'\nimport { isAtStartOfNode, isNodeActive } from '@tiptap/core'\nimport type { Node } from '@tiptap/pm/model'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { hasListBefore } from './hasListBefore.js'\nimport { hasListItemBefore } from './hasListItemBefore.js'\nimport { listItemHasSubList } from './listItemHasSubList.js'\n\nexport const handleBackspace = (editor: Editor, name: string, parentListTypes: string[]) => {\n  // this is required to still handle the undo handling\n  if (editor.commands.undoInputRule()) {\n    return true\n  }\n\n  // if the selection is not collapsed\n  // we can rely on the default backspace behavior\n  if (editor.state.selection.from !== editor.state.selection.to) {\n    return false\n  }\n\n  // if the current item is NOT inside a list item &\n  // the previous item is a list (orderedList or bulletList)\n  // move the cursor into the list and delete the current item\n  if (!isNodeActive(editor.state, name) && hasListBefore(editor.state, name, parentListTypes)) {\n    const { $anchor } = editor.state.selection\n\n    const $listPos = editor.state.doc.resolve($anchor.before() - 1)\n\n    const listDescendants: Array<{ node: Node; pos: number }> = []\n\n    $listPos.node().descendants((node, pos) => {\n      if (node.type.name === name) {\n        listDescendants.push({ node, pos })\n      }\n    })\n\n    const lastItem = listDescendants.at(-1)\n\n    if (!lastItem) {\n      return false\n    }\n\n    const $lastItemPos = editor.state.doc.resolve($listPos.start() + lastItem.pos + 1)\n\n    return editor\n      .chain()\n      .cut({ from: $anchor.start() - 1, to: $anchor.end() + 1 }, $lastItemPos.end())\n      .joinForward()\n      .run()\n  }\n\n  // if the cursor is not inside the current node type\n  // do nothing and proceed\n  if (!isNodeActive(editor.state, name)) {\n    return false\n  }\n\n  // if the cursor is not at the start of a node\n  // do nothing and proceed\n  if (!isAtStartOfNode(editor.state)) {\n    return false\n  }\n\n  const listItemPos = findListItemPos(name, editor.state)\n\n  if (!listItemPos) {\n    return false\n  }\n\n  const $prev = editor.state.doc.resolve(listItemPos.$pos.pos - 2)\n  const prevNode = $prev.node(listItemPos.depth)\n\n  const previousListItemHasSubList = listItemHasSubList(name, editor.state, prevNode)\n\n  // if the previous item is a list item and doesn't have a sublist, join the list items\n  if (hasListItemBefore(name, editor.state) && !previousListItemHasSubList) {\n    return editor.commands.joinItemBackward()\n  }\n\n  // otherwise in the end, a backspace should\n  // always just lift the list item if\n  // joining / merging is not possible\n  return editor.chain().liftListItem(name).run()\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListBefore = (editorState: EditorState, name: string, parentListTypes: string[]) => {\n  const { $anchor } = editorState.selection\n\n  const previousNodePos = Math.max(0, $anchor.pos - 2)\n\n  const previousNode = editorState.doc.resolve(previousNodePos).node()\n\n  if (!previousNode || !parentListTypes.includes(previousNode.type.name)) {\n    return false\n  }\n\n  return true\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListItemBefore = (typeOrName: string, state: EditorState): boolean => {\n  const { $anchor } = state.selection\n\n  const $targetPos = state.doc.resolve($anchor.pos - 2)\n\n  if ($targetPos.index() === 0) {\n    return false\n  }\n\n  if ($targetPos.nodeBefore?.type.name !== typeOrName) {\n    return false\n  }\n\n  return true\n}\n", "import { getNodeType } from '@tiptap/core'\nimport type { Node } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nexport const listItemHasSubList = (typeOrName: string, state: EditorState, node?: Node) => {\n  if (!node) {\n    return false\n  }\n\n  const nodeType = getNodeType(typeOrName, state.schema)\n\n  let hasSubList = false\n\n  node.descendants(child => {\n    if (child.type === nodeType) {\n      hasSubList = true\n    }\n  })\n\n  return hasSubList\n}\n", "import type { Editor } from '@tiptap/core'\nimport { isAtEndOfNode, isNodeActive } from '@tiptap/core'\n\nimport { nextListIsDeeper } from './nextListIsDeeper.js'\nimport { nextListIsHigher } from './nextListIsHigher.js'\n\nexport const handleDelete = (editor: Editor, name: string) => {\n  // if the cursor is not inside the current node type\n  // do nothing and proceed\n  if (!isNodeActive(editor.state, name)) {\n    return false\n  }\n\n  // if the cursor is not at the end of a node\n  // do nothing and proceed\n  if (!isAtEndOfNode(editor.state, name)) {\n    return false\n  }\n\n  // if the selection is not collapsed, or not within a single node\n  // do nothing and proceed\n  const { selection } = editor.state\n  const { $from, $to } = selection\n\n  if (!selection.empty && $from.sameParent($to)) {\n    return false\n  }\n\n  // check if the next node is a list with a deeper depth\n  if (nextListIsDeeper(name, editor.state)) {\n    return editor\n      .chain()\n      .focus(editor.state.selection.from + 4)\n      .lift(name)\n      .joinBackward()\n      .run()\n  }\n\n  if (nextList<PERSON>s<PERSON>igh<PERSON>(name, editor.state)) {\n    return editor.chain().joinForward().joinBackward().run()\n  }\n\n  return editor.commands.joinItemForward()\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { getNextListDepth } from './getNextListDepth.js'\n\nexport const nextListIsDeeper = (typeOrName: string, state: EditorState) => {\n  const listDepth = getNextListDepth(typeOrName, state)\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos || !listDepth) {\n    return false\n  }\n\n  if (listDepth > listItemPos.depth) {\n    return true\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { getNextListDepth } from './getNextListDepth.js'\n\nexport const nextListIsHigher = (typeOrName: string, state: EditorState) => {\n  const listDepth = getNextListDepth(typeOrName, state)\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos || !listDepth) {\n    return false\n  }\n\n  if (listDepth < listItemPos.depth) {\n    return true\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListItemAfter = (typeOrName: string, state: EditorState): boolean => {\n  const { $anchor } = state.selection\n\n  const $targetPos = state.doc.resolve($anchor.pos - $anchor.parentOffset - 2)\n\n  if ($targetPos.index() === $targetPos.parent.childCount - 1) {\n    return false\n  }\n\n  if ($targetPos.nodeAfter?.type.name !== typeOrName) {\n    return false\n  }\n\n  return true\n}\n", "import { Extension } from '@tiptap/core'\n\nimport type { BulletListOptions } from '../bullet-list/index.js'\nimport { BulletList } from '../bullet-list/index.js'\nimport type { ListItemOptions } from '../item/index.js'\nimport { ListItem } from '../item/index.js'\nimport type { ListKeymapOptions } from '../keymap/index.js'\nimport { ListKeymap } from '../keymap/index.js'\nimport type { OrderedListOptions } from '../ordered-list/index.js'\nimport { OrderedList } from '../ordered-list/index.js'\nimport type { TaskItemOptions } from '../task-item/index.js'\nimport { TaskItem } from '../task-item/index.js'\nimport type { TaskListOptions } from '../task-list/index.js'\nimport { TaskList } from '../task-list/index.js'\n\nexport interface ListKitOptions {\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example table: false\n   */\n  bulletList: Partial<BulletListOptions> | false\n  /**\n   * If set to false, the listItem extension will not be registered\n   */\n  listItem: Partial<ListItemOptions> | false\n  /**\n   * If set to false, the listKeymap extension will not be registered\n   */\n  listKeymap: Partial<ListKeymapOptions> | false\n  /**\n   * If set to false, the orderedList extension will not be registered\n   */\n  orderedList: Partial<OrderedListOptions> | false\n  /**\n   * If set to false, the taskItem extension will not be registered\n   */\n  taskItem: Partial<TaskItemOptions> | false\n  /**\n   * If set to false, the taskList extension will not be registered\n   */\n  taskList: Partial<TaskListOptions> | false\n}\n\n/**\n * The table kit is a collection of table editor extensions.\n *\n * It’s a good starting point for building your own table in Tiptap.\n */\nexport const ListKit = Extension.create<ListKitOptions>({\n  name: 'listKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.listKeymap !== false) {\n      extensions.push(ListKeymap.configure(this.options.listKeymap))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.taskItem !== false) {\n      extensions.push(TaskItem.configure(this.options.taskItem))\n    }\n\n    if (this.options.taskList !== false) {\n      extensions.push(TaskList.configure(this.options.taskList))\n    }\n\n    return extensions\n  },\n})\n", "import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface OrderedListOptions {\n  /**\n   * The node type name for list items.\n   * @default 'listItem'\n   * @example 'myListItem'\n   */\n  itemTypeName: string\n\n  /**\n   * The HTML attributes for an ordered list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Keep the marks when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean\n\n  /**\n   * Keep the attributes when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    orderedList: {\n      /**\n       * Toggle an ordered list\n       * @example editor.commands.toggleOrderedList()\n       */\n      toggleOrderedList: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nexport const orderedListInputRegex = /^(\\d+)\\.\\s$/\n\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const OrderedList = Node.create<OrderedListOptions>({\n  name: 'orderedList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  addAttributes() {\n    return {\n      start: {\n        default: 1,\n        parseHTML: element => {\n          return element.hasAttribute('start') ? parseInt(element.getAttribute('start') || '', 10) : 1\n        },\n      },\n      type: {\n        default: null,\n        parseHTML: element => element.getAttribute('type'),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'ol',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const { start, ...attributesWithoutStart } = HTMLAttributes\n\n    return start === 1\n      ? ['ol', mergeAttributes(this.options.HTMLAttributes, attributesWithoutStart), 0]\n      : ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleOrderedList:\n        () =>\n        ({ commands, chain }) => {\n          if (this.options.keepAttributes) {\n            return chain()\n              .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n              .updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName))\n              .run()\n          }\n          return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: orderedListInputRegex,\n      type: this.type,\n      getAttributes: match => ({ start: +match[1] }),\n      joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: orderedListInputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n        joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        editor: this.editor,\n      })\n    }\n    return [inputRule]\n  },\n})\n", "import type { KeyboardShortcutCommand } from '@tiptap/core'\nimport { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\nimport type { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nexport interface TaskItemOptions {\n  /**\n   * A callback function that is called when the checkbox is clicked while the editor is in readonly mode.\n   * @param node The prosemirror node of the task item\n   * @param checked The new checked state\n   * @returns boolean\n   */\n  onReadOnlyChecked?: (node: ProseMirrorNode, checked: boolean) => boolean\n\n  /**\n   * Controls whether the task items can be nested or not.\n   * @default false\n   * @example true\n   */\n  nested: boolean\n\n  /**\n   * HTML attributes to add to the task item element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * The node type for taskList nodes\n   * @default 'taskList'\n   * @example 'myCustomTaskList'\n   */\n  taskListTypeName: string\n\n  /**\n   * Accessibility options for the task item.\n   * @default {}\n   * @example\n   * ```js\n   * {\n   *   checkboxLabel: (node) => `Task item: ${node.textContent || 'empty task item'}`\n   * }\n   */\n  a11y?: {\n    checkboxLabel?: (node: ProseMirrorNode, checked: boolean) => string\n  }\n}\n\n/**\n * Matches a task item to a - [ ] on input.\n */\nexport const inputRegex = /^\\s*(\\[([( |x])?\\])\\s$/\n\n/**\n * This extension allows you to create task items.\n * @see https://www.tiptap.dev/api/nodes/task-item\n */\nexport const TaskItem = Node.create<TaskItemOptions>({\n  name: 'taskItem',\n\n  addOptions() {\n    return {\n      nested: false,\n      HTMLAttributes: {},\n      taskListTypeName: 'taskList',\n      a11y: undefined,\n    }\n  },\n\n  content() {\n    return this.options.nested ? 'paragraph block*' : 'paragraph+'\n  },\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      checked: {\n        default: false,\n        keepOnSplit: false,\n        parseHTML: element => {\n          const dataChecked = element.getAttribute('data-checked')\n\n          return dataChecked === '' || dataChecked === 'true'\n        },\n        renderHTML: attributes => ({\n          'data-checked': attributes.checked,\n        }),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `li[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    return [\n      'li',\n      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {\n        'data-type': this.name,\n      }),\n      [\n        'label',\n        [\n          'input',\n          {\n            type: 'checkbox',\n            checked: node.attrs.checked ? 'checked' : null,\n          },\n        ],\n        ['span'],\n      ],\n      ['div', 0],\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    const shortcuts: {\n      [key: string]: KeyboardShortcutCommand\n    } = {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n\n    if (!this.options.nested) {\n      return shortcuts\n    }\n\n    return {\n      ...shortcuts,\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n    }\n  },\n\n  addNodeView() {\n    return ({ node, HTMLAttributes, getPos, editor }) => {\n      const listItem = document.createElement('li')\n      const checkboxWrapper = document.createElement('label')\n      const checkboxStyler = document.createElement('span')\n      const checkbox = document.createElement('input')\n      const content = document.createElement('div')\n\n      const updateA11Y = (currentNode: ProseMirrorNode) => {\n        checkbox.ariaLabel =\n          this.options.a11y?.checkboxLabel?.(currentNode, checkbox.checked) ||\n          `Task item checkbox for ${currentNode.textContent || 'empty task item'}`\n      }\n\n      updateA11Y(node)\n\n      checkboxWrapper.contentEditable = 'false'\n      checkbox.type = 'checkbox'\n      checkbox.addEventListener('mousedown', event => event.preventDefault())\n      checkbox.addEventListener('change', event => {\n        // if the editor isn’t editable and we don't have a handler for\n        // readonly checks we have to undo the latest change\n        if (!editor.isEditable && !this.options.onReadOnlyChecked) {\n          checkbox.checked = !checkbox.checked\n\n          return\n        }\n\n        const { checked } = event.target as any\n\n        if (editor.isEditable && typeof getPos === 'function') {\n          editor\n            .chain()\n            .focus(undefined, { scrollIntoView: false })\n            .command(({ tr }) => {\n              const position = getPos()\n\n              if (typeof position !== 'number') {\n                return false\n              }\n              const currentNode = tr.doc.nodeAt(position)\n\n              tr.setNodeMarkup(position, undefined, {\n                ...currentNode?.attrs,\n                checked,\n              })\n\n              return true\n            })\n            .run()\n        }\n        if (!editor.isEditable && this.options.onReadOnlyChecked) {\n          // Reset state if onReadOnlyChecked returns false\n          if (!this.options.onReadOnlyChecked(node, checked)) {\n            checkbox.checked = !checkbox.checked\n          }\n        }\n      })\n\n      Object.entries(this.options.HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      listItem.dataset.checked = node.attrs.checked\n      checkbox.checked = node.attrs.checked\n\n      checkboxWrapper.append(checkbox, checkboxStyler)\n      listItem.append(checkboxWrapper, content)\n\n      Object.entries(HTMLAttributes).forEach(([key, value]) => {\n        listItem.setAttribute(key, value)\n      })\n\n      return {\n        dom: listItem,\n        contentDOM: content,\n        update: updatedNode => {\n          if (updatedNode.type !== this.type) {\n            return false\n          }\n\n          listItem.dataset.checked = updatedNode.attrs.checked\n          checkbox.checked = updatedNode.attrs.checked\n          updateA11Y(updatedNode)\n\n          return true\n        },\n      }\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => ({\n          checked: match[match.length - 1] === 'x',\n        }),\n      }),\n    ]\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface TaskListOptions {\n  /**\n   * The node type name for a task item.\n   * @default 'taskItem'\n   * @example 'myCustomTaskItem'\n   */\n  itemTypeName: string\n\n  /**\n   * The HTML attributes for a task list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    taskList: {\n      /**\n       * Toggle a task list\n       * @example editor.commands.toggleTaskList()\n       */\n      toggleTaskList: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create task lists.\n * @see https://www.tiptap.dev/api/nodes/task-list\n */\nexport const TaskList = Node.create<TaskListOptions>({\n  name: 'taskList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'taskItem',\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `ul[data-type=\"${this.name}\"]`,\n        priority: 51,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, { 'data-type': this.name }), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleTaskList:\n        () =>\n        ({ commands }) => {\n          return commands.toggleList(this.name, this.options.itemTypeName)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-9': () => this.editor.commands.toggleTaskList(),\n    }\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ParagraphOptions {\n  /**\n   * The HTML attributes for a paragraph node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    paragraph: {\n      /**\n       * Toggle a paragraph\n       * @example editor.commands.toggleParagraph()\n       */\n      setParagraph: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create paragraphs.\n * @see https://www.tiptap.dev/api/nodes/paragraph\n */\nexport const Paragraph = Node.create<ParagraphOptions>({\n  name: 'paragraph',\n\n  priority: 1000,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  content: 'inline*',\n\n  parseHTML() {\n    return [{ tag: 'p' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['p', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setParagraph:\n        () =>\n        ({ commands }) => {\n          return commands.setNode(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-0': () => this.editor.commands.setParagraph(),\n    }\n  },\n})\n", "import { Paragraph } from './paragraph.js'\n\nexport * from './paragraph.js'\n\nexport default Paragraph\n", "import { Mark, markInputRule, markPasteRule, mergeAttributes } from '@tiptap/core'\n\nexport interface StrikeOptions {\n  /**\n   * HTML attributes to add to the strike element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    strike: {\n      /**\n       * Set a strike mark\n       * @example editor.commands.setStrike()\n       */\n      setStrike: () => ReturnType\n      /**\n       * Toggle a strike mark\n       * @example editor.commands.toggleStrike()\n       */\n      toggleStrike: () => ReturnType\n      /**\n       * Unset a strike mark\n       * @example editor.commands.unsetStrike()\n       */\n      unsetStrike: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a strike to a ~~strike~~ on input.\n */\nexport const inputRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))$/\n\n/**\n * Matches a strike to a ~~strike~~ on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))/g\n\n/**\n * This extension allows you to create strike text.\n * @see https://www.tiptap.dev/api/marks/strike\n */\nexport const Strike = Mark.create<StrikeOptions>({\n  name: 'strike',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 's',\n      },\n      {\n        tag: 'del',\n      },\n      {\n        tag: 'strike',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('line-through') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setStrike:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleStrike:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetStrike:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-s': () => this.editor.commands.toggleStrike(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Strike } from './strike.js'\n\nexport * from './strike.js'\n\nexport default Strike\n", "import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n", "import { Text } from './text.js'\n\nexport * from './text.js'\n\nexport default Text\n", "import { Mark, mergeAttributes } from '@tiptap/core'\n\nexport interface UnderlineOptions {\n  /**\n   * HTML attributes to add to the underline element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    underline: {\n      /**\n       * Set an underline mark\n       * @example editor.commands.setUnderline()\n       */\n      setUnderline: () => ReturnType\n      /**\n       * Toggle an underline mark\n       * @example editor.commands.toggleUnderline()\n       */\n      toggleUnderline: () => ReturnType\n      /**\n       * Unset an underline mark\n       * @example editor.commands.unsetUnderline()\n       */\n      unsetUnderline: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nexport const Underline = Mark.create<UnderlineOptions>({\n  name: 'underline',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'u',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('underline') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['u', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetUnderline:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-u': () => this.editor.commands.toggleUnderline(),\n      'Mod-U': () => this.editor.commands.toggleUnderline(),\n    }\n  },\n})\n", "import { Underline } from './underline.js'\n\nexport * from './underline.js'\n\nexport default Underline\n", "import { Plugin } from 'prosemirror-state';\nimport { dropPoint } from 'prosemirror-transform';\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        let editorDOM = this.editorView.dom, editorRect = editorDOM.getBoundingClientRect();\n        let scaleX = editorRect.width / editorDOM.offsetWidth, scaleY = editorRect.height / editorDOM.offsetHeight;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    let halfWidth = (this.width / 2) * scaleY;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - halfWidth, bottom: top + halfWidth };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            let halfWidth = (this.width / 2) * scaleX;\n            rect = { left: coords.left - halfWidth, right: coords.left + halfWidth, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            let parentScaleX = rect.width / parent.offsetWidth, parentScaleY = rect.height / parent.offsetHeight;\n            parentLeft = rect.left - parent.scrollLeft * parentScaleX;\n            parentTop = rect.top - parent.scrollTop * parentScaleY;\n        }\n        this.element.style.left = (rect.left - parentLeft) / scaleX + \"px\";\n        this.element.style.top = (rect.top - parentTop) / scaleY + \"px\";\n        this.element.style.width = (rect.right - rect.left) / scaleX + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) / scaleY + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\"\n            ? disableDropCursor(this.editorView, pos, event)\n            : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = dropPoint(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (!this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\nexport { dropCursor };\n", "import { keydown<PERSON><PERSON><PERSON> } from 'prosemirror-keymap';\nimport { Selection, NodeSelection, TextSelection, Plugin } from 'prosemirror-state';\nimport { Slice, Fragment } from 'prosemirror-model';\nimport { DecorationSet, Decoration } from 'prosemirror-view';\n\n/**\nGap cursor selections are represented using this class. Its\n`$anchor` and `$head` properties both point at the cursor position.\n*/\nclass GapCursor extends Selection {\n    /**\n    Create a gap cursor.\n    */\n    constructor($pos) {\n        super($pos, $pos);\n    }\n    map(doc, mapping) {\n        let $pos = doc.resolve(mapping.map(this.head));\n        return GapCursor.valid($pos) ? new GapCursor($pos) : Selection.near($pos);\n    }\n    content() { return Slice.empty; }\n    eq(other) {\n        return other instanceof GapCursor && other.head == this.head;\n    }\n    toJSON() {\n        return { type: \"gapcursor\", pos: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for GapCursor.fromJSON\");\n        return new GapCursor(doc.resolve(json.pos));\n    }\n    /**\n    @internal\n    */\n    getBookmark() { return new GapBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static valid($pos) {\n        let parent = $pos.parent;\n        if (parent.isTextblock || !closedBefore($pos) || !closedAfter($pos))\n            return false;\n        let override = parent.type.spec.allowGapCursor;\n        if (override != null)\n            return override;\n        let deflt = parent.contentMatchAt($pos.index()).defaultType;\n        return deflt && deflt.isTextblock;\n    }\n    /**\n    @internal\n    */\n    static findGapCursorFrom($pos, dir, mustMove = false) {\n        search: for (;;) {\n            if (!mustMove && GapCursor.valid($pos))\n                return $pos;\n            let pos = $pos.pos, next = null;\n            // Scan up from this position\n            for (let d = $pos.depth;; d--) {\n                let parent = $pos.node(d);\n                if (dir > 0 ? $pos.indexAfter(d) < parent.childCount : $pos.index(d) > 0) {\n                    next = parent.child(dir > 0 ? $pos.indexAfter(d) : $pos.index(d) - 1);\n                    break;\n                }\n                else if (d == 0) {\n                    return null;\n                }\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            // And then down into the next node\n            for (;;) {\n                let inside = dir > 0 ? next.firstChild : next.lastChild;\n                if (!inside) {\n                    if (next.isAtom && !next.isText && !NodeSelection.isSelectable(next)) {\n                        $pos = $pos.doc.resolve(pos + next.nodeSize * dir);\n                        mustMove = false;\n                        continue search;\n                    }\n                    break;\n                }\n                next = inside;\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            return null;\n        }\n    }\n}\nGapCursor.prototype.visible = false;\nGapCursor.findFrom = GapCursor.findGapCursorFrom;\nSelection.jsonID(\"gapcursor\", GapCursor);\nclass GapBookmark {\n    constructor(pos) {\n        this.pos = pos;\n    }\n    map(mapping) {\n        return new GapBookmark(mapping.map(this.pos));\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.pos);\n        return GapCursor.valid($pos) ? new GapCursor($pos) : Selection.near($pos);\n    }\n}\nfunction closedBefore($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.index(d), parent = $pos.node(d);\n        // At the start of this parent, look at next one\n        if (index == 0) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        // See if the node before (or its first ancestor) is closed\n        for (let before = parent.child(index - 1);; before = before.lastChild) {\n            if ((before.childCount == 0 && !before.inlineContent) || before.isAtom || before.type.spec.isolating)\n                return true;\n            if (before.inlineContent)\n                return false;\n        }\n    }\n    // Hit start of document\n    return true;\n}\nfunction closedAfter($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.indexAfter(d), parent = $pos.node(d);\n        if (index == parent.childCount) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        for (let after = parent.child(index);; after = after.firstChild) {\n            if ((after.childCount == 0 && !after.inlineContent) || after.isAtom || after.type.spec.isolating)\n                return true;\n            if (after.inlineContent)\n                return false;\n        }\n    }\n    return true;\n}\n\n/**\nCreate a gap cursor plugin. When enabled, this will capture clicks\nnear and arrow-key-motion past places that don't have a normally\nselectable position nearby, and create a gap cursor selection for\nthem. The cursor is drawn as an element with class\n`ProseMirror-gapcursor`. You can either include\n`style/gapcursor.css` from the package's directory or add your own\nstyles to make it visible.\n*/\nfunction gapCursor() {\n    return new Plugin({\n        props: {\n            decorations: drawGapCursor,\n            createSelectionBetween(_view, $anchor, $head) {\n                return $anchor.pos == $head.pos && GapCursor.valid($head) ? new GapCursor($head) : null;\n            },\n            handleClick,\n            handleKeyDown,\n            handleDOMEvents: { beforeinput: beforeinput }\n        }\n    });\n}\nconst handleKeyDown = keydownHandler({\n    \"ArrowLeft\": arrow(\"horiz\", -1),\n    \"ArrowRight\": arrow(\"horiz\", 1),\n    \"ArrowUp\": arrow(\"vert\", -1),\n    \"ArrowDown\": arrow(\"vert\", 1)\n});\nfunction arrow(axis, dir) {\n    const dirStr = axis == \"vert\" ? (dir > 0 ? \"down\" : \"up\") : (dir > 0 ? \"right\" : \"left\");\n    return function (state, dispatch, view) {\n        let sel = state.selection;\n        let $start = dir > 0 ? sel.$to : sel.$from, mustMove = sel.empty;\n        if (sel instanceof TextSelection) {\n            if (!view.endOfTextblock(dirStr) || $start.depth == 0)\n                return false;\n            mustMove = false;\n            $start = state.doc.resolve(dir > 0 ? $start.after() : $start.before());\n        }\n        let $found = GapCursor.findGapCursorFrom($start, dir, mustMove);\n        if (!$found)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(new GapCursor($found)));\n        return true;\n    };\n}\nfunction handleClick(view, pos, event) {\n    if (!view || !view.editable)\n        return false;\n    let $pos = view.state.doc.resolve(pos);\n    if (!GapCursor.valid($pos))\n        return false;\n    let clickPos = view.posAtCoords({ left: event.clientX, top: event.clientY });\n    if (clickPos && clickPos.inside > -1 && NodeSelection.isSelectable(view.state.doc.nodeAt(clickPos.inside)))\n        return false;\n    view.dispatch(view.state.tr.setSelection(new GapCursor($pos)));\n    return true;\n}\n// This is a hack that, when a composition starts while a gap cursor\n// is active, quickly creates an inline context for the composition to\n// happen in, to avoid it being aborted by the DOM selection being\n// moved into a valid position.\nfunction beforeinput(view, event) {\n    if (event.inputType != \"insertCompositionText\" || !(view.state.selection instanceof GapCursor))\n        return false;\n    let { $from } = view.state.selection;\n    let insert = $from.parent.contentMatchAt($from.index()).findWrapping(view.state.schema.nodes.text);\n    if (!insert)\n        return false;\n    let frag = Fragment.empty;\n    for (let i = insert.length - 1; i >= 0; i--)\n        frag = Fragment.from(insert[i].createAndFill(null, frag));\n    let tr = view.state.tr.replace($from.pos, $from.pos, new Slice(frag, 0, 0));\n    tr.setSelection(TextSelection.near(tr.doc.resolve($from.pos + 1)));\n    view.dispatch(tr);\n    return false;\n}\nfunction drawGapCursor(state) {\n    if (!(state.selection instanceof GapCursor))\n        return null;\n    let node = document.createElement(\"div\");\n    node.className = \"ProseMirror-gapcursor\";\n    return DecorationSet.create(state.doc, [Decoration.widget(state.selection.head, node, { key: \"gapcursor\" })]);\n}\n\nexport { GapCursor, gapCursor };\n", "var GOOD_LEAF_SIZE = 200;\n\n// :: class<T> A rope sequence is a persistent sequence data structure\n// that supports appending, prepending, and slicing without doing a\n// full copy. It is represented as a mostly-balanced tree.\nvar RopeSequence = function RopeSequence () {};\n\nRopeSequence.prototype.append = function append (other) {\n  if (!other.length) { return this }\n  other = RopeSequence.from(other);\n\n  return (!this.length && other) ||\n    (other.length < GOOD_LEAF_SIZE && this.leafAppend(other)) ||\n    (this.length < GOOD_LEAF_SIZE && other.leafPrepend(this)) ||\n    this.appendInner(other)\n};\n\n// :: (union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Prepend an array or other rope to this one, returning a new rope.\nRopeSequence.prototype.prepend = function prepend (other) {\n  if (!other.length) { return this }\n  return RopeSequence.from(other).append(this)\n};\n\nRopeSequence.prototype.appendInner = function appendInner (other) {\n  return new Append(this, other)\n};\n\n// :: (?number, ?number) → RopeSequence<T>\n// Create a rope repesenting a sub-sequence of this rope.\nRopeSequence.prototype.slice = function slice (from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from >= to) { return RopeSequence.empty }\n  return this.sliceInner(Math.max(0, from), Math.min(this.length, to))\n};\n\n// :: (number) → T\n// Retrieve the element at the given position from this rope.\nRopeSequence.prototype.get = function get (i) {\n  if (i < 0 || i >= this.length) { return undefined }\n  return this.getInner(i)\n};\n\n// :: ((element: T, index: number) → ?bool, ?number, ?number)\n// Call the given function for each element between the given\n// indices. This tends to be more efficient than looping over the\n// indices and calling `get`, because it doesn't have to descend the\n// tree for every element.\nRopeSequence.prototype.forEach = function forEach (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from <= to)\n    { this.forEachInner(f, from, to, 0); }\n  else\n    { this.forEachInvertedInner(f, from, to, 0); }\n};\n\n// :: ((element: T, index: number) → U, ?number, ?number) → [U]\n// Map the given functions over the elements of the rope, producing\n// a flat array.\nRopeSequence.prototype.map = function map (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  var result = [];\n  this.forEach(function (elt, i) { return result.push(f(elt, i)); }, from, to);\n  return result\n};\n\n// :: (?union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Create a rope representing the given array, or return the rope\n// itself if a rope was given.\nRopeSequence.from = function from (values) {\n  if (values instanceof RopeSequence) { return values }\n  return values && values.length ? new Leaf(values) : RopeSequence.empty\n};\n\nvar Leaf = /*@__PURE__*/(function (RopeSequence) {\n  function Leaf(values) {\n    RopeSequence.call(this);\n    this.values = values;\n  }\n\n  if ( RopeSequence ) Leaf.__proto__ = RopeSequence;\n  Leaf.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Leaf.prototype.constructor = Leaf;\n\n  var prototypeAccessors = { length: { configurable: true },depth: { configurable: true } };\n\n  Leaf.prototype.flatten = function flatten () {\n    return this.values\n  };\n\n  Leaf.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    return new Leaf(this.values.slice(from, to))\n  };\n\n  Leaf.prototype.getInner = function getInner (i) {\n    return this.values[i]\n  };\n\n  Leaf.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    for (var i = from; i < to; i++)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    for (var i = from - 1; i >= to; i--)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.leafAppend = function leafAppend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(this.values.concat(other.flatten())) }\n  };\n\n  Leaf.prototype.leafPrepend = function leafPrepend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(other.flatten().concat(this.values)) }\n  };\n\n  prototypeAccessors.length.get = function () { return this.values.length };\n\n  prototypeAccessors.depth.get = function () { return 0 };\n\n  Object.defineProperties( Leaf.prototype, prototypeAccessors );\n\n  return Leaf;\n}(RopeSequence));\n\n// :: RopeSequence\n// The empty rope sequence.\nRopeSequence.empty = new Leaf([]);\n\nvar Append = /*@__PURE__*/(function (RopeSequence) {\n  function Append(left, right) {\n    RopeSequence.call(this);\n    this.left = left;\n    this.right = right;\n    this.length = left.length + right.length;\n    this.depth = Math.max(left.depth, right.depth) + 1;\n  }\n\n  if ( RopeSequence ) Append.__proto__ = RopeSequence;\n  Append.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Append.prototype.constructor = Append;\n\n  Append.prototype.flatten = function flatten () {\n    return this.left.flatten().concat(this.right.flatten())\n  };\n\n  Append.prototype.getInner = function getInner (i) {\n    return i < this.left.length ? this.left.get(i) : this.right.get(i - this.left.length)\n  };\n\n  Append.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from < leftLen &&\n        this.left.forEachInner(f, from, Math.min(to, leftLen), start) === false)\n      { return false }\n    if (to > leftLen &&\n        this.right.forEachInner(f, Math.max(from - leftLen, 0), Math.min(this.length, to) - leftLen, start + leftLen) === false)\n      { return false }\n  };\n\n  Append.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from > leftLen &&\n        this.right.forEachInvertedInner(f, from - leftLen, Math.max(to, leftLen) - leftLen, start + leftLen) === false)\n      { return false }\n    if (to < leftLen &&\n        this.left.forEachInvertedInner(f, Math.min(from, leftLen), to, start) === false)\n      { return false }\n  };\n\n  Append.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    var leftLen = this.left.length;\n    if (to <= leftLen) { return this.left.slice(from, to) }\n    if (from >= leftLen) { return this.right.slice(from - leftLen, to - leftLen) }\n    return this.left.slice(from, leftLen).append(this.right.slice(0, to - leftLen))\n  };\n\n  Append.prototype.leafAppend = function leafAppend (other) {\n    var inner = this.right.leafAppend(other);\n    if (inner) { return new Append(this.left, inner) }\n  };\n\n  Append.prototype.leafPrepend = function leafPrepend (other) {\n    var inner = this.left.leafPrepend(other);\n    if (inner) { return new Append(inner, this.right) }\n  };\n\n  Append.prototype.appendInner = function appendInner (other) {\n    if (this.left.depth >= Math.max(this.right.depth, other.depth) + 1)\n      { return new Append(this.left, new Append(this.right, other)) }\n    return new Append(this, other)\n  };\n\n  return Append;\n}(RopeSequence));\n\nexport default RopeSequence;\n", "import RopeSequence from 'rope-sequence';\nimport { Mapping } from 'prosemirror-transform';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Plugin } from 'prosemirror-state';\n\n// ProseMirror's history isn't simply a way to roll back to a previous\n// state, because ProseMirror supports applying changes without adding\n// them to the history (for example during collaboration).\n//\n// To this end, each 'Branch' (one for the undo history and one for\n// the redo history) keeps an array of 'Items', which can optionally\n// hold a step (an actual undoable change), and always hold a position\n// map (which is needed to move changes below them to apply to the\n// current document).\n//\n// An item that has both a step and a selection bookmark is the start\n// of an 'event' — a group of changes that will be undone or redone at\n// once. (It stores only the bookmark, since that way we don't have to\n// provide a document until the selection is actually applied, which\n// is useful when compressing.)\n// Used to schedule history compression\nconst max_empty_items = 500;\nclass Branch {\n    constructor(items, eventCount) {\n        this.items = items;\n        this.eventCount = eventCount;\n    }\n    // Pop the latest event off the branch's history and apply it\n    // to a document transform.\n    popEvent(state, preserveItems) {\n        if (this.eventCount == 0)\n            return null;\n        let end = this.items.length;\n        for (;; end--) {\n            let next = this.items.get(end - 1);\n            if (next.selection) {\n                --end;\n                break;\n            }\n        }\n        let remap, mapFrom;\n        if (preserveItems) {\n            remap = this.remapping(end, this.items.length);\n            mapFrom = remap.maps.length;\n        }\n        let transform = state.tr;\n        let selection, remaining;\n        let addAfter = [], addBefore = [];\n        this.items.forEach((item, i) => {\n            if (!item.step) {\n                if (!remap) {\n                    remap = this.remapping(end, i + 1);\n                    mapFrom = remap.maps.length;\n                }\n                mapFrom--;\n                addBefore.push(item);\n                return;\n            }\n            if (remap) {\n                addBefore.push(new Item(item.map));\n                let step = item.step.map(remap.slice(mapFrom)), map;\n                if (step && transform.maybeStep(step).doc) {\n                    map = transform.mapping.maps[transform.mapping.maps.length - 1];\n                    addAfter.push(new Item(map, undefined, undefined, addAfter.length + addBefore.length));\n                }\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n            }\n            else {\n                transform.maybeStep(item.step);\n            }\n            if (item.selection) {\n                selection = remap ? item.selection.map(remap.slice(mapFrom)) : item.selection;\n                remaining = new Branch(this.items.slice(0, end).append(addBefore.reverse().concat(addAfter)), this.eventCount - 1);\n                return false;\n            }\n        }, this.items.length, 0);\n        return { remaining: remaining, transform, selection: selection };\n    }\n    // Create a new branch with the given transform added.\n    addTransform(transform, selection, histOptions, preserveItems) {\n        let newItems = [], eventCount = this.eventCount;\n        let oldItems = this.items, lastItem = !preserveItems && oldItems.length ? oldItems.get(oldItems.length - 1) : null;\n        for (let i = 0; i < transform.steps.length; i++) {\n            let step = transform.steps[i].invert(transform.docs[i]);\n            let item = new Item(transform.mapping.maps[i], step, selection), merged;\n            if (merged = lastItem && lastItem.merge(item)) {\n                item = merged;\n                if (i)\n                    newItems.pop();\n                else\n                    oldItems = oldItems.slice(0, oldItems.length - 1);\n            }\n            newItems.push(item);\n            if (selection) {\n                eventCount++;\n                selection = undefined;\n            }\n            if (!preserveItems)\n                lastItem = item;\n        }\n        let overflow = eventCount - histOptions.depth;\n        if (overflow > DEPTH_OVERFLOW) {\n            oldItems = cutOffEvents(oldItems, overflow);\n            eventCount -= overflow;\n        }\n        return new Branch(oldItems.append(newItems), eventCount);\n    }\n    remapping(from, to) {\n        let maps = new Mapping;\n        this.items.forEach((item, i) => {\n            let mirrorPos = item.mirrorOffset != null && i - item.mirrorOffset >= from\n                ? maps.maps.length - item.mirrorOffset : undefined;\n            maps.appendMap(item.map, mirrorPos);\n        }, from, to);\n        return maps;\n    }\n    addMaps(array) {\n        if (this.eventCount == 0)\n            return this;\n        return new Branch(this.items.append(array.map(map => new Item(map))), this.eventCount);\n    }\n    // When the collab module receives remote changes, the history has\n    // to know about those, so that it can adjust the steps that were\n    // rebased on top of the remote changes, and include the position\n    // maps for the remote changes in its array of items.\n    rebased(rebasedTransform, rebasedCount) {\n        if (!this.eventCount)\n            return this;\n        let rebasedItems = [], start = Math.max(0, this.items.length - rebasedCount);\n        let mapping = rebasedTransform.mapping;\n        let newUntil = rebasedTransform.steps.length;\n        let eventCount = this.eventCount;\n        this.items.forEach(item => { if (item.selection)\n            eventCount--; }, start);\n        let iRebased = rebasedCount;\n        this.items.forEach(item => {\n            let pos = mapping.getMirror(--iRebased);\n            if (pos == null)\n                return;\n            newUntil = Math.min(newUntil, pos);\n            let map = mapping.maps[pos];\n            if (item.step) {\n                let step = rebasedTransform.steps[pos].invert(rebasedTransform.docs[pos]);\n                let selection = item.selection && item.selection.map(mapping.slice(iRebased + 1, pos));\n                if (selection)\n                    eventCount++;\n                rebasedItems.push(new Item(map, step, selection));\n            }\n            else {\n                rebasedItems.push(new Item(map));\n            }\n        }, start);\n        let newMaps = [];\n        for (let i = rebasedCount; i < newUntil; i++)\n            newMaps.push(new Item(mapping.maps[i]));\n        let items = this.items.slice(0, start).append(newMaps).append(rebasedItems);\n        let branch = new Branch(items, eventCount);\n        if (branch.emptyItemCount() > max_empty_items)\n            branch = branch.compress(this.items.length - rebasedItems.length);\n        return branch;\n    }\n    emptyItemCount() {\n        let count = 0;\n        this.items.forEach(item => { if (!item.step)\n            count++; });\n        return count;\n    }\n    // Compressing a branch means rewriting it to push the air (map-only\n    // items) out. During collaboration, these naturally accumulate\n    // because each remote change adds one. The `upto` argument is used\n    // to ensure that only the items below a given level are compressed,\n    // because `rebased` relies on a clean, untouched set of items in\n    // order to associate old items with rebased steps.\n    compress(upto = this.items.length) {\n        let remap = this.remapping(0, upto), mapFrom = remap.maps.length;\n        let items = [], events = 0;\n        this.items.forEach((item, i) => {\n            if (i >= upto) {\n                items.push(item);\n                if (item.selection)\n                    events++;\n            }\n            else if (item.step) {\n                let step = item.step.map(remap.slice(mapFrom)), map = step && step.getMap();\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n                if (step) {\n                    let selection = item.selection && item.selection.map(remap.slice(mapFrom));\n                    if (selection)\n                        events++;\n                    let newItem = new Item(map.invert(), step, selection), merged, last = items.length - 1;\n                    if (merged = items.length && items[last].merge(newItem))\n                        items[last] = merged;\n                    else\n                        items.push(newItem);\n                }\n            }\n            else if (item.map) {\n                mapFrom--;\n            }\n        }, this.items.length, 0);\n        return new Branch(RopeSequence.from(items.reverse()), events);\n    }\n}\nBranch.empty = new Branch(RopeSequence.empty, 0);\nfunction cutOffEvents(items, n) {\n    let cutPoint;\n    items.forEach((item, i) => {\n        if (item.selection && (n-- == 0)) {\n            cutPoint = i;\n            return false;\n        }\n    });\n    return items.slice(cutPoint);\n}\nclass Item {\n    constructor(\n    // The (forward) step map for this item.\n    map, \n    // The inverted step\n    step, \n    // If this is non-null, this item is the start of a group, and\n    // this selection is the starting selection for the group (the one\n    // that was active before the first step was applied)\n    selection, \n    // If this item is the inverse of a previous mapping on the stack,\n    // this points at the inverse's offset\n    mirrorOffset) {\n        this.map = map;\n        this.step = step;\n        this.selection = selection;\n        this.mirrorOffset = mirrorOffset;\n    }\n    merge(other) {\n        if (this.step && other.step && !other.selection) {\n            let step = other.step.merge(this.step);\n            if (step)\n                return new Item(step.getMap().invert(), step, this.selection);\n        }\n    }\n}\n// The value of the state field that tracks undo/redo history for that\n// state. Will be stored in the plugin state when the history plugin\n// is active.\nclass HistoryState {\n    constructor(done, undone, prevRanges, prevTime, prevComposition) {\n        this.done = done;\n        this.undone = undone;\n        this.prevRanges = prevRanges;\n        this.prevTime = prevTime;\n        this.prevComposition = prevComposition;\n    }\n}\nconst DEPTH_OVERFLOW = 20;\n// Record a transformation in undo history.\nfunction applyTransaction(history, state, tr, options) {\n    let historyTr = tr.getMeta(historyKey), rebased;\n    if (historyTr)\n        return historyTr.historyState;\n    if (tr.getMeta(closeHistoryKey))\n        history = new HistoryState(history.done, history.undone, null, 0, -1);\n    let appended = tr.getMeta(\"appendedTransaction\");\n    if (tr.steps.length == 0) {\n        return history;\n    }\n    else if (appended && appended.getMeta(historyKey)) {\n        if (appended.getMeta(historyKey).redo)\n            return new HistoryState(history.done.addTransform(tr, undefined, options, mustPreserveItems(state)), history.undone, rangesFor(tr.mapping.maps), history.prevTime, history.prevComposition);\n        else\n            return new HistoryState(history.done, history.undone.addTransform(tr, undefined, options, mustPreserveItems(state)), null, history.prevTime, history.prevComposition);\n    }\n    else if (tr.getMeta(\"addToHistory\") !== false && !(appended && appended.getMeta(\"addToHistory\") === false)) {\n        // Group transforms that occur in quick succession into one event.\n        let composition = tr.getMeta(\"composition\");\n        let newGroup = history.prevTime == 0 ||\n            (!appended && history.prevComposition != composition &&\n                (history.prevTime < (tr.time || 0) - options.newGroupDelay || !isAdjacentTo(tr, history.prevRanges)));\n        let prevRanges = appended ? mapRanges(history.prevRanges, tr.mapping) : rangesFor(tr.mapping.maps);\n        return new HistoryState(history.done.addTransform(tr, newGroup ? state.selection.getBookmark() : undefined, options, mustPreserveItems(state)), Branch.empty, prevRanges, tr.time, composition == null ? history.prevComposition : composition);\n    }\n    else if (rebased = tr.getMeta(\"rebased\")) {\n        // Used by the collab module to tell the history that some of its\n        // content has been rebased.\n        return new HistoryState(history.done.rebased(tr, rebased), history.undone.rebased(tr, rebased), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n    else {\n        return new HistoryState(history.done.addMaps(tr.mapping.maps), history.undone.addMaps(tr.mapping.maps), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n}\nfunction isAdjacentTo(transform, prevRanges) {\n    if (!prevRanges)\n        return false;\n    if (!transform.docChanged)\n        return true;\n    let adjacent = false;\n    transform.mapping.maps[0].forEach((start, end) => {\n        for (let i = 0; i < prevRanges.length; i += 2)\n            if (start <= prevRanges[i + 1] && end >= prevRanges[i])\n                adjacent = true;\n    });\n    return adjacent;\n}\nfunction rangesFor(maps) {\n    let result = [];\n    for (let i = maps.length - 1; i >= 0 && result.length == 0; i--)\n        maps[i].forEach((_from, _to, from, to) => result.push(from, to));\n    return result;\n}\nfunction mapRanges(ranges, mapping) {\n    if (!ranges)\n        return null;\n    let result = [];\n    for (let i = 0; i < ranges.length; i += 2) {\n        let from = mapping.map(ranges[i], 1), to = mapping.map(ranges[i + 1], -1);\n        if (from <= to)\n            result.push(from, to);\n    }\n    return result;\n}\n// Apply the latest event from one branch to the document and shift the event\n// onto the other branch.\nfunction histTransaction(history, state, redo) {\n    let preserveItems = mustPreserveItems(state);\n    let histOptions = historyKey.get(state).spec.config;\n    let pop = (redo ? history.undone : history.done).popEvent(state, preserveItems);\n    if (!pop)\n        return null;\n    let selection = pop.selection.resolve(pop.transform.doc);\n    let added = (redo ? history.done : history.undone).addTransform(pop.transform, state.selection.getBookmark(), histOptions, preserveItems);\n    let newHist = new HistoryState(redo ? added : pop.remaining, redo ? pop.remaining : added, null, 0, -1);\n    return pop.transform.setSelection(selection).setMeta(historyKey, { redo, historyState: newHist });\n}\nlet cachedPreserveItems = false, cachedPreserveItemsPlugins = null;\n// Check whether any plugin in the given state has a\n// `historyPreserveItems` property in its spec, in which case we must\n// preserve steps exactly as they came in, so that they can be\n// rebased.\nfunction mustPreserveItems(state) {\n    let plugins = state.plugins;\n    if (cachedPreserveItemsPlugins != plugins) {\n        cachedPreserveItems = false;\n        cachedPreserveItemsPlugins = plugins;\n        for (let i = 0; i < plugins.length; i++)\n            if (plugins[i].spec.historyPreserveItems) {\n                cachedPreserveItems = true;\n                break;\n            }\n    }\n    return cachedPreserveItems;\n}\n/**\nSet a flag on the given transaction that will prevent further steps\nfrom being appended to an existing history event (so that they\nrequire a separate undo command to undo).\n*/\nfunction closeHistory(tr) {\n    return tr.setMeta(closeHistoryKey, true);\n}\nconst historyKey = new PluginKey(\"history\");\nconst closeHistoryKey = new PluginKey(\"closeHistory\");\n/**\nReturns a plugin that enables the undo history for an editor. The\nplugin will track undo and redo stacks, which can be used with the\n[`undo`](https://prosemirror.net/docs/ref/#history.undo) and [`redo`](https://prosemirror.net/docs/ref/#history.redo) commands.\n\nYou can set an `\"addToHistory\"` [metadata\nproperty](https://prosemirror.net/docs/ref/#state.Transaction.setMeta) of `false` on a transaction\nto prevent it from being rolled back by undo.\n*/\nfunction history(config = {}) {\n    config = { depth: config.depth || 100,\n        newGroupDelay: config.newGroupDelay || 500 };\n    return new Plugin({\n        key: historyKey,\n        state: {\n            init() {\n                return new HistoryState(Branch.empty, Branch.empty, null, 0, -1);\n            },\n            apply(tr, hist, state) {\n                return applyTransaction(hist, state, tr, config);\n            }\n        },\n        config,\n        props: {\n            handleDOMEvents: {\n                beforeinput(view, e) {\n                    let inputType = e.inputType;\n                    let command = inputType == \"historyUndo\" ? undo : inputType == \"historyRedo\" ? redo : null;\n                    if (!command)\n                        return false;\n                    e.preventDefault();\n                    return command(view.state, view.dispatch);\n                }\n            }\n        }\n    });\n}\nfunction buildCommand(redo, scroll) {\n    return (state, dispatch) => {\n        let hist = historyKey.getState(state);\n        if (!hist || (redo ? hist.undone : hist.done).eventCount == 0)\n            return false;\n        if (dispatch) {\n            let tr = histTransaction(hist, state, redo);\n            if (tr)\n                dispatch(scroll ? tr.scrollIntoView() : tr);\n        }\n        return true;\n    };\n}\n/**\nA command function that undoes the last change, if any.\n*/\nconst undo = buildCommand(false, true);\n/**\nA command function that redoes the last undone change, if any.\n*/\nconst redo = buildCommand(true, true);\n/**\nA command function that undoes the last change. Don't scroll the\nselection into view.\n*/\nconst undoNoScroll = buildCommand(false, false);\n/**\nA command function that redoes the last undone change. Don't\nscroll the selection into view.\n*/\nconst redoNoScroll = buildCommand(true, false);\n/**\nThe amount of undoable events available in a given state.\n*/\nfunction undoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.done.eventCount : 0;\n}\n/**\nThe amount of redoable events available in a given editor state.\n*/\nfunction redoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.undone.eventCount : 0;\n}\n\nexport { closeHistory, history, redo, redoDepth, redoNoScroll, undo, undoDepth, undoNoScroll };\n", "import { Extension } from '@tiptap/core'\nimport type { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, PluginKey } from '@tiptap/pm/state'\n\nexport interface CharacterCountOptions {\n  /**\n   * The maximum number of characters that should be allowed. Defaults to `0`.\n   * @default null\n   * @example 180\n   */\n  limit: number | null | undefined\n  /**\n   * The mode by which the size is calculated. If set to `textSize`, the textContent of the document is used.\n   * If set to `nodeSize`, the nodeSize of the document is used.\n   * @default 'textSize'\n   * @example 'textSize'\n   */\n  mode: 'textSize' | 'nodeSize'\n  /**\n   * The text counter function to use. Defaults to a simple character count.\n   * @default (text) => text.length\n   * @example (text) => [...new Intl.Segmenter().segment(text)].length\n   */\n  textCounter: (text: string) => number\n  /**\n   * The word counter function to use. Defaults to a simple word count.\n   * @default (text) => text.split(' ').filter(word => word !== '').length\n   * @example (text) => text.split(/\\s+/).filter(word => word !== '').length\n   */\n  wordCounter: (text: string) => number\n}\n\nexport interface CharacterCountStorage {\n  /**\n   * Get the number of characters for the current document.\n   * @param options The options for the character count. (optional)\n   * @param options.node The node to get the characters from. Defaults to the current document.\n   * @param options.mode The mode by which the size is calculated. If set to `textSize`, the textContent of the document is used.\n   */\n  characters: (options?: { node?: ProseMirrorNode; mode?: 'textSize' | 'nodeSize' }) => number\n\n  /**\n   * Get the number of words for the current document.\n   * @param options The options for the character count. (optional)\n   * @param options.node The node to get the words from. Defaults to the current document.\n   */\n  words: (options?: { node?: ProseMirrorNode }) => number\n}\n\ndeclare module '@tiptap/core' {\n  interface Storage {\n    characterCount: CharacterCountStorage\n  }\n}\n\n/**\n * This extension allows you to count the characters and words of your document.\n * @see https://tiptap.dev/api/extensions/character-count\n */\nexport const CharacterCount = Extension.create<CharacterCountOptions, CharacterCountStorage>({\n  name: 'characterCount',\n\n  addOptions() {\n    return {\n      limit: null,\n      mode: 'textSize',\n      textCounter: text => text.length,\n      wordCounter: text => text.split(' ').filter(word => word !== '').length,\n    }\n  },\n\n  addStorage() {\n    return {\n      characters: () => 0,\n      words: () => 0,\n    }\n  },\n\n  onBeforeCreate() {\n    this.storage.characters = options => {\n      const node = options?.node || this.editor.state.doc\n      const mode = options?.mode || this.options.mode\n\n      if (mode === 'textSize') {\n        const text = node.textBetween(0, node.content.size, undefined, ' ')\n\n        return this.options.textCounter(text)\n      }\n\n      return node.nodeSize\n    }\n\n    this.storage.words = options => {\n      const node = options?.node || this.editor.state.doc\n      const text = node.textBetween(0, node.content.size, ' ', ' ')\n\n      return this.options.wordCounter(text)\n    }\n  },\n\n  addProseMirrorPlugins() {\n    let initialEvaluationDone = false\n\n    return [\n      new Plugin({\n        key: new PluginKey('characterCount'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (initialEvaluationDone) {\n            return\n          }\n\n          const limit = this.options.limit\n\n          if (limit === null || limit === undefined || limit === 0) {\n            initialEvaluationDone = true\n            return\n          }\n\n          const initialContentSize = this.storage.characters({ node: newState.doc })\n\n          if (initialContentSize > limit) {\n            const over = initialContentSize - limit\n            const from = 0\n            const to = over\n\n            console.warn(\n              `[CharacterCount] Initial content exceeded limit of ${limit} characters. Content was automatically trimmed.`,\n            )\n            const tr = newState.tr.deleteRange(from, to)\n\n            initialEvaluationDone = true\n            return tr\n          }\n\n          initialEvaluationDone = true\n        },\n        filterTransaction: (transaction, state) => {\n          const limit = this.options.limit\n\n          // Nothing has changed or no limit is defined. Ignore it.\n          if (!transaction.docChanged || limit === 0 || limit === null || limit === undefined) {\n            return true\n          }\n\n          const oldSize = this.storage.characters({ node: state.doc })\n          const newSize = this.storage.characters({ node: transaction.doc })\n\n          // Everything is in the limit. Good.\n          if (newSize <= limit) {\n            return true\n          }\n\n          // The limit has already been exceeded but will be reduced.\n          if (oldSize > limit && newSize > limit && newSize <= oldSize) {\n            return true\n          }\n\n          // The limit has already been exceeded and will be increased further.\n          if (oldSize > limit && newSize > limit && newSize > oldSize) {\n            return false\n          }\n\n          const isPaste = transaction.getMeta('paste')\n\n          // Block all exceeding transactions that were not pasted.\n          if (!isPaste) {\n            return false\n          }\n\n          // For pasted content, we try to remove the exceeding content.\n          const pos = transaction.selection.$head.pos\n          const over = newSize - limit\n          const from = pos - over\n          const to = pos\n\n          // It’s probably a bad idea to mutate transactions within `filterTransaction`\n          // but for now this is working fine.\n          transaction.deleteRange(from, to)\n\n          // In some situations, the limit will continue to be exceeded after trimming.\n          // This happens e.g. when truncating within a complex node (e.g. table)\n          // and ProseMirror has to close this node again.\n          // If this is the case, we prevent the transaction completely.\n          const updatedSize = this.storage.characters({ node: transaction.doc })\n\n          if (updatedSize > limit) {\n            return false\n          }\n\n          return true\n        },\n      }),\n    ]\n  },\n})\n", "import { Extension } from '@tiptap/core'\nimport { dropCursor } from '@tiptap/pm/dropcursor'\n\nexport interface DropcursorOptions {\n  /**\n   * The color of the drop cursor\n   * @default 'currentColor'\n   * @example 'red'\n   */\n  color: string | undefined\n\n  /**\n   * The width of the drop cursor\n   * @default 1\n   * @example 2\n   */\n  width: number | undefined\n\n  /**\n   * The class of the drop cursor\n   * @default undefined\n   * @example 'drop-cursor'\n   */\n  class: string | undefined\n}\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * in-between nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nexport const Dropcursor = Extension.create<DropcursorOptions>({\n  name: 'dropCursor',\n\n  addOptions() {\n    return {\n      color: 'currentColor',\n      width: 1,\n      class: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [dropCursor(this.options)]\n  },\n})\n", "import { Extension } from '@tiptap/core'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface FocusOptions {\n  /**\n   * The class name that should be added to the focused node.\n   * @default 'has-focus'\n   * @example 'is-focused'\n   */\n  className: string\n\n  /**\n   * The mode by which the focused node is determined.\n   * - All: All nodes are marked as focused.\n   * - Deepest: Only the deepest node is marked as focused.\n   * - Shallowest: Only the shallowest node is marked as focused.\n   *\n   * @default 'all'\n   * @example 'deepest'\n   * @example 'shallowest'\n   */\n  mode: 'all' | 'deepest' | 'shallowest'\n}\n\n/**\n * This extension allows you to add a class to the focused node.\n * @see https://www.tiptap.dev/api/extensions/focus\n */\nexport const Focus = Extension.create<FocusOptions>({\n  name: 'focus',\n\n  addOptions() {\n    return {\n      className: 'has-focus',\n      mode: 'all',\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('focus'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const { isEditable, isFocused } = this.editor\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!isEditable || !isFocused) {\n              return DecorationSet.create(doc, [])\n            }\n\n            // Maximum Levels\n            let maxLevels = 0\n\n            if (this.options.mode === 'deepest') {\n              doc.descendants((node, pos) => {\n                if (node.isText) {\n                  return\n                }\n\n                const isCurrent = anchor >= pos && anchor <= pos + node.nodeSize - 1\n\n                if (!isCurrent) {\n                  return false\n                }\n\n                maxLevels += 1\n              })\n            }\n\n            // Loop through current\n            let currentLevel = 0\n\n            doc.descendants((node, pos) => {\n              if (node.isText) {\n                return false\n              }\n\n              const isCurrent = anchor >= pos && anchor <= pos + node.nodeSize - 1\n\n              if (!isCurrent) {\n                return false\n              }\n\n              currentLevel += 1\n\n              const outOfScope =\n                (this.options.mode === 'deepest' && maxLevels - currentLevel > 0) ||\n                (this.options.mode === 'shallowest' && currentLevel > 1)\n\n              if (outOfScope) {\n                return this.options.mode === 'deepest'\n              }\n\n              decorations.push(\n                Decoration.node(pos, pos + node.nodeSize, {\n                  class: this.options.className,\n                }),\n              )\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import type { ParentConfig } from '@tiptap/core'\nimport { callOrReturn, Extension, getExtensionField } from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options>>['allowGapCursor']\n        }) => boolean | null)\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [gapCursor()]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n", "import type { Editor } from '@tiptap/core'\nimport { Extension, isNodeEmpty } from '@tiptap/core'\nimport type { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface PlaceholderOptions {\n  /**\n   * **The class name for the empty editor**\n   * @default 'is-editor-empty'\n   */\n  emptyEditorClass: string\n\n  /**\n   * **The class name for empty nodes**\n   * @default 'is-empty'\n   */\n  emptyNodeClass: string\n\n  /**\n   * **The placeholder content**\n   *\n   * You can use a function to return a dynamic placeholder or a string.\n   * @default 'Write something …'\n   */\n  placeholder:\n    | ((PlaceholderProps: { editor: Editor; node: ProsemirrorNode; pos: number; hasAnchor: boolean }) => string)\n    | string\n\n  /**\n   * **Checks if the placeholder should be only shown when the editor is editable.**\n   *\n   * If true, the placeholder will only be shown when the editor is editable.\n   * If false, the placeholder will always be shown.\n   * @default true\n   */\n  showOnlyWhenEditable: boolean\n\n  /**\n   * **Checks if the placeholder should be only shown when the current node is empty.**\n   *\n   * If true, the placeholder will only be shown when the current node is empty.\n   * If false, the placeholder will be shown when any node is empty.\n   * @default true\n   */\n  showOnlyCurrent: boolean\n\n  /**\n   * **Controls if the placeholder should be shown for all descendents.**\n   *\n   * If true, the placeholder will be shown for all descendents.\n   * If false, the placeholder will only be shown for the current node.\n   * @default false\n   */\n  includeChildren: boolean\n}\n\n/**\n * This extension allows you to add a placeholder to your editor.\n * A placeholder is a text that appears when the editor or a node is empty.\n * @see https://www.tiptap.dev/api/extensions/placeholder\n */\nexport const Placeholder = Extension.create<PlaceholderOptions>({\n  name: 'placeholder',\n\n  addOptions() {\n    return {\n      emptyEditorClass: 'is-editor-empty',\n      emptyNodeClass: 'is-empty',\n      placeholder: 'Write something …',\n      showOnlyWhenEditable: true,\n      showOnlyCurrent: true,\n      includeChildren: false,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('placeholder'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const active = this.editor.isEditable || !this.options.showOnlyWhenEditable\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!active) {\n              return null\n            }\n\n            const isEmptyDoc = this.editor.isEmpty\n\n            doc.descendants((node, pos) => {\n              const hasAnchor = anchor >= pos && anchor <= pos + node.nodeSize\n              const isEmpty = !node.isLeaf && isNodeEmpty(node)\n\n              if ((hasAnchor || !this.options.showOnlyCurrent) && isEmpty) {\n                const classes = [this.options.emptyNodeClass]\n\n                if (isEmptyDoc) {\n                  classes.push(this.options.emptyEditorClass)\n                }\n\n                const decoration = Decoration.node(pos, pos + node.nodeSize, {\n                  class: classes.join(' '),\n                  'data-placeholder':\n                    typeof this.options.placeholder === 'function'\n                      ? this.options.placeholder({\n                          editor: this.editor,\n                          node,\n                          pos,\n                          hasAnchor,\n                        })\n                      : this.options.placeholder,\n                })\n\n                decorations.push(decoration)\n              }\n\n              return this.options.includeChildren\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Extension, isNodeSelection } from '@tiptap/core'\nimport { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport type SelectionOptions = {\n  /**\n   * The class name that should be added to the selected text.\n   * @default 'selection'\n   * @example 'is-selected'\n   */\n  className: string\n}\n\n/**\n * This extension allows you to add a class to the selected text.\n * @see https://www.tiptap.dev/api/extensions/selection\n */\nexport const Selection = Extension.create({\n  name: 'selection',\n\n  addOptions() {\n    return {\n      className: 'selection',\n    }\n  },\n\n  addProseMirrorPlugins() {\n    const { editor, options } = this\n\n    return [\n      new Plugin({\n        key: new PluginKey('selection'),\n        props: {\n          decorations(state) {\n            if (\n              state.selection.empty ||\n              editor.isFocused ||\n              !editor.isEditable ||\n              isNodeSelection(state.selection) ||\n              editor.view.dragging\n            ) {\n              return null\n            }\n\n            return DecorationSet.create(state.doc, [\n              Decoration.inline(state.selection.from, state.selection.to, {\n                class: options.className,\n              }),\n            ])\n          },\n        },\n      }),\n    ]\n  },\n})\n\nexport default Selection\n", "import { Extension } from '@tiptap/core'\nimport type { Node, NodeType } from '@tiptap/pm/model'\nimport { Plug<PERSON>, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nfunction nodeEqualsType({ types, node }: { types: NodeType | NodeType[]; node: Node | null | undefined }) {\n  return (node && Array.isArray(types) && types.includes(node.type)) || node?.type === types\n}\n\n/**\n * Extension based on:\n * - https://github.com/ueberdosis/tiptap/blob/v1/packages/tiptap-extensions/src/extensions/TrailingNode.js\n * - https://github.com/remirror/remirror/blob/e0f1bec4a1e8073ce8f5500d62193e52321155b9/packages/prosemirror-trailing-node/src/trailing-node-plugin.ts\n */\n\nexport interface TrailingNodeOptions {\n  /**\n   * The node type that should be inserted at the end of the document.\n   * @note the node will always be added to the `notAfter` lists to\n   * prevent an infinite loop.\n   * @default 'paragraph'\n   */\n  node: string\n  /**\n   * The node types after which the trailing node should not be inserted.\n   * @default ['paragraph']\n   */\n  notAfter?: string | string[]\n}\n\n/**\n * This extension allows you to add an extra node at the end of the document.\n * @see https://www.tiptap.dev/api/extensions/trailing-node\n */\nexport const TrailingNode = Extension.create<TrailingNodeOptions>({\n  name: 'trailingNode',\n\n  addOptions() {\n    return {\n      node: 'paragraph',\n      notAfter: [],\n    }\n  },\n\n  addProseMirrorPlugins() {\n    const plugin = new PluginKey(this.name)\n    const disabledNodes = Object.entries(this.editor.schema.nodes)\n      .map(([, value]) => value)\n      .filter(node => (this.options.notAfter || []).concat(this.options.node).includes(node.name))\n\n    return [\n      new Plugin({\n        key: plugin,\n        appendTransaction: (_, __, state) => {\n          const { doc, tr, schema } = state\n          const shouldInsertNodeAtEnd = plugin.getState(state)\n          const endPosition = doc.content.size\n          const type = schema.nodes[this.options.node]\n\n          if (!shouldInsertNodeAtEnd) {\n            return\n          }\n\n          return tr.insert(endPosition, type.create())\n        },\n        state: {\n          init: (_, state) => {\n            const lastNode = state.tr.doc.lastChild\n\n            return !nodeEqualsType({ node: lastNode, types: disabledNodes })\n          },\n          apply: (tr, value) => {\n            if (!tr.docChanged) {\n              return value\n            }\n\n            const lastNode = tr.doc.lastChild\n\n            return !nodeEqualsType({ node: lastNode, types: disabledNodes })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Extension } from '@tiptap/core'\nimport { history, redo, undo } from '@tiptap/pm/history'\n\nexport interface UndoRedoOptions {\n  /**\n   * The amount of history events that are collected before the oldest events are discarded.\n   * @default 100\n   * @example 50\n   */\n  depth: number\n\n  /**\n   * The delay (in milliseconds) between changes after which a new group should be started.\n   * @default 500\n   * @example 1000\n   */\n  newGroupDelay: number\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    undoRedo: {\n      /**\n       * Undo recent changes\n       * @example editor.commands.undo()\n       */\n      undo: () => ReturnType\n      /**\n       * Reapply reverted changes\n       * @example editor.commands.redo()\n       */\n      redo: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/undo-redo\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `undo-redo` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nexport const UndoRedo = Extension.create<UndoRedoOptions>({\n  name: 'undoRedo',\n\n  addOptions() {\n    return {\n      depth: 100,\n      newGroupDelay: 500,\n    }\n  },\n\n  addCommands() {\n    return {\n      undo:\n        () =>\n        ({ state, dispatch }) => {\n          return undo(state, dispatch)\n        },\n      redo:\n        () =>\n        ({ state, dispatch }) => {\n          return redo(state, dispatch)\n        },\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [history(this.options)]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-z': () => this.editor.commands.undo(),\n      'Shift-Mod-z': () => this.editor.commands.redo(),\n      'Mod-y': () => this.editor.commands.redo(),\n\n      // Russian keyboard layouts\n      'Mod-я': () => this.editor.commands.undo(),\n      'Shift-Mod-я': () => this.editor.commands.redo(),\n    }\n  },\n})\n", "import { Extension } from '@tiptap/core'\nimport type { BlockquoteOptions } from '@tiptap/extension-blockquote'\nimport { Blockquote } from '@tiptap/extension-blockquote'\nimport type { BoldOptions } from '@tiptap/extension-bold'\nimport { Bold } from '@tiptap/extension-bold'\nimport type { CodeOptions } from '@tiptap/extension-code'\nimport { Code } from '@tiptap/extension-code'\nimport type { CodeBlockOptions } from '@tiptap/extension-code-block'\nimport { CodeBlock } from '@tiptap/extension-code-block'\nimport { Document } from '@tiptap/extension-document'\nimport type { HardBreakOptions } from '@tiptap/extension-hard-break'\nimport { HardBreak } from '@tiptap/extension-hard-break'\nimport type { HeadingOptions } from '@tiptap/extension-heading'\nimport { Heading } from '@tiptap/extension-heading'\nimport type { HorizontalRuleOptions } from '@tiptap/extension-horizontal-rule'\nimport { HorizontalRule } from '@tiptap/extension-horizontal-rule'\nimport type { ItalicOptions } from '@tiptap/extension-italic'\nimport { Italic } from '@tiptap/extension-italic'\nimport type { LinkOptions } from '@tiptap/extension-link'\nimport { Link } from '@tiptap/extension-link'\nimport type { BulletListOptions, ListItemOptions, ListKeymapOptions, OrderedListOptions } from '@tiptap/extension-list'\nimport { BulletList, ListItem, ListKeymap, OrderedList } from '@tiptap/extension-list'\nimport type { ParagraphOptions } from '@tiptap/extension-paragraph'\nimport { Paragraph } from '@tiptap/extension-paragraph'\nimport type { StrikeOptions } from '@tiptap/extension-strike'\nimport { Strike } from '@tiptap/extension-strike'\nimport { Text } from '@tiptap/extension-text'\nimport type { UnderlineOptions } from '@tiptap/extension-underline'\nimport { Underline } from '@tiptap/extension-underline'\nimport type { DropcursorOptions, TrailingNodeOptions, UndoRedoOptions } from '@tiptap/extensions'\nimport { Dropcursor, Gapcursor, TrailingNode, UndoRedo } from '@tiptap/extensions'\n\nexport interface StarterKitOptions {\n  /**\n   * If set to false, the blockquote extension will not be registered\n   * @example blockquote: false\n   */\n  blockquote: Partial<BlockquoteOptions> | false\n\n  /**\n   * If set to false, the bold extension will not be registered\n   * @example bold: false\n   */\n  bold: Partial<BoldOptions> | false\n\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example bulletList: false\n   */\n  bulletList: Partial<BulletListOptions> | false\n\n  /**\n   * If set to false, the code extension will not be registered\n   * @example code: false\n   */\n  code: Partial<CodeOptions> | false\n\n  /**\n   * If set to false, the codeBlock extension will not be registered\n   * @example codeBlock: false\n   */\n  codeBlock: Partial<CodeBlockOptions> | false\n\n  /**\n   * If set to false, the document extension will not be registered\n   * @example document: false\n   */\n  document: false\n\n  /**\n   * If set to false, the dropcursor extension will not be registered\n   * @example dropcursor: false\n   */\n  dropcursor: Partial<DropcursorOptions> | false\n\n  /**\n   * If set to false, the gapcursor extension will not be registered\n   * @example gapcursor: false\n   */\n  gapcursor: false\n\n  /**\n   * If set to false, the hardBreak extension will not be registered\n   * @example hardBreak: false\n   */\n  hardBreak: Partial<HardBreakOptions> | false\n\n  /**\n   * If set to false, the heading extension will not be registered\n   * @example heading: false\n   */\n  heading: Partial<HeadingOptions> | false\n\n  /**\n   * If set to false, the undo-redo extension will not be registered\n   * @example undoRedo: false\n   */\n  undoRedo: Partial<UndoRedoOptions> | false\n\n  /**\n   * If set to false, the horizontalRule extension will not be registered\n   * @example horizontalRule: false\n   */\n  horizontalRule: Partial<HorizontalRuleOptions> | false\n\n  /**\n   * If set to false, the italic extension will not be registered\n   * @example italic: false\n   */\n  italic: Partial<ItalicOptions> | false\n\n  /**\n   * If set to false, the listItem extension will not be registered\n   * @example listItem: false\n   */\n  listItem: Partial<ListItemOptions> | false\n\n  /**\n   * If set to false, the listItemKeymap extension will not be registered\n   * @example listKeymap: false\n   */\n  listKeymap: Partial<ListKeymapOptions> | false\n\n  /**\n   * If set to false, the link extension will not be registered\n   * @example link: false\n   */\n  link: Partial<LinkOptions> | false\n\n  /**\n   * If set to false, the orderedList extension will not be registered\n   * @example orderedList: false\n   */\n  orderedList: Partial<OrderedListOptions> | false\n\n  /**\n   * If set to false, the paragraph extension will not be registered\n   * @example paragraph: false\n   */\n  paragraph: Partial<ParagraphOptions> | false\n\n  /**\n   * If set to false, the strike extension will not be registered\n   * @example strike: false\n   */\n  strike: Partial<StrikeOptions> | false\n\n  /**\n   * If set to false, the text extension will not be registered\n   * @example text: false\n   */\n  text: false\n\n  /**\n   * If set to false, the underline extension will not be registered\n   * @example underline: false\n   */\n  underline: Partial<UnderlineOptions> | false\n\n  /**\n   * If set to false, the trailingNode extension will not be registered\n   * @example trailingNode: false\n   */\n  trailingNode: Partial<TrailingNodeOptions> | false\n}\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nexport const StarterKit = Extension.create<StarterKitOptions>({\n  name: 'starterKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bold !== false) {\n      extensions.push(Bold.configure(this.options.bold))\n    }\n\n    if (this.options.blockquote !== false) {\n      extensions.push(Blockquote.configure(this.options.blockquote))\n    }\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.code !== false) {\n      extensions.push(Code.configure(this.options.code))\n    }\n\n    if (this.options.codeBlock !== false) {\n      extensions.push(CodeBlock.configure(this.options.codeBlock))\n    }\n\n    if (this.options.document !== false) {\n      extensions.push(Document.configure(this.options.document))\n    }\n\n    if (this.options.dropcursor !== false) {\n      extensions.push(Dropcursor.configure(this.options.dropcursor))\n    }\n\n    if (this.options.gapcursor !== false) {\n      extensions.push(Gapcursor.configure(this.options.gapcursor))\n    }\n\n    if (this.options.hardBreak !== false) {\n      extensions.push(HardBreak.configure(this.options.hardBreak))\n    }\n\n    if (this.options.heading !== false) {\n      extensions.push(Heading.configure(this.options.heading))\n    }\n\n    if (this.options.undoRedo !== false) {\n      extensions.push(UndoRedo.configure(this.options.undoRedo))\n    }\n\n    if (this.options.horizontalRule !== false) {\n      extensions.push(HorizontalRule.configure(this.options.horizontalRule))\n    }\n\n    if (this.options.italic !== false) {\n      extensions.push(Italic.configure(this.options.italic))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.listKeymap !== false) {\n      extensions.push(ListKeymap.configure(this.options?.listKeymap))\n    }\n\n    if (this.options.link !== false) {\n      extensions.push(Link.configure(this.options?.link))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.paragraph !== false) {\n      extensions.push(Paragraph.configure(this.options.paragraph))\n    }\n\n    if (this.options.strike !== false) {\n      extensions.push(Strike.configure(this.options.strike))\n    }\n\n    if (this.options.text !== false) {\n      extensions.push(Text.configure(this.options.text))\n    }\n\n    if (this.options.underline !== false) {\n      extensions.push(Underline.configure(this.options?.underline))\n    }\n\n    if (this.options.trailingNode !== false) {\n      extensions.push(TrailingNode.configure(this.options?.trailingNode))\n    }\n\n    return extensions\n  },\n})\n", "import { StarterKit } from './starter-kit.js'\n\nexport type { StarterKitOptions } from './starter-kit.js'\nexport * from './starter-kit.js'\n\nexport default StarterKit\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCO,IAAM,IAAiB,CAAC,KAAK,eAAe;AAEjD,MAAI,QAAQ,QAAQ;AAClB,WAAO;EACT;AAGA,MAAI,eAAe,UAAU;AAC3B,WAAO,IAAI,UAAU;EACvB;AAEA,QAAM,EAAE,UAAU,GAAG,KAAK,IAAI,cAAA,OAAA,aAAc,CAAC;AAE7C,MAAI,QAAQ,OAAO;AACjB,UAAM,IAAI,MAAM,gFAAgF;EAClG;AAGA,SAAO,CAAC,KAAK,MAAM,QAAQ;AAC7B;;;ACxBO,IAAM,aAAa;AAMnB,IAAM,aAAa,MAAK,OAA0B;EACvD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,SAAS;EAET,OAAO;EAEP,UAAU;EAEV,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,aAAa,CAAC;EAC/B;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WACE,EAAC,cAAA,EAAY,GAAG,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GACzE,UAAA,EAAC,QAAA,CAAA,CAAK,EAAA,CACR;EAEJ;EAEA,cAAc;AACZ,WAAO;MACL,eACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,OAAO,KAAK,IAAI;MAClC;MACF,kBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,iBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,KAAK,KAAK,IAAI;MAChC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAiB;IAC7D;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,kBAAkB;QAChB,MAAM;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;AACF,CAAC;;;AEnEM,IAAM,iBAAiB;AAKvB,IAAM,iBAAiB;AAKvB,IAAM,uBAAuB;AAK7B,IAAM,uBAAuB;AAM7B,IAAM,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;MACA;QACE,KAAK;QACL,UAAU,CAAA,SAAS,KAAqB,MAAM,eAAe,YAAY;MAC3E;MACA;QACE,OAAO;QACP,WAAW,CAAA,SAAQ,KAAK,KAAK,SAAS,KAAK;MAC7C;MACA;QACE,OAAO;QACP,UAAU,CAAA,UAAS,4BAA4B,KAAK,KAAe,KAAK;MAC1E;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WACE,EAAC,UAAA,EAAQ,GAAG,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GACrE,UAAA,EAAC,QAAA,CAAA,CAAK,EAAA,CACR;EAEJ;EAEA,cAAc;AACZ,WAAO;MACL,SACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;MACF,YACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,WACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;MACrC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAW;MAC/C,SAAS,MAAM,KAAK,OAAO,SAAS,WAAW;IACjD;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;MACb,CAAC;MACD,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;MACb,CAAC;MACD,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;AACF,CAAC;;;AEzGM,IAAMA,cAAa;AAKnB,IAAM,aAAa;AAMnB,IAAM,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,UAAU;EAEV,MAAM;EAEN,UAAU;EAEV,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,OAAO,CAAC;EACzB;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EACjF;EAEA,cAAc;AACZ,WAAO;MACL,SACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;MACF,YACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,WACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;MACrC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAW;IACjD;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAMA;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;AACF,CAAC;;;AE7DM,IAAM,qBAAqB;AAK3B,IAAM,kBAAkB;AAMxB,IAAM,YAAY,MAAK,OAAyB;EACrD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,qBAAqB;MACrB,mBAAmB;MACnB,iBAAiB;MACjB,iBAAiB;MACjB,gBAAgB,CAAC;IACnB;EACF;EAEA,SAAS;EAET,OAAO;EAEP,OAAO;EAEP,MAAM;EAEN,UAAU;EAEV,gBAAgB;AACd,WAAO;MACL,UAAU;QACR,SAAS,KAAK,QAAQ;QACtB,WAAW,CAAA,YAAW;AA7F9B,cAAA;AA8FU,gBAAM,EAAE,oBAAoB,IAAI,KAAK;AACrC,gBAAM,aAAa,CAAC,KAAI,KAAA,QAAQ,sBAAR,OAAA,SAAA,GAA2B,cAAa,CAAC,CAAE;AACnE,gBAAM,YAAY,WACf,OAAO,CAAA,cAAa,UAAU,WAAW,mBAAmB,CAAC,EAC7D,IAAI,CAAA,cAAa,UAAU,QAAQ,qBAAqB,EAAE,CAAC;AAC9D,gBAAM,WAAW,UAAU,CAAC;AAE5B,cAAI,CAAC,UAAU;AACb,mBAAO;UACT;AAEA,iBAAO;QACT;QACA,UAAU;MACZ;IACF;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;QACL,oBAAoB;MACtB;IACF;EACF;EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,WAAO;MACL;MACA,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc;MAC3D;QACE;QACA;UACE,OAAO,KAAK,MAAM,WAAW,KAAK,QAAQ,sBAAsB,KAAK,MAAM,WAAW;QACxF;QACA;MACF;IACF;EACF;EAEA,cAAc;AACZ,WAAO;MACL,cACE,CAAA,eACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;MAC/C;MACF,iBACE,CAAA,eACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,MAAM,aAAa,UAAU;MAC/D;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,aAAa,MAAM,KAAK,OAAO,SAAS,gBAAgB;;MAGxD,WAAW,MAAM;AACf,cAAM,EAAE,OAAO,QAAQ,IAAI,KAAK,OAAO,MAAM;AAC7C,cAAM,YAAY,QAAQ,QAAQ;AAElC,YAAI,CAAC,SAAS,QAAQ,OAAO,KAAK,SAAS,KAAK,MAAM;AACpD,iBAAO;QACT;AAEA,YAAI,aAAa,CAAC,QAAQ,OAAO,YAAY,QAAQ;AACnD,iBAAO,KAAK,OAAO,SAAS,WAAW;QACzC;AAEA,eAAO;MACT;;MAGA,OAAO,CAAC,EAAE,OAAO,MAAM;AACrB,YAAI,CAAC,KAAK,QAAQ,mBAAmB;AACnC,iBAAO;QACT;AAEA,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,OAAO,MAAM,IAAI;AAEzB,YAAI,CAAC,SAAS,MAAM,OAAO,SAAS,KAAK,MAAM;AAC7C,iBAAO;QACT;AAEA,cAAM,UAAU,MAAM,iBAAiB,MAAM,OAAO,WAAW;AAC/D,cAAM,wBAAwB,MAAM,OAAO,YAAY,SAAS,MAAM;AAEtE,YAAI,CAAC,WAAW,CAAC,uBAAuB;AACtC,iBAAO;QACT;AAEA,eAAO,OACJ,MAAM,EACN,QAAQ,CAAC,EAAE,IAAAC,IAAG,MAAM;AACnB,UAAAA,IAAG,OAAO,MAAM,MAAM,GAAG,MAAM,GAAG;AAElC,iBAAO;QACT,CAAC,EACA,SAAS,EACT,IAAI;MACT;;MAGA,WAAW,CAAC,EAAE,OAAO,MAAM;AACzB,YAAI,CAAC,KAAK,QAAQ,iBAAiB;AACjC,iBAAO;QACT;AAEA,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,cAAM,EAAE,OAAO,MAAM,IAAI;AAEzB,YAAI,CAAC,SAAS,MAAM,OAAO,SAAS,KAAK,MAAM;AAC7C,iBAAO;QACT;AAEA,cAAM,UAAU,MAAM,iBAAiB,MAAM,OAAO,WAAW;AAE/D,YAAI,CAAC,SAAS;AACZ,iBAAO;QACT;AAEA,cAAM,QAAQ,MAAM,MAAM;AAE1B,YAAI,UAAU,QAAW;AACvB,iBAAO;QACT;AAEA,cAAM,YAAY,IAAI,OAAO,KAAK;AAElC,YAAI,WAAW;AACb,iBAAO,OAAO,SAAS,QAAQ,CAAC,EAAE,IAAAA,IAAG,MAAM;AACzC,YAAAA,IAAG,aAAa,UAAU,KAAK,IAAI,QAAQ,KAAK,CAAC,CAAC;AAClD,mBAAO;UACT,CAAC;QACH;AAEA,eAAO,OAAO,SAAS,SAAS;MAClC;IACF;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,uBAAuB;QACrB,MAAM;QACN,MAAM,KAAK;QACX,eAAe,CAAA,WAAU;UACvB,UAAU,MAAM,CAAC;QACnB;MACF,CAAC;MACD,uBAAuB;QACrB,MAAM;QACN,MAAM,KAAK;QACX,eAAe,CAAA,WAAU;UACvB,UAAU,MAAM,CAAC;QACnB;MACF,CAAC;IACH;EACF;EAEA,wBAAwB;AACtB,WAAO;;;MAGL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,wBAAwB;QAC3C,OAAO;UACL,aAAa,CAAC,MAAM,UAAU;AAC5B,gBAAI,CAAC,MAAM,eAAe;AACxB,qBAAO;YACT;AAGA,gBAAI,KAAK,OAAO,SAAS,KAAK,KAAK,IAAI,GAAG;AACxC,qBAAO;YACT;AAEA,kBAAM,OAAO,MAAM,cAAc,QAAQ,YAAY;AACrD,kBAAM,SAAS,MAAM,cAAc,QAAQ,oBAAoB;AAC/D,kBAAM,aAAa,SAAS,KAAK,MAAM,MAAM,IAAI;AACjD,kBAAM,WAAW,cAAA,OAAA,SAAA,WAAY;AAE7B,gBAAI,CAAC,QAAQ,CAAC,UAAU;AACtB,qBAAO;YACT;AAEA,kBAAM,EAAE,IAAAA,KAAI,OAAO,IAAI,KAAK;AAK5B,kBAAM,WAAW,OAAO,KAAK,KAAK,QAAQ,UAAU,IAAI,CAAC;AAIzD,YAAAA,IAAG,qBAAqB,KAAK,KAAK,OAAO,EAAE,SAAS,GAAG,QAAQ,CAAC;AAEhE,gBAAIA,IAAG,UAAU,MAAM,OAAO,SAAS,KAAK,MAAM;AAEhD,cAAAA,IAAG,aAAa,cAAc,KAAKA,IAAG,IAAI,QAAQ,KAAK,IAAI,GAAGA,IAAG,UAAU,OAAO,CAAC,CAAC,CAAC,CAAC;YACxF;AAKA,YAAAA,IAAG,QAAQ,SAAS,IAAI;AAExB,iBAAK,SAASA,GAAE;AAEhB,mBAAO;UACT;QACF;MACF,CAAC;IACH;EACF;AACF,CAAC;;;AEtTM,IAAM,WAAW,MAAK,OAAO;EAClC,MAAM;EACN,SAAS;EACT,SAAS;AACX,CAAC;;;AEwBM,IAAM,YAAY,MAAK,OAAyB;EACrD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,WAAW;MACX,gBAAgB,CAAC;IACnB;EACF;EAEA,QAAQ;EAER,OAAO;EAEP,YAAY;EAEZ,sBAAsB;EAEtB,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;EACvB;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;EAC5E;EAEA,aAAa;AACX,WAAO;EACT;EAEA,cAAc;AACZ,WAAO;MACL,cACE,MACA,CAAC,EAAE,UAAU,OAAO,OAAO,OAAO,MAAM;AACtC,eAAO,SAAS,MAAM;UACpB,MAAM,SAAS,SAAS;UACxB,MACE,SAAS,QAAQ,MAAM;AACrB,kBAAM,EAAE,WAAW,YAAY,IAAI;AAEnC,gBAAI,UAAU,MAAM,OAAO,KAAK,KAAK,WAAW;AAC9C,qBAAO;YACT;AAEA,kBAAM,EAAE,UAAU,IAAI,KAAK;AAC3B,kBAAM,EAAE,gBAAgB,IAAI,OAAO;AACnC,kBAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAM;AAElF,mBAAO,MAAM,EACV,cAAc,EAAE,MAAM,KAAK,KAAK,CAAC,EACjC,QAAQ,CAAC,EAAE,IAAAC,KAAI,SAAS,MAAM;AAC7B,kBAAI,YAAY,SAAS,WAAW;AAClC,sBAAM,gBAAgB,MAAM,OAAO,CAAA,SAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,gBAAAA,IAAG,YAAY,aAAa;cAC9B;AAEA,qBAAO;YACT,CAAC,EACA,IAAI;UACT,CAAC;QACL,CAAC;MACH;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa;MACrD,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa;IACzD;EACF;AACF,CAAC;;;AE7DM,IAAM,UAAU,MAAK,OAAuB;EACjD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;MACzB,gBAAgB,CAAC;IACnB;EACF;EAEA,SAAS;EAET,OAAO;EAEP,UAAU;EAEV,gBAAgB;AACd,WAAO;MACL,OAAO;QACL,SAAS;QACT,UAAU;MACZ;IACF;EACF;EAEA,YAAY;AACV,WAAO,KAAK,QAAQ,OAAO,IAAI,CAAC,WAAkB;MAChD,KAAK,IAAI,KAAK;MACd,OAAO,EAAE,MAAM;IACjB,EAAE;EACJ;EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,UAAM,WAAW,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM,KAAK;AAC9D,UAAM,QAAQ,WAAW,KAAK,MAAM,QAAQ,KAAK,QAAQ,OAAO,CAAC;AAEjE,WAAO,CAAC,IAAI,KAAK,IAAI,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EACtF;EAEA,cAAc;AACZ,WAAO;MACL,YACE,CAAA,eACA,CAAC,EAAE,SAAS,MAAM;AAChB,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;QACT;AAEA,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;MAC/C;MACF,eACE,CAAA,eACA,CAAC,EAAE,SAAS,MAAM;AAChB,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;QACT;AAEA,eAAO,SAAS,WAAW,KAAK,MAAM,aAAa,UAAU;MAC/D;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,OAAO;MACzB,CAAC,OAAO,WAAW;QACjB,GAAG;QACH,GAAG;UACD,CAAC,WAAW,KAAK,EAAE,GAAG,MAAM,KAAK,OAAO,SAAS,cAAc,EAAE,MAAM,CAAC;QAC1E;MACF;MACA,CAAC;IACH;EACF;EAEA,gBAAgB;AACd,WAAO,KAAK,QAAQ,OAAO,IAAI,CAAA,UAAS;AACtC,aAAO,uBAAuB;QAC5B,MAAM,IAAI,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,QAAQ;QACzE,MAAM,KAAK;QACX,eAAe;UACb;QACF;MACF,CAAC;IACH,CAAC;EACH;AACF,CAAC;;;AEvGM,IAAM,iBAAiB,MAAK,OAA8B;EAC/D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,OAAO;EAEP,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;EACvB;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;EAC5E;EAEA,cAAc;AACZ,WAAO;MACL,mBACE,MACA,CAAC,EAAE,OAAO,MAAM,MAAM;AAEpB,YAAI,CAAC,cAAc,OAAO,MAAM,OAAO,MAAM,KAAK,IAAI,CAAC,GAAG;AACxD,iBAAO;QACT;AAEA,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,KAAK,UAAU,IAAI;AAE3B,cAAM,eAAe,MAAM;AAE3B,YAAI,gBAAgB,SAAS,GAAG;AAC9B,uBAAa,gBAAgB,UAAU,KAAK;YAC1C,MAAM,KAAK;UACb,CAAC;QACH,OAAO;AACL,uBAAa,cAAc,EAAE,MAAM,KAAK,KAAK,CAAC;QAChD;AAEA,eACE,aAEG,QAAQ,CAAC,EAAE,IAAAC,KAAI,SAAS,MAAM;AAzE7C,cAAA;AA0EgB,cAAI,UAAU;AACZ,kBAAM,EAAE,IAAI,IAAIA,IAAG;AACnB,kBAAM,WAAW,IAAI,IAAI;AAEzB,gBAAI,IAAI,WAAW;AACjB,kBAAI,IAAI,UAAU,aAAa;AAC7B,gBAAAA,IAAG,aAAa,cAAc,OAAOA,IAAG,KAAK,IAAI,MAAM,CAAC,CAAC;cAC3D,WAAW,IAAI,UAAU,SAAS;AAChC,gBAAAA,IAAG,aAAa,cAAc,OAAOA,IAAG,KAAK,IAAI,GAAG,CAAC;cACvD,OAAO;AACL,gBAAAA,IAAG,aAAa,cAAc,OAAOA,IAAG,KAAK,IAAI,GAAG,CAAC;cACvD;YACF,OAAO;AAEL,oBAAM,QAAO,KAAA,IAAI,OAAO,KAAK,aAAa,gBAA7B,OAAA,SAAA,GAA0C,OAAA;AAEvD,kBAAI,MAAM;AACR,gBAAAA,IAAG,OAAO,UAAU,IAAI;AACxB,gBAAAA,IAAG,aAAa,cAAc,OAAOA,IAAG,KAAK,WAAW,CAAC,CAAC;cAC5D;YACF;AAEA,YAAAA,IAAG,eAAe;UACpB;AAEA,iBAAO;QACT,CAAC,EACA,IAAI;MAEX;IACJ;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;AACF,CAAC;;;AE/EM,IAAMC,kBAAiB;AAKvB,IAAMC,kBAAiB;AAKvB,IAAMC,wBAAuB;AAK7B,IAAMC,wBAAuB;AAM7B,IAAM,SAAS,KAAK,OAAsB;EAC/C,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;MACA;QACE,KAAK;QACL,UAAU,CAAA,SAAS,KAAqB,MAAM,cAAc,YAAY;MAC1E;MACA;QACE,OAAO;QACP,WAAW,CAAA,SAAQ,KAAK,KAAK,SAAS,KAAK;MAC7C;MACA;QACE,OAAO;MACT;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC/E;EAEA,cAAc;AACZ,WAAO;MACL,WACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;MACF,cACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,aACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;MACrC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,aAAa;MACjD,SAAS,MAAM,KAAK,OAAO,SAAS,aAAa;IACnD;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAMH;QACN,MAAM,KAAK;MACb,CAAC;MACD,cAAc;QACZ,MAAME;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAMD;QACN,MAAM,KAAK;MACb,CAAC;MACD,cAAc;QACZ,MAAME;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;AACF,CAAC;;;AE1ID,IAAM,cAAc;AAEpB,IAAM,eAAe;AAgBrB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,aAAa;AAQnB,SAAS,cAAc,MAAM,QAAQ;AACnC,MAAI,EAAE,QAAQ,SAAS;AACrB,WAAO,IAAI,IAAI,CAAC;AAAA,EAClB;AACA,SAAO,OAAO,IAAI;AACpB;AAQA,SAAS,YAAY,GAAG,OAAO,QAAQ;AACrC,MAAI,MAAM,OAAO,GAAG;AAClB,UAAM,YAAY,IAAI;AACtB,UAAM,YAAY,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,KAAK,GAAG;AAChB,UAAM,YAAY,IAAI;AACtB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,MAAI,MAAM,YAAY,GAAG;AACvB,UAAM,YAAY,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,KAAK,GAAG;AAChB,UAAM,YAAY,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,YAAY,GAAG;AACvB,UAAM,MAAM,IAAI;AAAA,EAClB;AACA,MAAI,MAAM,KAAK,GAAG;AAChB,UAAM,MAAM,IAAI;AAAA,EAClB;AACA,aAAW,KAAK,OAAO;AACrB,UAAM,QAAQ,cAAc,GAAG,MAAM;AACrC,QAAI,MAAM,QAAQ,CAAC,IAAI,GAAG;AACxB,YAAM,KAAK,CAAC;AAAA,IACd;AAAA,EACF;AACF;AAQA,SAAS,cAAc,GAAG,QAAQ;AAChC,QAAM,SAAS,CAAC;AAChB,aAAW,KAAK,QAAQ;AACtB,QAAI,OAAO,CAAC,EAAE,QAAQ,CAAC,KAAK,GAAG;AAC7B,aAAO,CAAC,IAAI;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAoBA,SAAS,MAAM,QAAQ,MAAM;AAG3B,OAAK,IAAI,CAAC;AAGV,OAAK,KAAK,CAAC;AAEX,OAAK,KAAK;AAEV,OAAK,IAAI;AACX;AAMA,MAAM,SAAS,CAAC;AAChB,MAAM,YAAY;AAAA,EAChB,UAAU;AACR,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,GAAG,OAAO;AACR,UAAM,QAAQ;AACd,UAAM,YAAY,MAAM,EAAE,KAAK;AAC/B,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,MAAM,GAAG,QAAQ,KAAK;AACxC,YAAM,QAAQ,MAAM,GAAG,CAAC,EAAE,CAAC;AAC3B,YAAMC,aAAY,MAAM,GAAG,CAAC,EAAE,CAAC;AAC/B,UAAIA,cAAa,MAAM,KAAK,KAAK,GAAG;AAClC,eAAOA;AAAA,MACT;AAAA,IACF;AAEA,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO,YAAY,OAAO;AAC5B,WAAO,YAAY,SAAS,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,GAAG,QAAQ,MAAM,OAAO,QAAQ;AAC9B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,GAAG,OAAO,CAAC,GAAG,MAAM,OAAO,MAAM;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,GAAGC,SAAQ,MAAM,OAAO,QAAQ;AAC9B,aAAS,UAAU,MAAM;AACzB,QAAI;AACJ,QAAI,QAAQ,KAAK,GAAG;AAClB,kBAAY;AAAA,IACd,OAAO;AAEL,kBAAY,IAAI,MAAM,IAAI;AAC1B,UAAI,SAAS,QAAQ;AACnB,oBAAY,MAAM,OAAO,MAAM;AAAA,MACjC;AAAA,IACF;AACA,SAAK,GAAG,KAAK,CAACA,SAAQ,SAAS,CAAC;AAChC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,GAAG,OAAO,MAAM,OAAO,QAAQ;AAC7B,QAAI,QAAQ;AACZ,UAAM,MAAM,MAAM;AAClB,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK;AAChC,cAAQ,MAAM,GAAG,MAAM,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO,MAAM,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,GAAG,OAAO,MAAM,OAAO,QAAQ;AAC7B,aAAS,UAAU,MAAM;AACzB,UAAM,QAAQ;AAGd,QAAI,QAAQ,KAAK,GAAG;AAClB,YAAM,EAAE,KAAK,IAAI;AACjB,aAAO;AAAA,IACT;AACA,UAAM,IAAI;AAIV,QAAI,WACF,gBAAgB,MAAM,GAAG,KAAK;AAChC,QAAI,eAAe;AACjB,kBAAY,IAAI,MAAM;AACtB,aAAO,OAAO,UAAU,GAAG,cAAc,CAAC;AAC1C,gBAAU,GAAG,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE;AACtD,gBAAU,KAAK,cAAc;AAC7B,gBAAU,IAAI,cAAc;AAAA,IAC9B,OAAO;AACL,kBAAY,IAAI,MAAM;AAAA,IACxB;AACA,QAAI,GAAG;AAEL,UAAI,QAAQ;AACV,YAAI,UAAU,KAAK,OAAO,UAAU,MAAM,UAAU;AAClD,gBAAM,WAAW,OAAO,OAAO,cAAc,UAAU,GAAG,MAAM,GAAG,KAAK;AACxE,sBAAY,GAAG,UAAU,MAAM;AAAA,QACjC,WAAW,OAAO;AAChB,sBAAY,GAAG,OAAO,MAAM;AAAA,QAC9B;AAAA,MACF;AACA,gBAAU,IAAI;AAAA,IAChB;AACA,UAAM,EAAE,KAAK,IAAI;AACjB,WAAO;AAAA,EACT;AACF;AAWA,IAAM,KAAK,CAAC,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,GAAG,OAAO,MAAM,OAAO,MAAM;AAUrF,IAAM,KAAK,CAAC,OAAOA,SAAQ,MAAM,OAAO,WAAW,MAAM,GAAGA,SAAQ,MAAM,OAAO,MAAM;AAUvF,IAAM,KAAK,CAAC,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,GAAG,OAAO,MAAM,OAAO,MAAM;AAUrF,IAAM,KAAK,CAAC,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,GAAG,OAAO,MAAM,OAAO,MAAM;AAQrF,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AAGvB,IAAM,YAAY;AAGlB,IAAM,MAAM;AAGZ,IAAM,OAAO;AAKb,IAAM,SAAS;AAKf,IAAM,eAAe;AAGrB,IAAM,MAAM;AAGZ,IAAM,KAAK;AAGX,IAAM,KAAK;AAKX,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,yBAAyB;AAC/B,IAAM,0BAA0B;AAChC,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAG7B,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,KAAK;AACX,IAAM,YAAY;AAClB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,MAAM;AACZ,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,qBAAqB;AAE3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,aAAa;AAGnB,IAAM,UAAU;AAGhB,IAAM,MAAM;AAEZ,IAAI,KAAkB,OAAO,OAAO;AAAA,EACnC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAGD,IAAM,eAAe;AACrB,IAAM,SAAS,WAAC,UAAM,GAAC;AACvB,IAAM,QAAQ,WAAC,cAAU,GAAC;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,QAAQ;AACd,IAAM,QAAQ;AAEd,IAAI,SAAsB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AACD,CAAC;AAOD,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAE3B,IAAI,OAAO;AAAX,IACE,QAAQ;AAuBV,SAAS,OAAO,gBAAgB,CAAC,GAAG;AAGlC,QAAM,SAAS,CAAC;AAChB,QAAM,SAAS;AAEf,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAI,QAAQ,MAAM;AAChB,WAAO,WAAW,WAAW;AAAA,EAC/B;AACA,MAAI,SAAS,MAAM;AACjB,YAAQ,WAAW,YAAY;AAAA,EACjC;AAGA,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,KAAK,SAAS;AACxB,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,KAAK,WAAW;AAC1B,KAAG,OAAO,KAAK,YAAY;AAC3B,KAAG,OAAO,KAAK,SAAS;AACxB,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,KAAK,gBAAgB;AAC/B,KAAG,OAAO,KAAK,iBAAiB;AAChC,KAAG,OAAO,KAAK,kBAAkB;AACjC,KAAG,OAAO,KAAK,mBAAmB;AAClC,KAAG,OAAO,KAAK,iBAAiB;AAChC,KAAG,OAAO,KAAK,kBAAkB;AACjC,KAAG,OAAO,KAAK,sBAAsB;AACrC,KAAG,OAAO,KAAK,uBAAuB;AACtC,KAAG,OAAO,KAAK,iBAAiB;AAChC,KAAG,OAAO,KAAK,oBAAoB;AACnC,KAAG,OAAO,KAAK,SAAS;AACxB,KAAG,OAAO,KAAK,QAAQ;AACvB,KAAG,OAAO,KAAK,EAAE;AACjB,KAAG,OAAO,KAAK,QAAQ;AACvB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,MAAM;AACrB,KAAG,OAAO,KAAK,GAAG;AAClB,KAAG,OAAO,KAAK,MAAM;AACrB,KAAG,OAAO,KAAK,WAAW;AAC1B,KAAG,OAAO,KAAK,MAAM;AACrB,KAAG,OAAO,KAAK,OAAO;AACtB,KAAG,OAAO,KAAK,IAAI;AACnB,KAAG,OAAO,KAAK,IAAI;AACnB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,IAAI;AACnB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,MAAM,SAAS;AACzB,KAAG,OAAO,KAAK,kBAAkB;AACjC,QAAM,MAAM,GAAG,OAAO,OAAO,KAAK;AAAA,IAChC,CAAC,OAAO,GAAG;AAAA,EACb,CAAC;AACD,KAAG,KAAK,OAAO,GAAG;AAClB,QAAM,eAAe,GAAG,KAAK,cAAc,gBAAgB;AAAA,IACzD,CAAC,YAAY,GAAG;AAAA,EAClB,CAAC;AACD,QAAM,eAAe,GAAG,KAAK,QAAQ,gBAAgB;AAAA,IACnD,CAAC,YAAY,GAAG;AAAA,EAClB,CAAC;AAGD,QAAM,OAAO,GAAG,OAAO,cAAc,MAAM;AAAA,IACzC,CAAC,KAAK,GAAG;AAAA,EACX,CAAC;AACD,KAAG,MAAM,OAAO,YAAY;AAC5B,KAAG,MAAM,cAAc,IAAI;AAC3B,KAAG,cAAc,OAAO,YAAY;AACpC,KAAG,cAAc,cAAc,YAAY;AAG3C,QAAM,QAAQ,GAAG,OAAO,QAAQ,OAAO;AAAA,IACrC,CAAC,KAAK,GAAG;AAAA,EACX,CAAC;AACD,KAAG,OAAO,YAAY;AACtB,KAAG,OAAO,OAAO,YAAY;AAC7B,KAAG,OAAO,QAAQ,KAAK;AACvB,KAAG,cAAc,OAAO,YAAY;AACpC,KAAG,cAAc,YAAY;AAC7B,KAAG,cAAc,QAAQ,YAAY;AAKrC,QAAMC,MAAK,GAAG,OAAO,IAAI,IAAI;AAAA,IAC3B,CAAC,UAAU,GAAG;AAAA,EAChB,CAAC;AACD,QAAM,KAAK,GAAG,OAAO,IAAI,IAAI;AAAA,IAC3B,CAAC,UAAU,GAAG;AAAA,EAChB,CAAC;AACD,QAAM,KAAK,GAAG,OAAO,OAAO,IAAI;AAAA,IAC9B,CAAC,UAAU,GAAG;AAAA,EAChB,CAAC;AACD,KAAG,OAAO,oBAAoB,EAAE;AAChC,KAAG,IAAI,IAAIA,GAAE;AACb,KAAG,IAAI,oBAAoB,EAAE;AAC7B,KAAG,IAAI,OAAO,EAAE;AAChB,KAAG,IAAI,EAAE;AACT,KAAG,IAAI,EAAE;AACT,KAAG,IAAI,OAAO,EAAE;AAChB,KAAG,IAAI,oBAAoB,EAAE;AAI7B,QAAM,QAAQ,GAAG,OAAO,OAAO,SAAS;AAAA,IACtC,CAAC,KAAK,GAAG;AAAA,EACX,CAAC;AACD,KAAG,OAAO,GAAG;AACb,KAAG,OAAO,OAAO,KAAK;AACtB,KAAG,OAAO,iBAAiB,KAAK;AAGhC,QAAM,cAAc,GAAG,OAAO,YAAY;AAC1C,KAAG,aAAa,GAAG;AACnB,KAAG,aAAa,OAAO,KAAK;AAK5B,QAAM,SAAS,CAAC,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,YAAY,CAAC;AAC3D,QAAM,UAAU,CAAC,CAAC,cAAc,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,YAAY,CAAC;AAC7E,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAO,OAAO,KAAK,CAAC,GAAG,KAAK,MAAM,MAAM;AAAA,EAC1C;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,WAAO,OAAO,MAAM,CAAC,GAAG,MAAM,OAAO,OAAO;AAAA,EAC9C;AACA,cAAY,KAAK;AAAA,IACf,KAAK;AAAA,IACL,OAAO;AAAA,EACT,GAAG,MAAM;AACT,cAAY,MAAM;AAAA,IAChB,MAAM;AAAA,IACN,OAAO;AAAA,EACT,GAAG,MAAM;AAKT,SAAO,OAAO,QAAQ,QAAQ,MAAM,MAAM;AAC1C,SAAO,OAAO,UAAU,QAAQ,MAAM,MAAM;AAC5C,SAAO,OAAO,QAAQ,cAAc,MAAM,MAAM;AAChD,SAAO,OAAO,SAAS,cAAc,MAAM,MAAM;AACjD,SAAO,OAAO,OAAO,cAAc,MAAM,MAAM;AAC/C,SAAO,OAAO,QAAQ,cAAc,MAAM,MAAM;AAChD,cAAY,QAAQ;AAAA,IAClB,QAAQ;AAAA,IACR,OAAO;AAAA,EACT,GAAG,MAAM;AACT,cAAY,cAAc;AAAA,IACxB,aAAa;AAAA,IACb,OAAO;AAAA,EACT,GAAG,MAAM;AAGT,kBAAgB,cAAc,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE;AACjE,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,UAAM,MAAM,cAAc,CAAC,EAAE,CAAC;AAC9B,UAAM,qBAAqB,cAAc,CAAC,EAAE,CAAC;AAC7C,UAAM,QAAQ,qBAAqB;AAAA,MACjC,CAAC,MAAM,GAAG;AAAA,IACZ,IAAI;AAAA,MACF,CAAC,WAAW,GAAG;AAAA,IACjB;AACA,QAAI,IAAI,QAAQ,GAAG,KAAK,GAAG;AACzB,YAAM,MAAM,IAAI;AAAA,IAClB,WAAW,CAAC,aAAa,KAAK,GAAG,GAAG;AAClC,YAAM,OAAO,IAAI;AAAA,IACnB,WAAW,MAAM,KAAK,GAAG,GAAG;AAC1B,YAAM,YAAY,IAAI;AAAA,IACxB,OAAO;AACL,YAAM,KAAK,IAAI;AAAA,IACjB;AACA,OAAG,OAAO,KAAK,KAAK,KAAK;AAAA,EAC3B;AAGA,KAAG,OAAO,aAAa,WAAW;AAAA,IAChC,OAAO;AAAA,EACT,CAAC;AAGD,QAAM,KAAK,IAAI,MAAM,GAAG;AACxB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ,OAAO,OAAO;AAAA,MACpB;AAAA,IACF,GAAG,EAAE;AAAA,EACP;AACF;AAWA,SAAS,MAAM,OAAO,KAAK;AAKzB,QAAM,WAAW,cAAc,IAAI,QAAQ,UAAU,OAAK,EAAE,YAAY,CAAC,CAAC;AAC1E,QAAM,YAAY,SAAS;AAC3B,QAAM,SAAS,CAAC;AAIhB,MAAI,SAAS;AAGb,MAAI,aAAa;AAGjB,SAAO,aAAa,WAAW;AAC7B,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,QAAI,oBAAoB;AACxB,WAAO,aAAa,cAAc,YAAY,MAAM,GAAG,SAAS,UAAU,CAAC,IAAI;AAC7E,cAAQ;AAGR,UAAI,MAAM,QAAQ,GAAG;AACnB,uBAAe;AACf,4BAAoB;AACpB,0BAAkB;AAAA,MACpB,WAAW,gBAAgB,GAAG;AAC5B,wBAAgB,SAAS,UAAU,EAAE;AACrC;AAAA,MACF;AACA,qBAAe,SAAS,UAAU,EAAE;AACpC,gBAAU,SAAS,UAAU,EAAE;AAC/B;AAAA,IACF;AAGA,cAAU;AACV,kBAAc;AACd,mBAAe;AAGf,WAAO,KAAK;AAAA,MACV,GAAG,gBAAgB;AAAA;AAAA,MAEnB,GAAG,IAAI,MAAM,SAAS,aAAa,MAAM;AAAA;AAAA,MAEzC,GAAG,SAAS;AAAA;AAAA,MAEZ,GAAG;AAAA;AAAA,IACL,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAaA,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,CAAC;AAChB,QAAM,MAAM,IAAI;AAChB,MAAI,QAAQ;AACZ,SAAO,QAAQ,KAAK;AAClB,QAAI,QAAQ,IAAI,WAAW,KAAK;AAChC,QAAI;AACJ,QAAI,OAAO,QAAQ,SAAU,QAAQ,SAAU,QAAQ,MAAM,QAAQ,SAAS,IAAI,WAAW,QAAQ,CAAC,KAAK,SAAU,SAAS,QAAS,IAAI,KAAK,IAC9I,IAAI,MAAM,OAAO,QAAQ,CAAC;AAC5B,WAAO,KAAK,IAAI;AAChB,aAAS,KAAK;AAAA,EAChB;AACA,SAAO;AACT;AAWA,SAAS,OAAO,OAAO,OAAO,GAAG,UAAU,IAAI;AAC7C,MAAI;AACJ,QAAM,MAAM,MAAM;AAClB,WAAS,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK;AAChC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,MAAM,EAAE,IAAI,GAAG;AACjB,aAAO,MAAM,EAAE,IAAI;AAAA,IACrB,OAAO;AACL,aAAO,IAAI,MAAM,QAAQ;AACzB,WAAK,KAAK,GAAG,MAAM;AACnB,YAAM,EAAE,IAAI,IAAI;AAAA,IAClB;AACA,YAAQ;AAAA,EACV;AACA,SAAO,IAAI,MAAM,CAAC;AAClB,OAAK,KAAK,GAAG,MAAM;AACnB,QAAM,EAAE,MAAM,MAAM,CAAC,CAAC,IAAI;AAC1B,SAAO;AACT;AAQA,SAAS,WAAW,SAAS;AAC3B,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,CAAC;AACf,MAAI,IAAI;AACR,MAAI,SAAS;AACb,SAAO,IAAI,QAAQ,QAAQ;AACzB,QAAI,gBAAgB;AACpB,WAAO,OAAO,QAAQ,QAAQ,IAAI,aAAa,CAAC,KAAK,GAAG;AACtD;AAAA,IACF;AACA,QAAI,gBAAgB,GAAG;AACrB,YAAM,KAAK,MAAM,KAAK,EAAE,CAAC;AACzB,eAAS,WAAW,SAAS,QAAQ,UAAU,GAAG,IAAI,aAAa,GAAG,EAAE,GAAG,WAAW,GAAG,YAAY;AACnG,cAAM,IAAI;AAAA,MACZ;AACA,WAAK;AAAA,IACP,OAAO;AACL,YAAM,KAAK,QAAQ,CAAC,CAAC;AACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAmFA,IAAM,WAAW;AAAA,EACf,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY,CAAC;AAAA,EACb,QAAQ;AACV;AAYA,SAAS,QAAQ,MAAM,gBAAgB,MAAM;AAC3C,MAAI,IAAI,OAAO,OAAO,CAAC,GAAG,QAAQ;AAClC,MAAI,MAAM;AACR,QAAI,OAAO,OAAO,GAAG,gBAAgB,UAAU,KAAK,IAAI,IAAI;AAAA,EAC9D;AAGA,QAAM,cAAc,EAAE;AACtB,QAAM,uBAAuB,CAAC;AAC9B,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,yBAAqB,KAAK,YAAY,CAAC,EAAE,YAAY,CAAC;AAAA,EACxD;AAEA,OAAK,IAAI;AACT,MAAI,eAAe;AACjB,SAAK,gBAAgB;AAAA,EACvB;AACA,OAAK,aAAa;AACpB;AACA,QAAQ,YAAY;AAAA,EAClB,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,cAAc,IAAI;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO;AACX,WAAO,KAAK,IAAI,YAAY,MAAM,SAAS,GAAG,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,KAAK,UAAU,OAAO;AACxB,UAAM,aAAa,YAAY;AAC/B,QAAI,SAAS,KAAK,EAAE,GAAG;AACvB,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,eAAS,MAAM,KAAK,SAAS,OAAO,MAAM,CAAC,IAAI,SAAS,GAAG;AAC3D,UAAI,OAAO,WAAW,cAAc,YAAY;AAC9C,iBAAS,OAAO,UAAU,KAAK;AAAA,MACjC;AAAA,IACF,WAAW,OAAO,WAAW,cAAc,YAAY;AACrD,eAAS,OAAO,UAAU,MAAM,GAAG,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK,UAAU,OAAO;AAC3B,QAAI,MAAM,KAAK,EAAE,GAAG;AACpB,QAAI,OAAO,QAAQ,cAAc,YAAY,MAAM;AACjD,YAAM,IAAI,UAAU,MAAM,GAAG,KAAK;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO;AACZ,UAAM,KAAK,MAAM,OAAO,IAAI;AAC5B,UAAM,WAAW,KAAK,IAAI,UAAU,MAAM,KAAK,KAAK,KAAK;AACzD,WAAO,SAAS,IAAI,MAAM,GAAG,KAAK;AAAA,EACpC;AACF;AACA,SAAS,KAAK,KAAK;AACjB,SAAO;AACT;AAEA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA;AACD,CAAC;AAWD,SAAS,WAAW,OAAO,QAAQ;AACjC,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,KAAK;AACZ;AAeA,WAAW,YAAY;AAAA,EACrB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAOC,SAAQ;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBC,UAAS;AACzB,UAAM,MAAM,KAAK,SAAS;AAC1B,UAAM,WAAWA,SAAQ,IAAI,YAAY,KAAK,IAAI;AAClD,UAAM,YAAYA,SAAQ,IAAI,UAAU,KAAK,IAAI;AACjD,WAAO,YAAY,UAAU,SAAS,WAAW,UAAU,UAAU,GAAG,QAAQ,IAAI,MAAM;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgBA,UAAS;AACvB,WAAOA,SAAQ,IAAI,cAAc,KAAK,OAAOA,SAAQ,IAAI,iBAAiB,CAAC,GAAG,IAAI;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,WAAO,KAAK,GAAG,CAAC,EAAE;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC,EAAE;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS,WAAW,SAAS,iBAAiB;AAC5C,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,SAAS;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK,OAAO,QAAQ;AAAA,MAC1B,OAAO,KAAK,WAAW;AAAA,MACvB,KAAK,KAAK,SAAS;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBA,UAAS;AACzB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,kBAAkBA,QAAO;AAAA,MACrC,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK,gBAAgBA,QAAO;AAAA,MAClC,OAAO,KAAK,WAAW;AAAA,MACvB,KAAK,KAAK,SAAS;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAASA,UAAS;AAChB,WAAOA,SAAQ,IAAI,YAAY,KAAK,SAAS,GAAG,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAOA,UAAS;AACd,UAAM,QAAQ;AACd,UAAM,OAAO,KAAK,OAAOA,SAAQ,IAAI,iBAAiB,CAAC;AACvD,UAAM,gBAAgBA,SAAQ,IAAI,cAAc,MAAM,IAAI;AAC1D,UAAM,UAAUA,SAAQ,IAAI,WAAW,MAAM,KAAK;AAClD,UAAM,UAAU,KAAK,kBAAkBA,QAAO;AAC9C,UAAM,aAAa,CAAC;AACpB,UAAM,YAAYA,SAAQ,IAAI,aAAa,MAAM,KAAK;AACtD,UAAM,SAASA,SAAQ,IAAI,UAAU,MAAM,KAAK;AAChD,UAAM,MAAMA,SAAQ,IAAI,OAAO,MAAM,KAAK;AAC1C,UAAM,QAAQA,SAAQ,OAAO,cAAc,MAAM,KAAK;AACtD,UAAM,iBAAiBA,SAAQ,OAAO,UAAU,MAAM,KAAK;AAC3D,eAAW,OAAO;AAClB,QAAI,WAAW;AACb,iBAAW,QAAQ;AAAA,IACrB;AACA,QAAI,QAAQ;AACV,iBAAW,SAAS;AAAA,IACtB;AACA,QAAI,KAAK;AACP,iBAAW,MAAM;AAAA,IACnB;AACA,QAAI,OAAO;AACT,aAAO,OAAO,YAAY,KAAK;AAAA,IACjC;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAQA,SAAS,iBAAiB,MAAM,OAAO;AAAA,EACrC,MAAM,cAAc,WAAW;AAAA,IAC7B,YAAY,OAAO,QAAQ;AACzB,YAAM,OAAO,MAAM;AACnB,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AACA,aAAW,KAAK,OAAO;AACrB,UAAM,UAAU,CAAC,IAAI,MAAM,CAAC;AAAA,EAC9B;AACA,QAAM,IAAI;AACV,SAAO;AACT;AAKA,IAAM,QAAQ,iBAAiB,SAAS;AAAA,EACtC,QAAQ;AAAA,EACR,SAAS;AACP,WAAO,YAAY,KAAK,SAAS;AAAA,EACnC;AACF,CAAC;AAKD,IAAM,OAAO,iBAAiB,MAAM;AAMpC,IAAM,KAAK,iBAAiB,IAAI;AAMhC,IAAM,MAAM,iBAAiB,OAAO;AAAA,EAClC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQR,OAAOD,UAAS,SAAS,iBAAiB;AAExC,WAAO,KAAK,YAAY,IAAI,KAAK,IAAI,GAAGA,OAAM,MAAM,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,UAAM,SAAS,KAAK;AACpB,WAAO,OAAO,UAAU,KAAK,OAAO,CAAC,EAAE,MAAM,aAAa,OAAO,CAAC,EAAE,MAAM;AAAA,EAC5E;AACF,CAAC;AAED,IAAI,QAAqB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAiBD,IAAM,YAAY,SAAO,IAAI,MAAM,GAAG;AAMtC,SAAS,OAAO;AAAA,EACd;AACF,GAAG;AAED,QAAM,cAAc,OAAO,OAAO,OAAO,CAAC,WAAW,UAAU,IAAI,WAAW,UAAU,OAAO,QAAQ,QAAQ,QAAQ,KAAK,SAAS,MAAM,MAAM,OAAO,OAAO,KAAK,OAAO,UAAU,CAAC;AAKtL,QAAM,iBAAiB,CAAC,YAAY,OAAO,OAAO,KAAK,aAAa,SAAS,OAAO,OAAO,MAAM,kBAAkB,mBAAmB,WAAW,YAAY,cAAc,aAAa,WAAW,YAAY,oBAAoB,qBAAqB,mBAAmB,oBAAoB,wBAAwB,yBAAyB,mBAAmB,oBAAoB;AAIvX,QAAM,qBAAqB,CAAC,WAAW,YAAY,UAAU,WAAW,UAAU,OAAO,QAAQ,QAAQ,QAAQ,WAAW,YAAY,SAAS,MAAM,MAAM,OAAO,OAAO,OAAO,KAAK,OAAO,UAAU;AAMxM,QAAM,QAAQ,UAAU;AACxB,QAAM,YAAY,GAAG,OAAO,KAAK;AACjC,KAAG,WAAW,oBAAoB,SAAS;AAC3C,KAAG,WAAW,OAAO,QAAQ,SAAS;AACtC,QAAM,SAAS,UAAU,GACvB,SAAS,UAAU,GACnB,cAAc,UAAU;AAC1B,KAAG,OAAO,OAAO,QAAQ,MAAM;AAC/B,KAAG,OAAO,OAAO,QAAQ,MAAM;AAC/B,KAAG,OAAO,OAAO,aAAa,WAAW;AAEzC,KAAG,QAAQ,oBAAoB,SAAS;AACxC,KAAG,QAAQ,OAAO,QAAQ,MAAM;AAChC,QAAM,cAAc,GAAG,QAAQ,EAAE;AAEjC,KAAG,WAAW,IAAI,WAAW;AAG7B,KAAG,QAAQ,IAAI,WAAW;AAC1B,KAAG,aAAa,IAAI,WAAW;AAC/B,QAAM,eAAe,GAAG,WAAW,GAAG;AACtC,KAAG,cAAc,oBAAoB,SAAS;AAC9C,KAAG,cAAc,OAAO,QAAQ,SAAS;AACzC,QAAM,cAAc,UAAU;AAC9B,KAAG,aAAa,OAAO,QAAQ,WAAW;AAC1C,KAAG,aAAa,OAAO,QAAQ,WAAW;AAC1C,QAAM,iBAAiB,GAAG,aAAa,GAAG;AAC1C,KAAG,gBAAgB,OAAO,QAAQ,WAAW;AAC7C,QAAM,UAAU,UAAU,KAAK;AAC/B,KAAG,gBAAgB,OAAO,KAAK,OAAO;AACtC,KAAG,gBAAgB,OAAO,MAAM,OAAO;AACvC,KAAG,aAAa,WAAW,OAAO;AAGlC,QAAM,oBAAoB,GAAG,aAAa,MAAM;AAChD,KAAG,mBAAmB,QAAQ,iBAAiB;AAC/C,KAAG,mBAAmB,OAAO,QAAQ,WAAW;AAChD,KAAG,SAAS,OAAO,QAAQ,WAAW;AACtC,KAAG,SAAS,KAAK,cAAc;AAC/B,KAAG,SAAS,QAAQ,iBAAiB;AAGrC,QAAM,aAAa,GAAG,SAAS,KAAK;AAEpC,KAAG,YAAY,OAAO,SAAS,KAAK;AAIpC,QAAM,eAAe,GAAG,QAAQ,MAAM;AACtC,QAAM,YAAY,GAAG,QAAQ,GAAG;AAChC,KAAG,cAAc,QAAQ,YAAY;AACrC,KAAG,cAAc,OAAO,QAAQ,MAAM;AACtC,KAAG,WAAW,oBAAoB,SAAS;AAC3C,KAAG,WAAW,OAAO,QAAQ,MAAM;AACnC,QAAM,eAAe,UAAU,GAAG;AAClC,KAAG,WAAW,OAAO,KAAK,YAAY;AACtC,KAAG,WAAW,OAAO,MAAM,YAAY;AACvC,KAAG,cAAc,OAAO,QAAQ,MAAM;AACtC,KAAG,cAAc,oBAAoB,SAAS;AAC9C,KAAG,cAAc,KAAK,SAAS;AAC/B,KAAG,cAAc,QAAQ,YAAY;AACrC,KAAG,cAAc,IAAI,WAAW;AAChC,QAAM,oBAAoB,GAAG,cAAc,KAAK;AAChD,QAAM,wBAAwB,UAAU,GAAG;AAC3C,KAAG,mBAAmB,OAAO,SAAS,qBAAqB;AAG3D,QAAM,QAAQ,UAAU,GAAG;AAG3B,QAAM,eAAe,UAAU;AAG/B,KAAG,OAAO,aAAa,KAAK;AAC5B,KAAG,OAAO,gBAAgB,YAAY;AACtC,KAAG,cAAc,aAAa,KAAK;AACnC,KAAG,cAAc,gBAAgB,YAAY;AAI7C,KAAG,cAAc,OAAO,KAAK;AAC7B,KAAG,uBAAuB,OAAO,KAAK;AAGtC,QAAM,cAAc,GAAG,QAAQ,KAAK;AACpC,QAAM,mBAAmB,GAAG,aAAa,KAAK;AAC9C,QAAM,wBAAwB,GAAG,kBAAkB,KAAK;AAExD,QAAM,YAAY,GAAG,uBAAuB,KAAK;AAGjD,KAAG,QAAQ,OAAO,QAAQ,MAAM;AAChC,KAAG,QAAQ,KAAK,SAAS;AACzB,KAAG,QAAQ,QAAQ,YAAY;AAC/B,KAAG,aAAa,OAAO,QAAQ,MAAM;AACrC,KAAG,aAAa,KAAK,SAAS;AAC9B,KAAG,aAAa,QAAQ,YAAY;AAGpC,KAAG,aAAa,OAAO,QAAQ,KAAK;AACpC,KAAG,aAAa,OAAO,KAAK;AAC5B,KAAG,aAAa,OAAO,KAAK;AAC5B,KAAG,WAAW,OAAO,QAAQ,KAAK;AAClC,KAAG,WAAW,aAAa,KAAK;AAChC,KAAG,WAAW,OAAO,KAAK;AAC1B,QAAM,eAAe;AAAA,IAAC,CAAC,WAAW,UAAU;AAAA;AAAA,IAE5C,CAAC,aAAa,YAAY;AAAA;AAAA,IAE1B,CAAC,WAAW,UAAU;AAAA;AAAA,IAEtB,CAAC,kBAAkB,iBAAiB;AAAA;AAAA,IAEpC,CAAC,oBAAoB,mBAAmB;AAAA;AAAA,IAExC,CAAC,mBAAmB,kBAAkB;AAAA;AAAA,IAEtC,CAAC,wBAAwB,uBAAuB;AAAA;AAAA,IAEhD,CAAC,mBAAmB,oBAAoB;AAAA;AAAA,EACxC;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAM,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC;AACpC,UAAM,UAAU,GAAG,OAAO,IAAI;AAG9B,OAAG,cAAc,MAAM,OAAO;AAG9B,OAAG,SAAS,OAAO,KAAK;AAKxB,UAAM,WAAW,UAAU,GAAG;AAC9B,OAAG,SAAS,aAAa,QAAQ;AACjC,UAAM,cAAc,UAAU;AAC9B,OAAG,SAAS,cAAc;AAG1B,OAAG,UAAU,aAAa,QAAQ;AAClC,OAAG,UAAU,gBAAgB,WAAW;AACxC,OAAG,aAAa,aAAa,QAAQ;AACrC,OAAG,aAAa,gBAAgB,WAAW;AAG3C,OAAG,UAAU,OAAO,KAAK;AACzB,OAAG,aAAa,OAAO,KAAK;AAAA,EAC9B;AACA,KAAG,OAAO,WAAW,YAAY;AACjC,KAAG,OAAO,IAAI,EAAE;AAEhB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AAYA,SAAS,IAAI,OAAO,OAAO,QAAQ;AACjC,MAAI,MAAM,OAAO;AACjB,MAAI,SAAS;AACb,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,CAAC;AAClB,SAAO,SAAS,KAAK;AACnB,QAAI,QAAQ;AACZ,QAAI,cAAc;AAClB,QAAI,YAAY;AAChB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,WAAO,SAAS,OAAO,EAAE,cAAc,MAAM,GAAG,OAAO,MAAM,EAAE,CAAC,IAAI;AAGlE,iBAAW,KAAK,OAAO,QAAQ,CAAC;AAAA,IAClC;AACA,WAAO,SAAS,QAAQ,YAAY,eAAe,MAAM,GAAG,OAAO,MAAM,EAAE,CAAC,IAAI;AAE9E,oBAAc;AACd,cAAQ;AAGR,UAAI,MAAM,QAAQ,GAAG;AACnB,uBAAe;AACf,0BAAkB;AAAA,MACpB,WAAW,gBAAgB,GAAG;AAC5B;AAAA,MACF;AACA;AACA;AAAA,IACF;AACA,QAAI,eAAe,GAAG;AAIpB,gBAAU;AACV,UAAI,SAAS,KAAK;AAChB,mBAAW,KAAK,OAAO,MAAM,CAAC;AAC9B;AAAA,MACF;AAAA,IACF,OAAO;AAGL,UAAI,WAAW,SAAS,GAAG;AACzB,eAAO,KAAK,eAAe,MAAM,OAAO,UAAU,CAAC;AACnD,qBAAa,CAAC;AAAA,MAChB;AAGA,gBAAU;AACV,qBAAe;AAGf,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,YAAY,OAAO,MAAM,SAAS,aAAa,MAAM;AAC3D,aAAO,KAAK,eAAe,OAAO,OAAO,SAAS,CAAC;AAAA,IACrD;AAAA,EACF;AAGA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,KAAK,eAAe,MAAM,OAAO,UAAU,CAAC;AAAA,EACrD;AACA,SAAO;AACT;AAUA,SAAS,eAAe,OAAO,OAAO,QAAQ;AAC5C,QAAM,WAAW,OAAO,CAAC,EAAE;AAC3B,QAAM,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE;AACzC,QAAM,QAAQ,MAAM,MAAM,UAAU,MAAM;AAC1C,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;AAEA,IAAM,OAAO,OAAO,YAAY,eAAe,WAAW,QAAQ,SAAS,MAAM;AAAC;AAClF,IAAM,aAAa;AAGnB,IAAM,OAAO;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,YAAY,CAAC;AAAA,EACb,aAAa,CAAC;AAAA,EACd,eAAe,CAAC;AAAA,EAChB,aAAa;AACf;AA6BA,SAAS,QAAQ;AACf,QAAM,SAAS,CAAC;AAChB,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,aAAa,CAAC;AACnB,OAAK,cAAc,CAAC;AACpB,OAAK,gBAAgB,CAAC;AACtB,OAAK,cAAc;AACnB,SAAO;AACT;AAyDA,SAAS,uBAAuBE,SAAQ,qBAAqB,OAAO;AAClE,MAAI,KAAK,aAAa;AACpB,SAAK,qEAAqEA,OAAM,KAAK,UAAU,EAAE;AAAA,EACnG;AACA,MAAI,CAAC,2BAA2B,KAAKA,OAAM,GAAG;AAC5C,UAAM,IAAI,MAAM;AAAA;AAAA;AAAA,qBAGC;AAAA,EACnB;AACA,OAAK,cAAc,KAAK,CAACA,SAAQ,kBAAkB,CAAC;AACtD;AAMA,SAAS,OAAO;AAEd,OAAK,UAAU,OAAO,KAAK,aAAa;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,SAAK,WAAW,CAAC,EAAE,CAAC,EAAE;AAAA,MACpB,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAGA,OAAK,SAAS,OAAO,KAAK,QAAQ,MAAM;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,SAAK,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,MACrB,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,OAAK,cAAc;AACnB,SAAO;AACT;AAOA,SAAS,SAAS,KAAK;AACrB,MAAI,CAAC,KAAK,aAAa;AACrB,SAAK;AAAA,EACP;AACA,SAAO,IAAI,KAAK,OAAO,OAAO,KAAK,MAAM,KAAK,QAAQ,OAAO,GAAG,CAAC;AACnE;AACA,SAAS,OAAO;AAUhB,SAAS,KAAK,KAAK,OAAO,MAAM,OAAO,MAAM;AAC3C,MAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,QAAI,MAAM;AACR,YAAM,MAAM,gCAAgC,IAAI,oBAAoB;AAAA,IACtE;AACA,WAAO;AACP,WAAO;AAAA,EACT;AACA,QAAMC,WAAU,IAAI,QAAQ,IAAI;AAChC,QAAM,SAAS,SAAS,GAAG;AAC3B,QAAM,WAAW,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,MAAM,WAAW,CAAC,QAAQ,MAAM,MAAM,SAASA,SAAQ,MAAM,KAAK,GAAG;AACvE,eAAS,KAAK,MAAM,kBAAkBA,QAAO,CAAC;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;;;AGrxDO,IAAM,6BAA6B;AAEnC,IAAM,2BAA2B,IAAI,OAAO,0BAA0B;AACtE,IAAM,+BAA+B,IAAI,OAAO,GAAG,0BAA0B,GAAG;AAChF,IAAM,kCAAkC,IAAI,OAAO,4BAA4B,GAAG;ADazF,SAAS,qBAAqB,QAAmD;AAC/E,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO,OAAO,CAAC,EAAE;EACnB;AAEA,MAAI,OAAO,WAAW,KAAK,OAAO,CAAC,EAAE,QAAQ;AAC3C,WAAO,CAAC,MAAM,IAAI,EAAE,SAAS,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,KAAK;EAChE;AAEA,SAAO;AACT;AAcO,SAAS,SAASC,UAAkC;AACzD,SAAO,IAAI,OAAO;IAChB,KAAK,IAAI,UAAU,UAAU;IAC7B,mBAAmB,CAAC,cAAc,UAAU,aAAa;AAIvD,YAAM,aAAa,aAAa,KAAK,CAAA,gBAAe,YAAY,UAAU,KAAK,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG;AAK5G,YAAM,kBAAkB,aAAa,KAAK,CAAA,gBAAe,YAAY,QAAQ,iBAAiB,CAAC;AAM/F,UAAI,CAAC,cAAc,iBAAiB;AAClC;MACF;AAEA,YAAM,EAAE,IAAAC,IAAG,IAAI;AACf,YAAM,YAAY,wBAAwB,SAAS,KAAK,CAAC,GAAG,YAAY,CAAC;AACzE,YAAM,UAAU,iBAAiB,SAAS;AAE1C,cAAQ,QAAQ,CAAC,EAAE,SAAS,MAAM;AAEhC,cAAM,uBAAuB,oBAAoB,SAAS,KAAK,UAAU,CAAA,SAAQ,KAAK,WAAW;AAEjG,YAAI;AACJ,YAAI;AAEJ,YAAI,qBAAqB,SAAS,GAAG;AAEnC,sBAAY,qBAAqB,CAAC;AAClC,iCAAuB,SAAS,IAAI;YAClC,UAAU;YACV,UAAU,MAAM,UAAU,KAAK;YAC/B;YACA;UACF;QACF,WAAW,qBAAqB,QAAQ;AACtC,gBAAM,UAAU,SAAS,IAAI,YAAY,SAAS,MAAM,SAAS,IAAI,KAAK,GAAG;AAC7E,cAAI,CAAC,6BAA6B,KAAK,OAAO,GAAG;AAC/C;UACF;AACA,sBAAY,qBAAqB,CAAC;AAClC,iCAAuB,SAAS,IAAI,YAAY,UAAU,KAAK,SAAS,IAAI,QAAW,GAAG;QAC5F;AAEA,YAAI,aAAa,sBAAsB;AACrC,gBAAM,wBAAwB,qBAAqB,MAAM,wBAAwB,EAAE,OAAO,OAAO;AAEjG,cAAI,sBAAsB,UAAU,GAAG;AACrC,mBAAO;UACT;AAEA,gBAAM,sBAAsB,sBAAsB,sBAAsB,SAAS,CAAC;AAClF,gBAAM,yBAAyB,UAAU,MAAM,qBAAqB,YAAY,mBAAmB;AAEnG,cAAI,CAAC,qBAAqB;AACxB,mBAAO;UACT;AAEA,gBAAM,mBAAmB,SAAS,mBAAmB,EAAE,IAAI,CAAA,MAAK,EAAE,SAASD,SAAQ,eAAe,CAAC;AAEnG,cAAI,CAAC,qBAAqB,gBAAgB,GAAG;AAC3C,mBAAO;UACT;AAEA,2BACG,OAAO,CAAA,SAAQ,KAAK,MAAM,EAE1B,IAAI,CAAA,UAAS;YACZ,GAAG;YACH,MAAM,yBAAyB,KAAK,QAAQ;YAC5C,IAAI,yBAAyB,KAAK,MAAM;UAC1C,EAAE,EAED,OAAO,CAAA,SAAQ;AACd,gBAAI,CAAC,SAAS,OAAO,MAAM,MAAM;AAC/B,qBAAO;YACT;AAEA,mBAAO,CAAC,SAAS,IAAI,aAAa,KAAK,MAAM,KAAK,IAAI,SAAS,OAAO,MAAM,IAAI;UAClF,CAAC,EAEA,OAAO,CAAA,SAAQA,SAAQ,SAAS,KAAK,KAAK,CAAC,EAE3C,OAAO,CAAA,SAAQA,SAAQ,eAAe,KAAK,KAAK,CAAC,EAEjD,QAAQ,CAAA,SAAQ;AACf,gBAAI,gBAAgB,KAAK,MAAM,KAAK,IAAI,SAAS,GAAG,EAAE,KAAK,CAAA,SAAQ,KAAK,KAAK,SAASA,SAAQ,IAAI,GAAG;AACnG;YACF;AAEA,YAAAC,IAAG;cACD,KAAK;cACL,KAAK;cACLD,SAAQ,KAAK,OAAO;gBAClB,MAAM,KAAK;cACb,CAAC;YACH;UACF,CAAC;QACL;MACF,CAAC;AAED,UAAI,CAACC,IAAG,MAAM,QAAQ;AACpB;MACF;AAEA,aAAOA;IACT;EACF,CAAC;AACH;AEnJO,SAAS,aAAaD,UAAsC;AACjE,SAAO,IAAIE,OAAO;IAChB,KAAK,IAAIC,UAAU,iBAAiB;IACpC,OAAO;MACL,aAAa,CAAC,MAAM,KAAK,UAAU;AAfzC,YAAA,IAAA;AAgBQ,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO;QACT;AAEA,YAAI,CAAC,KAAK,UAAU;AAClB,iBAAO;QACT;AAEA,YAAI,OAAiC;AAErC,YAAI,MAAM,kBAAkB,mBAAmB;AAC7C,iBAAO,MAAM;QACf,OAAO;AACL,cAAI,IAAI,MAAM;AACd,gBAAM,MAAM,CAAC;AAEb,iBAAO,EAAE,aAAa,OAAO;AAC3B,gBAAI,KAAK,CAAC;AACV,gBAAI,EAAE;UACR;AACA,iBAAO,IAAI,KAAK,CAAA,UAAS,MAAM,aAAa,GAAG;QACjD;AAEA,YAAI,CAAC,MAAM;AACT,iBAAO;QACT;AAEA,cAAM,QAAQ,cAAc,KAAK,OAAOH,SAAQ,KAAK,IAAI;AACzD,cAAM,QAAO,KAAA,QAAA,OAAA,SAAA,KAAM,SAAN,OAAA,KAAc,MAAM;AACjC,cAAM,UAAS,KAAA,QAAA,OAAA,SAAA,KAAM,WAAN,OAAA,KAAgB,MAAM;AAErC,YAAIA,SAAQ,sBAAsB;AAChC,UAAAA,SAAQ,OAAO,SAAS,gBAAgBA,SAAQ,KAAK,IAAI;QAC3D;AAEA,YAAI,QAAQ,MAAM;AAChB,iBAAO,KAAK,MAAM,MAAM;AAExB,iBAAO;QACT;AAEA,eAAO;MACT;IACF;EACF,CAAC;AACH;AClDO,SAAS,aAAaA,UAAsC;AACjE,SAAO,IAAIE,OAAO;IAChB,KAAK,IAAIC,UAAU,iBAAiB;IACpC,OAAO;MACL,aAAa,CAAC,MAAM,OAAOC,WAAU;AACnC,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,MAAM,IAAI;AAElB,YAAI,OAAO;AACT,iBAAO;QACT;AAEA,YAAI,cAAc;AAElB,QAAAA,OAAM,QAAQ,QAAQ,CAAA,SAAQ;AAC5B,yBAAe,KAAK;QACtB,CAAC;AAED,cAAM,OAAO,KAAK,aAAa,EAAE,iBAAiBJ,SAAQ,gBAAgB,CAAC,EAAE;UAC3E,CAAA,SAAQ,KAAK,UAAU,KAAK,UAAU;QACxC;AAEA,YAAI,CAAC,eAAe,CAAC,MAAM;AACzB,iBAAO;QACT;AAEA,eAAOA,SAAQ,OAAO,SAAS,QAAQA,SAAQ,MAAM;UACnD,MAAM,KAAK;QACb,CAAC;MACH;IACF;EACF,CAAC;AACH;AJwHO,SAAS,aAAa,KAAyB,WAAsC;AAC1F,QAAM,mBAA6B,CAAC,QAAQ,SAAS,OAAO,QAAQ,UAAU,OAAO,UAAU,OAAO,OAAO,MAAM;AAEnH,MAAI,WAAW;AACb,cAAU,QAAQ,CAAA,aAAY;AAC5B,YAAM,eAAe,OAAO,aAAa,WAAW,WAAW,SAAS;AAExE,UAAI,cAAc;AAChB,yBAAiB,KAAK,YAAY;MACpC;IACF,CAAC;EACH;AAEA,SACE,CAAC,OACD,IAAI,QAAQ,iCAAiC,EAAE,EAAE;IAC/C,IAAI;;MAEF,UAAU,iBAAiB,KAAK,GAAG,CAAC;MACpC;IACF;EACF;AAEJ;AAMO,IAAM,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,UAAU;EAEV,aAAa;EAEb,UAAU;EAEV,WAAW;AACT,QAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,gBAAgB;AAEzD,WAAK,QAAQ,iBAAiB,KAAK,QAAQ;AAC3C,cAAQ,KAAK,qFAAqF;IACpG;AACA,SAAK,QAAQ,UAAU,QAAQ,CAAA,aAAY;AACzC,UAAI,OAAO,aAAa,UAAU;AAChC,+BAAuB,QAAQ;AAC/B;MACF;AACA,6BAAuB,SAAS,QAAQ,SAAS,eAAe;IAClE,CAAC;EACH;EAEA,YAAY;AACV,UAAM;EACR;EAEA,YAAY;AACV,WAAO,KAAK,QAAQ;EACtB;EAEA,aAAa;AACX,WAAO;MACL,aAAa;MACb,sBAAsB;MACtB,aAAa;MACb,UAAU;MACV,WAAW,CAAC;MACZ,iBAAiB;MACjB,gBAAgB;QACd,QAAQ;QACR,KAAK;QACL,OAAO;MACT;MACA,cAAc,CAAC,KAAK,QAAQ,CAAC,CAAC,aAAa,KAAK,IAAI,SAAS;MAC7D,UAAU,CAAA,QAAO,CAAC,CAAC;MACnB,gBAAgB,CAAA,QAAO,CAAC,CAAC;IAC3B;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,MAAM;QACJ,SAAS;QACT,UAAU,SAAS;AACjB,iBAAO,QAAQ,aAAa,MAAM;QACpC;MACF;MACA,QAAQ;QACN,SAAS,KAAK,QAAQ,eAAe;MACvC;MACA,KAAK;QACH,SAAS,KAAK,QAAQ,eAAe;MACvC;MACA,OAAO;QACL,SAAS,KAAK,QAAQ,eAAe;MACvC;IACF;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;QACL,UAAU,CAAA,QAAO;AACf,gBAAM,OAAQ,IAAoB,aAAa,MAAM;AAGrD,cACE,CAAC,QACD,CAAC,KAAK,QAAQ,aAAa,MAAM;YAC/B,iBAAiB,CAAA,QAAO,CAAC,CAAC,aAAa,KAAK,KAAK,QAAQ,SAAS;YAClE,WAAW,KAAK,QAAQ;YACxB,iBAAiB,KAAK,QAAQ;UAChC,CAAC,GACD;AACA,mBAAO;UACT;AACA,iBAAO;QACT;MACF;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAE7B,QACE,CAAC,KAAK,QAAQ,aAAa,eAAe,MAAM;MAC9C,iBAAiB,CAAA,SAAQ,CAAC,CAAC,aAAa,MAAM,KAAK,QAAQ,SAAS;MACpE,WAAW,KAAK,QAAQ;MACxB,iBAAiB,KAAK,QAAQ;IAChC,CAAC,GACD;AAEA,aAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,EAAE,GAAG,gBAAgB,MAAM,GAAG,CAAC,GAAG,CAAC;IAC/F;AAEA,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC9E;EAEA,cAAc;AACZ,WAAO;MACL,SACE,CAAA,eACA,CAAC,EAAE,MAAM,MAAM;AACb,cAAM,EAAE,KAAK,IAAI;AAEjB,YACE,CAAC,KAAK,QAAQ,aAAa,MAAM;UAC/B,iBAAiB,CAAA,QAAO,CAAC,CAAC,aAAa,KAAK,KAAK,QAAQ,SAAS;UAClE,WAAW,KAAK,QAAQ;UACxB,iBAAiB,KAAK,QAAQ;QAChC,CAAC,GACD;AACA,iBAAO;QACT;AAEA,eAAO,MAAM,EAAE,QAAQ,KAAK,MAAM,UAAU,EAAE,QAAQ,mBAAmB,IAAI,EAAE,IAAI;MACrF;MAEF,YACE,CAAA,eACA,CAAC,EAAE,MAAM,MAAM;AACb,cAAM,EAAE,KAAK,IAAI,cAAc,CAAC;AAEhC,YACE,QACA,CAAC,KAAK,QAAQ,aAAa,MAAM;UAC/B,iBAAiB,CAAA,QAAO,CAAC,CAAC,aAAa,KAAK,KAAK,QAAQ,SAAS;UAClE,WAAW,KAAK,QAAQ;UACxB,iBAAiB,KAAK,QAAQ;QAChC,CAAC,GACD;AACA,iBAAO;QACT;AAEA,eAAO,MAAM,EACV,WAAW,KAAK,MAAM,YAAY,EAAE,sBAAsB,KAAK,CAAC,EAChE,QAAQ,mBAAmB,IAAI,EAC/B,IAAI;MACT;MAEF,WACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,UAAU,KAAK,MAAM,EAAE,sBAAsB,KAAK,CAAC,EAAE,QAAQ,mBAAmB,IAAI,EAAE,IAAI;MAC3G;IACJ;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAM,CAAA,SAAQ;AACZ,gBAAM,aAA+B,CAAC;AAEtC,cAAI,MAAM;AACR,kBAAM,EAAE,WAAW,gBAAgB,IAAI,KAAK;AAC5C,kBAAM,QAAQK,KAAK,IAAI,EAAE;cACvB,CAAA,SACE,KAAK,UACL,KAAK,QAAQ,aAAa,KAAK,OAAO;gBACpC,iBAAiB,CAAA,SAAQ,CAAC,CAAC,aAAa,MAAM,SAAS;gBACvD;gBACA;cACF,CAAC;YACL;AAEA,gBAAI,MAAM,QAAQ;AAChB,oBAAM;gBAAQ,CAAA,SACZ,WAAW,KAAK;kBACd,MAAM,KAAK;kBACX,MAAM;oBACJ,MAAM,KAAK;kBACb;kBACA,OAAO,KAAK;gBACd,CAAC;cACH;YACF;UACF;AAEA,iBAAO;QACT;QACA,MAAM,KAAK;QACX,eAAe,CAAA,UAAS;AApYhC,cAAA;AAqYU,iBAAO;YACL,OAAM,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY;UACpB;QACF;MACF,CAAC;IACH;EACF;EAEA,wBAAwB;AACtB,UAAM,UAAoB,CAAC;AAC3B,UAAM,EAAE,WAAW,gBAAgB,IAAI,KAAK;AAE5C,QAAI,KAAK,QAAQ,UAAU;AACzB,cAAQ;QACN,SAAS;UACP,MAAM,KAAK;UACX,iBAAiB,KAAK,QAAQ;UAC9B,UAAU,CAAA,QACR,KAAK,QAAQ,aAAa,KAAK;YAC7B,iBAAiB,CAAA,SAAQ,CAAC,CAAC,aAAa,MAAM,SAAS;YACvD;YACA;UACF,CAAC;UACH,gBAAgB,KAAK,QAAQ;QAC/B,CAAC;MACH;IACF;AAEA,QAAI,KAAK,QAAQ,gBAAgB,MAAM;AACrC,cAAQ;QACN,aAAa;UACX,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,sBAAsB,KAAK,QAAQ;QACrC,CAAC;MACH;IACF;AAEA,QAAI,KAAK,QAAQ,aAAa;AAC5B,cAAQ;QACN,aAAa;UACX,QAAQ,KAAK;UACb,iBAAiB,KAAK,QAAQ;UAC9B,MAAM,KAAK;QACb,CAAC;MACH;IACF;AAEA,WAAO;EACT;AACF,CAAC;;;;;;;;AMrbD,IAAM,eAAe;AACrB,IAAM,gBAAgB;AA8Cf,IAAM,uBAAuB;AAQ7B,IAAM,aAAa,MAAK,OAA0B;EACvD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,cAAc;MACd,gBAAgB,CAAC;MACjB,WAAW;MACX,gBAAgB;IAClB;EACF;EAEA,OAAO;EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;EACrC;EAEA,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;EACvB;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC/E;EAEA,cAAc;AACZ,WAAO;MACL,kBACE,MACA,CAAC,EAAE,UAAU,MAAM,MAAM;AACvB,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAM,EACV,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EACvE,iBAAiB,cAAc,KAAK,OAAO,cAAc,aAAa,CAAC,EACvE,IAAI;QACT;AACA,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;MACzF;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAiB;IAC7D;EACF;EAEA,gBAAgB;AACd,QAAI,YAAY,kBAAkB;MAChC,MAAM;MACN,MAAM,KAAK;IACb,CAAC;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,kBAAY,kBAAkB;QAC5B,MAAM;QACN,MAAM,KAAK;QACX,WAAW,KAAK,QAAQ;QACxB,gBAAgB,KAAK,QAAQ;QAC7B,eAAe,MAAM;AACnB,iBAAO,KAAK,OAAO,cAAc,aAAa;QAChD;QACA,QAAQ,KAAK;MACf,CAAC;IACH;AACA,WAAO,CAAC,SAAS;EACnB;AACF,CAAC;AChGM,IAAM,WAAWC,MAAK,OAAwB;EACnD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;MACjB,oBAAoB;MACpB,qBAAqB;IACvB;EACF;EAEA,SAAS;EAET,UAAU;EAEV,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAMC,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC/E;EAEA,uBAAuB;AACrB,WAAO;MACL,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;MACzD,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;MACtD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;IAChE;EACF;AACF,CAAC;AE/DD,IAAA,sBAAA,CAAA;AAAA,SAAA,qBAAA;EAAA,iBAAA,MAAA;EAAA,kBAAA,MAAA;EAAA,iBAAA,MAAA;EAAA,cAAA,MAAA;EAAA,eAAA,MAAA;EAAA,kBAAA,MAAA;EAAA,mBAAA,MAAA;EAAA,oBAAA,MAAA;EAAA,kBAAA,MAAA;EAAA,kBAAA,MAAA;AAAA,CAAA;ACIO,IAAM,kBAAkB,CAAC,YAA+B,UAAuB;AACpF,QAAM,EAAE,MAAM,IAAI,MAAM;AACxB,QAAM,WAAW,YAAY,YAAY,MAAM,MAAM;AAErD,MAAI,cAAc;AAClB,MAAI,eAAe,MAAM;AACzB,MAAI,aAAa,MAAM;AACvB,MAAI,cAA6B;AAEjC,SAAO,eAAe,KAAK,gBAAgB,MAAM;AAC/C,kBAAc,MAAM,KAAK,YAAY;AAErC,QAAI,YAAY,SAAS,UAAU;AACjC,oBAAc;IAChB,OAAO;AACL,sBAAgB;AAChB,oBAAc;IAChB;EACF;AAEA,MAAI,gBAAgB,MAAM;AACxB,WAAO;EACT;AAEA,SAAO,EAAE,MAAM,MAAM,IAAI,QAAQ,UAAU,GAAG,OAAO,YAAY;AACnE;ACxBO,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,aAAa;AAChB,WAAO;EACT;AAEA,QAAM,CAAC,EAAE,KAAK,IAAI,kBAAkB,OAAO,YAAY,YAAY,KAAK,MAAM,CAAC;AAE/E,SAAO;AACT;AEbO,IAAM,gBAAgB,CAAC,aAA0B,MAAc,oBAA8B;AAClG,QAAM,EAAE,QAAQ,IAAI,YAAY;AAEhC,QAAM,kBAAkB,KAAK,IAAI,GAAG,QAAQ,MAAM,CAAC;AAEnD,QAAM,eAAe,YAAY,IAAI,QAAQ,eAAe,EAAE,KAAK;AAEnE,MAAI,CAAC,gBAAgB,CAAC,gBAAgB,SAAS,aAAa,KAAK,IAAI,GAAG;AACtE,WAAO;EACT;AAEA,SAAO;AACT;ACZO,IAAM,oBAAoB,CAAC,YAAoB,UAAgC;AAFtF,MAAA;AAGE,QAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,QAAM,aAAa,MAAM,IAAI,QAAQ,QAAQ,MAAM,CAAC;AAEpD,MAAI,WAAW,MAAM,MAAM,GAAG;AAC5B,WAAO;EACT;AAEA,QAAI,KAAA,WAAW,eAAX,OAAA,SAAA,GAAuB,KAAK,UAAS,YAAY;AACnD,WAAO;EACT;AAEA,SAAO;AACT;ACZO,IAAM,qBAAqB,CAAC,YAAoB,OAAoB,SAAgB;AACzF,MAAI,CAAC,MAAM;AACT,WAAO;EACT;AAEA,QAAM,WAAWC,YAAY,YAAY,MAAM,MAAM;AAErD,MAAI,aAAa;AAEjB,OAAK,YAAY,CAAA,UAAS;AACxB,QAAI,MAAM,SAAS,UAAU;AAC3B,mBAAa;IACf;EACF,CAAC;AAED,SAAO;AACT;AHXO,IAAM,kBAAkB,CAAC,QAAgB,MAAc,oBAA8B;AAE1F,MAAI,OAAO,SAAS,cAAc,GAAG;AACnC,WAAO;EACT;AAIA,MAAI,OAAO,MAAM,UAAU,SAAS,OAAO,MAAM,UAAU,IAAI;AAC7D,WAAO;EACT;AAKA,MAAI,CAAC,aAAa,OAAO,OAAO,IAAI,KAAK,cAAc,OAAO,OAAO,MAAM,eAAe,GAAG;AAC3F,UAAM,EAAE,QAAQ,IAAI,OAAO,MAAM;AAEjC,UAAM,WAAW,OAAO,MAAM,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;AAE9D,UAAM,kBAAsD,CAAC;AAE7D,aAAS,KAAK,EAAE,YAAY,CAAC,MAAM,QAAQ;AACzC,UAAI,KAAK,KAAK,SAAS,MAAM;AAC3B,wBAAgB,KAAK,EAAE,MAAM,IAAI,CAAC;MACpC;IACF,CAAC;AAED,UAAM,WAAW,gBAAgB,GAAG,EAAE;AAEtC,QAAI,CAAC,UAAU;AACb,aAAO;IACT;AAEA,UAAM,eAAe,OAAO,MAAM,IAAI,QAAQ,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC;AAEjF,WAAO,OACJ,MAAM,EACN,IAAI,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG,aAAa,IAAI,CAAC,EAC5E,YAAY,EACZ,IAAI;EACT;AAIA,MAAI,CAAC,aAAa,OAAO,OAAO,IAAI,GAAG;AACrC,WAAO;EACT;AAIA,MAAI,CAAC,gBAAgB,OAAO,KAAK,GAAG;AAClC,WAAO;EACT;AAEA,QAAM,cAAc,gBAAgB,MAAM,OAAO,KAAK;AAEtD,MAAI,CAAC,aAAa;AAChB,WAAO;EACT;AAEA,QAAM,QAAQ,OAAO,MAAM,IAAI,QAAQ,YAAY,KAAK,MAAM,CAAC;AAC/D,QAAM,WAAW,MAAM,KAAK,YAAY,KAAK;AAE7C,QAAM,6BAA6B,mBAAmB,MAAM,OAAO,OAAO,QAAQ;AAGlF,MAAI,kBAAkB,MAAM,OAAO,KAAK,KAAK,CAAC,4BAA4B;AACxE,WAAO,OAAO,SAAS,iBAAiB;EAC1C;AAKA,SAAO,OAAO,MAAM,EAAE,aAAa,IAAI,EAAE,IAAI;AAC/C;AK/EO,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,YAAY,iBAAiB,YAAY,KAAK;AACpD,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;EACT;AAEA,MAAI,YAAY,YAAY,OAAO;AACjC,WAAO;EACT;AAEA,SAAO;AACT;ACbO,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,YAAY,iBAAiB,YAAY,KAAK;AACpD,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;EACT;AAEA,MAAI,YAAY,YAAY,OAAO;AACjC,WAAO;EACT;AAEA,SAAO;AACT;AFZO,IAAM,eAAe,CAAC,QAAgB,SAAiB;AAG5D,MAAI,CAACC,aAAa,OAAO,OAAO,IAAI,GAAG;AACrC,WAAO;EACT;AAIA,MAAI,CAAC,cAAc,OAAO,OAAO,IAAI,GAAG;AACtC,WAAO;EACT;AAIA,QAAM,EAAE,UAAU,IAAI,OAAO;AAC7B,QAAM,EAAE,OAAO,IAAI,IAAI;AAEvB,MAAI,CAAC,UAAU,SAAS,MAAM,WAAW,GAAG,GAAG;AAC7C,WAAO;EACT;AAGA,MAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG;AACxC,WAAO,OACJ,MAAM,EACN,MAAM,OAAO,MAAM,UAAU,OAAO,CAAC,EACrC,KAAK,IAAI,EACT,aAAa,EACb,IAAI;EACT;AAEA,MAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG;AACxC,WAAO,OAAO,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI;EACzD;AAEA,SAAO,OAAO,SAAS,gBAAgB;AACzC;AGzCO,IAAM,mBAAmB,CAAC,YAAoB,UAAgC;AAFrF,MAAA;AAGE,QAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,QAAM,aAAa,MAAM,IAAI,QAAQ,QAAQ,MAAM,QAAQ,eAAe,CAAC;AAE3E,MAAI,WAAW,MAAM,MAAM,WAAW,OAAO,aAAa,GAAG;AAC3D,WAAO;EACT;AAEA,QAAI,KAAA,WAAW,cAAX,OAAA,SAAA,GAAsB,KAAK,UAAS,YAAY;AAClD,WAAO;EACT;AAEA,SAAO;AACT;AXOO,IAAM,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,WAAW;QACT;UACE,UAAU;UACV,cAAc,CAAC,cAAc,aAAa;QAC5C;QACA;UACE,UAAU;UACV,cAAc,CAAC,UAAU;QAC3B;MACF;IACF;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,QAAQ,CAAC,EAAE,OAAO,MAAM;AACtB,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,SAAS,MAAM;AAC/C,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;UACF;AAEA,cAAI,aAAa,QAAQ,QAAQ,GAAG;AAClC,sBAAU;UACZ;QACF,CAAC;AAED,eAAO;MACT;MACA,cAAc,CAAC,EAAE,OAAO,MAAM;AAC5B,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,SAAS,MAAM;AAC/C,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;UACF;AAEA,cAAI,aAAa,QAAQ,QAAQ,GAAG;AAClC,sBAAU;UACZ;QACF,CAAC;AAED,eAAO;MACT;MACA,WAAW,CAAC,EAAE,OAAO,MAAM;AACzB,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,UAAU,aAAa,MAAM;AAC7D,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;UACF;AAEA,cAAI,gBAAgB,QAAQ,UAAU,YAAY,GAAG;AACnD,sBAAU;UACZ;QACF,CAAC;AAED,eAAO;MACT;MACA,iBAAiB,CAAC,EAAE,OAAO,MAAM;AAC/B,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,UAAU,aAAa,MAAM;AAC7D,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;UACF;AAEA,cAAI,gBAAgB,QAAQ,UAAU,YAAY,GAAG;AACnD,sBAAU;UACZ;QACF,CAAC;AAED,eAAO;MACT;IACF;EACF;AACF,CAAC;AavGD,IAAMC,gBAAe;AACrB,IAAMC,iBAAgB;AA+Cf,IAAM,wBAAwB;AAQ9B,IAAM,cAAcL,MAAK,OAA2B;EACzD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,cAAc;MACd,gBAAgB,CAAC;MACjB,WAAW;MACX,gBAAgB;IAClB;EACF;EAEA,OAAO;EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;EACrC;EAEA,gBAAgB;AACd,WAAO;MACL,OAAO;QACL,SAAS;QACT,WAAW,CAAA,YAAW;AACpB,iBAAO,QAAQ,aAAa,OAAO,IAAI,SAAS,QAAQ,aAAa,OAAO,KAAK,IAAI,EAAE,IAAI;QAC7F;MACF;MACA,MAAM;QACJ,SAAS;QACT,WAAW,CAAA,YAAW,QAAQ,aAAa,MAAM;MACnD;IACF;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,UAAM,EAAE,OAAO,GAAG,uBAAuB,IAAI;AAE7C,WAAO,UAAU,IACb,CAAC,MAAMC,gBAAgB,KAAK,QAAQ,gBAAgB,sBAAsB,GAAG,CAAC,IAC9E,CAAC,MAAMA,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC5E;EAEA,cAAc;AACZ,WAAO;MACL,mBACE,MACA,CAAC,EAAE,UAAU,MAAM,MAAM;AACvB,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAM,EACV,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EACvE,iBAAiBG,eAAc,KAAK,OAAO,cAAcC,cAAa,CAAC,EACvE,IAAI;QACT;AACA,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;MACzF;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,kBAAkB;IAC9D;EACF;EAEA,gBAAgB;AACd,QAAI,YAAYC,kBAAkB;MAChC,MAAM;MACN,MAAM,KAAK;MACX,eAAe,CAAA,WAAU,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;MAC5C,eAAe,CAAC,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,UAAU,CAAC,MAAM,CAAC;IACjF,CAAC;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,kBAAYA,kBAAkB;QAC5B,MAAM;QACN,MAAM,KAAK;QACX,WAAW,KAAK,QAAQ;QACxB,gBAAgB,KAAK,QAAQ;QAC7B,eAAe,CAAA,WAAU,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,OAAO,cAAcD,cAAa,EAAE;QACzF,eAAe,CAAC,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,UAAU,CAAC,MAAM,CAAC;QAC/E,QAAQ,KAAK;MACf,CAAC;IACH;AACA,WAAO,CAAC,SAAS;EACnB;AACF,CAAC;ACnGM,IAAME,cAAa;AAMnB,IAAM,WAAWP,MAAK,OAAwB;EACnD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,QAAQ;MACR,gBAAgB,CAAC;MACjB,kBAAkB;MAClB,MAAM;IACR;EACF;EAEA,UAAU;AACR,WAAO,KAAK,QAAQ,SAAS,qBAAqB;EACpD;EAEA,UAAU;EAEV,gBAAgB;AACd,WAAO;MACL,SAAS;QACP,SAAS;QACT,aAAa;QACb,WAAW,CAAA,YAAW;AACpB,gBAAM,cAAc,QAAQ,aAAa,cAAc;AAEvD,iBAAO,gBAAgB,MAAM,gBAAgB;QAC/C;QACA,YAAY,CAAA,gBAAe;UACzB,gBAAgB,WAAW;QAC7B;MACF;IACF;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK,iBAAiB,KAAK,IAAI;QAC/B,UAAU;MACZ;IACF;EACF;EAEA,WAAW,EAAE,MAAM,eAAe,GAAG;AACnC,WAAO;MACL;MACAC,gBAAgB,KAAK,QAAQ,gBAAgB,gBAAgB;QAC3D,aAAa,KAAK;MACpB,CAAC;MACD;QACE;QACA;UACE;UACA;YACE,MAAM;YACN,SAAS,KAAK,MAAM,UAAU,YAAY;UAC5C;QACF;QACA,CAAC,MAAM;MACT;MACA,CAAC,OAAO,CAAC;IACX;EACF;EAEA,uBAAuB;AACrB,UAAM,YAEF;MACF,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;MACzD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;IAChE;AAEA,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB,aAAO;IACT;AAEA,WAAO;MACL,GAAG;MACH,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;IACxD;EACF;EAEA,cAAc;AACZ,WAAO,CAAC,EAAE,MAAM,gBAAgB,QAAQ,OAAO,MAAM;AACnD,YAAM,WAAW,SAAS,cAAc,IAAI;AAC5C,YAAM,kBAAkB,SAAS,cAAc,OAAO;AACtD,YAAM,iBAAiB,SAAS,cAAc,MAAM;AACpD,YAAM,WAAW,SAAS,cAAc,OAAO;AAC/C,YAAM,UAAU,SAAS,cAAc,KAAK;AAE5C,YAAM,aAAa,CAAC,gBAAiC;AApJ3D,YAAA,IAAA;AAqJQ,iBAAS,cACP,MAAA,KAAA,KAAK,QAAQ,SAAb,OAAA,SAAA,GAAmB,kBAAnB,OAAA,SAAA,GAAA,KAAA,IAAmC,aAAa,SAAS,OAAA,MACzD,0BAA0B,YAAY,eAAe,iBAAiB;MAC1E;AAEA,iBAAW,IAAI;AAEf,sBAAgB,kBAAkB;AAClC,eAAS,OAAO;AAChB,eAAS,iBAAiB,aAAa,CAAA,UAAS,MAAM,eAAe,CAAC;AACtE,eAAS,iBAAiB,UAAU,CAAA,UAAS;AAG3C,YAAI,CAAC,OAAO,cAAc,CAAC,KAAK,QAAQ,mBAAmB;AACzD,mBAAS,UAAU,CAAC,SAAS;AAE7B;QACF;AAEA,cAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,YAAI,OAAO,cAAc,OAAO,WAAW,YAAY;AACrD,iBACG,MAAM,EACN,MAAM,QAAW,EAAE,gBAAgB,MAAM,CAAC,EAC1C,QAAQ,CAAC,EAAE,IAAAO,IAAG,MAAM;AACnB,kBAAM,WAAW,OAAO;AAExB,gBAAI,OAAO,aAAa,UAAU;AAChC,qBAAO;YACT;AACA,kBAAM,cAAcA,IAAG,IAAI,OAAO,QAAQ;AAE1C,YAAAA,IAAG,cAAc,UAAU,QAAW;cACpC,GAAG,eAAA,OAAA,SAAA,YAAa;cAChB;YACF,CAAC;AAED,mBAAO;UACT,CAAC,EACA,IAAI;QACT;AACA,YAAI,CAAC,OAAO,cAAc,KAAK,QAAQ,mBAAmB;AAExD,cAAI,CAAC,KAAK,QAAQ,kBAAkB,MAAM,OAAO,GAAG;AAClD,qBAAS,UAAU,CAAC,SAAS;UAC/B;QACF;MACF,CAAC;AAED,aAAO,QAAQ,KAAK,QAAQ,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpE,iBAAS,aAAa,KAAK,KAAK;MAClC,CAAC;AAED,eAAS,QAAQ,UAAU,KAAK,MAAM;AACtC,eAAS,UAAU,KAAK,MAAM;AAE9B,sBAAgB,OAAO,UAAU,cAAc;AAC/C,eAAS,OAAO,iBAAiB,OAAO;AAExC,aAAO,QAAQ,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACvD,iBAAS,aAAa,KAAK,KAAK;MAClC,CAAC;AAED,aAAO;QACL,KAAK;QACL,YAAY;QACZ,QAAQ,CAAA,gBAAe;AACrB,cAAI,YAAY,SAAS,KAAK,MAAM;AAClC,mBAAO;UACT;AAEA,mBAAS,QAAQ,UAAU,YAAY,MAAM;AAC7C,mBAAS,UAAU,YAAY,MAAM;AACrC,qBAAW,WAAW;AAEtB,iBAAO;QACT;MACF;IACF;EACF;EAEA,gBAAgB;AACd,WAAO;MACLF,kBAAkB;QAChB,MAAMC;QACN,MAAM,KAAK;QACX,eAAe,CAAA,WAAU;UACvB,SAAS,MAAM,MAAM,SAAS,CAAC,MAAM;QACvC;MACF,CAAC;IACH;EACF;AACF,CAAC;AChNM,IAAM,WAAWP,MAAK,OAAwB;EACnD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,cAAc;MACd,gBAAgB,CAAC;IACnB;EACF;EAEA,OAAO;EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;EACrC;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK,iBAAiB,KAAK,IAAI;QAC/B,UAAU;MACZ;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAMC,gBAAgB,KAAK,QAAQ,gBAAgB,gBAAgB,EAAE,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC;EAC3G;EAEA,cAAc;AACZ,WAAO;MACL,gBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,YAAY;MACjE;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,eAAe;IAC3D;EACF;AACF,CAAC;AH9BM,IAAM,UAAUQ,UAAU,OAAuB;EACtD,MAAM;EAEN,gBAAgB;AACd,UAAM,aAAa,CAAC;AAEpB,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,iBAAW,KAAK,YAAY,UAAU,KAAK,QAAQ,WAAW,CAAC;IACjE;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,WAAO;EACT;AACF,CAAC;;;AIrDM,IAAM,YAAY,MAAK,OAAyB;EACrD,MAAM;EAEN,UAAU;EAEV,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,OAAO;EAEP,SAAS;EAET,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,IAAI,CAAC;EACtB;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC9E;EAEA,cAAc;AACZ,WAAO;MACL,cACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa;IACvD;EACF;AACF,CAAC;;;AE7BM,IAAMC,cAAa;AAKnB,IAAMC,cAAa;AAMnB,IAAM,SAAS,KAAK,OAAsB;EAC/C,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;MACA;QACE,KAAK;MACP;MACA;QACE,KAAK;MACP;MACA;QACE,OAAO;QACP,WAAW;QACX,UAAU,CAAA,UAAW,MAAiB,SAAS,cAAc,IAAI,CAAC,IAAI;MACxE;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC9E;EAEA,cAAc;AACZ,WAAO;MACL,WACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;MACF,cACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,aACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;MACrC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa;IACzD;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAMD;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;EAEA,gBAAgB;AACd,WAAO;MACL,cAAc;QACZ,MAAMC;QACN,MAAM,KAAK;MACb,CAAC;IACH;EACF;AACF,CAAC;;;AEpHM,IAAMC,QAAO,MAAK,OAAO;EAC9B,MAAM;EACN,OAAO;AACT,CAAC;;;AE4BM,IAAM,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,gBAAgB,CAAC;IACnB;EACF;EAEA,YAAY;AACV,WAAO;MACL;QACE,KAAK;MACP;MACA;QACE,OAAO;QACP,WAAW;QACX,UAAU,CAAA,UAAW,MAAiB,SAAS,WAAW,IAAI,CAAC,IAAI;MACrE;IACF;EACF;EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;EAC9E;EAEA,cAAc;AACZ,WAAO;MACL,cACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;MACnC;MACF,iBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;MACtC;MACF,gBACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;MACrC;IACJ;EACF;EAEA,uBAAuB;AACrB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAgB;MACpD,SAAS,MAAM,KAAK,OAAO,SAAS,gBAAgB;IACtD;EACF;AACF,CAAC;;;AE5ED,SAAS,WAAWC,WAAU,CAAC,GAAG;AAC9B,SAAO,IAAI,OAAO;AAAA,IACd,KAAK,YAAY;AAAE,aAAO,IAAI,eAAe,YAAYA,QAAO;AAAA,IAAG;AAAA,EACvE,CAAC;AACL;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,YAAYA,UAAS;AAC7B,QAAI;AACJ,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS,KAAKA,SAAQ,WAAW,QAAQ,OAAO,SAAS,KAAK;AACnE,SAAK,QAAQA,SAAQ,UAAU,QAAQ,SAAaA,SAAQ,SAAS;AACrE,SAAK,QAAQA,SAAQ;AACrB,SAAK,WAAW,CAAC,YAAY,WAAW,QAAQ,WAAW,EAAE,IAAI,UAAQ;AACrE,UAAI,UAAU,CAAC,MAAM;AAAE,aAAK,IAAI,EAAE,CAAC;AAAA,MAAG;AACtC,iBAAW,IAAI,iBAAiB,MAAM,OAAO;AAC7C,aAAO,EAAE,MAAM,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACL;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ,CAAC,EAAE,MAAM,QAAQ,MAAM,KAAK,WAAW,IAAI,oBAAoB,MAAM,OAAO,CAAC;AAAA,EACvG;AAAA,EACA,OAAO,YAAY,WAAW;AAC1B,QAAI,KAAK,aAAa,QAAQ,UAAU,OAAO,WAAW,MAAM,KAAK;AACjE,UAAI,KAAK,YAAY,WAAW,MAAM,IAAI,QAAQ;AAC9C,aAAK,UAAU,IAAI;AAAA;AAEnB,aAAK,cAAc;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,UAAU,KAAK;AACX,QAAI,OAAO,KAAK;AACZ;AACJ,SAAK,YAAY;AACjB,QAAI,OAAO,MAAM;AACb,WAAK,QAAQ,WAAW,YAAY,KAAK,OAAO;AAChD,WAAK,UAAU;AAAA,IACnB,OACK;AACD,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,QAAI,OAAO,KAAK,WAAW,MAAM,IAAI,QAAQ,KAAK,SAAS;AAC3D,QAAI,UAAU,CAAC,KAAK,OAAO,eAAe;AAC1C,QAAI,YAAY,KAAK,WAAW,KAAK,aAAa,UAAU,sBAAsB;AAClF,QAAI,SAAS,WAAW,QAAQ,UAAU,aAAa,SAAS,WAAW,SAAS,UAAU;AAC9F,QAAI,SAAS;AACT,UAAI,SAAS,KAAK,YAAY,QAAQ,KAAK;AAC3C,UAAI,UAAU,OAAO;AACjB,YAAI,OAAO,KAAK,WAAW,QAAQ,KAAK,aAAa,SAAS,OAAO,WAAW,EAAE;AAClF,YAAI,MAAM;AACN,cAAI,WAAW,KAAK,sBAAsB;AAC1C,cAAI,MAAM,SAAS,SAAS,SAAS,SAAS;AAC9C,cAAI,UAAU;AACV,mBAAO,MAAM,KAAK,WAAW,QAAQ,KAAK,SAAS,EAAE,sBAAsB,EAAE,OAAO;AACxF,cAAI,YAAa,KAAK,QAAQ,IAAK;AACnC,iBAAO,EAAE,MAAM,SAAS,MAAM,OAAO,SAAS,OAAO,KAAK,MAAM,WAAW,QAAQ,MAAM,UAAU;AAAA,QACvG;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,MAAM;AACP,UAAI,SAAS,KAAK,WAAW,YAAY,KAAK,SAAS;AACvD,UAAI,YAAa,KAAK,QAAQ,IAAK;AACnC,aAAO,EAAE,MAAM,OAAO,OAAO,WAAW,OAAO,OAAO,OAAO,WAAW,KAAK,OAAO,KAAK,QAAQ,OAAO,OAAO;AAAA,IACnH;AACA,QAAI,SAAS,KAAK,WAAW,IAAI;AACjC,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,UAAU,OAAO,YAAY,SAAS,cAAc,KAAK,CAAC;AAC/D,UAAI,KAAK;AACL,aAAK,QAAQ,YAAY,KAAK;AAClC,WAAK,QAAQ,MAAM,UAAU;AAC7B,UAAI,KAAK,OAAO;AACZ,aAAK,QAAQ,MAAM,kBAAkB,KAAK;AAAA,MAC9C;AAAA,IACJ;AACA,SAAK,QAAQ,UAAU,OAAO,gCAAgC,OAAO;AACrE,SAAK,QAAQ,UAAU,OAAO,iCAAiC,CAAC,OAAO;AACvE,QAAI,YAAY;AAChB,QAAI,CAAC,UAAU,UAAU,SAAS,QAAQ,iBAAiB,MAAM,EAAE,YAAY,UAAU;AACrF,mBAAa,CAAC;AACd,kBAAY,CAAC;AAAA,IACjB,OACK;AACD,UAAIC,QAAO,OAAO,sBAAsB;AACxC,UAAI,eAAeA,MAAK,QAAQ,OAAO,aAAa,eAAeA,MAAK,SAAS,OAAO;AACxF,mBAAaA,MAAK,OAAO,OAAO,aAAa;AAC7C,kBAAYA,MAAK,MAAM,OAAO,YAAY;AAAA,IAC9C;AACA,SAAK,QAAQ,MAAM,QAAQ,KAAK,OAAO,cAAc,SAAS;AAC9D,SAAK,QAAQ,MAAM,OAAO,KAAK,MAAM,aAAa,SAAS;AAC3D,SAAK,QAAQ,MAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS;AAC/D,SAAK,QAAQ,MAAM,UAAU,KAAK,SAAS,KAAK,OAAO,SAAS;AAAA,EACpE;AAAA,EACA,gBAAgB,SAAS;AACrB,iBAAa,KAAK,OAAO;AACzB,SAAK,UAAU,WAAW,MAAM,KAAK,UAAU,IAAI,GAAG,OAAO;AAAA,EACjE;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,CAAC,KAAK,WAAW;AACjB;AACJ,QAAI,MAAM,KAAK,WAAW,YAAY,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ,CAAC;AACjF,QAAI,OAAO,OAAO,IAAI,UAAU,KAAK,KAAK,WAAW,MAAM,IAAI,OAAO,IAAI,MAAM;AAChF,QAAI,oBAAoB,QAAQ,KAAK,KAAK,KAAK;AAC/C,QAAI,WAAW,OAAO,qBAAqB,aACrC,kBAAkB,KAAK,YAAY,KAAK,KAAK,IAC7C;AACN,QAAI,OAAO,CAAC,UAAU;AAClB,UAAI,SAAS,IAAI;AACjB,UAAI,KAAK,WAAW,YAAY,KAAK,WAAW,SAAS,OAAO;AAC5D,YAAI,QAAQ,UAAU,KAAK,WAAW,MAAM,KAAK,QAAQ,KAAK,WAAW,SAAS,KAAK;AACvF,YAAI,SAAS;AACT,mBAAS;AAAA,MACjB;AACA,WAAK,UAAU,MAAM;AACrB,WAAK,gBAAgB,GAAI;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,gBAAgB,EAAE;AAAA,EAC3B;AAAA,EACA,OAAO;AACH,SAAK,gBAAgB,EAAE;AAAA,EAC3B;AAAA,EACA,UAAU,OAAO;AACb,QAAI,CAAC,KAAK,WAAW,IAAI,SAAS,MAAM,aAAa;AACjD,WAAK,UAAU,IAAI;AAAA,EAC3B;AACJ;;;ACtIA,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,MAAM;AACd,UAAM,MAAM,IAAI;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,SAAS;AACd,QAAI,OAAO,IAAI,QAAQ,QAAQ,IAAI,KAAK,IAAI,CAAC;AAC7C,WAAO,WAAU,MAAM,IAAI,IAAI,IAAI,WAAU,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,EAC5E;AAAA,EACA,UAAU;AAAE,WAAO,MAAM;AAAA,EAAO;AAAA,EAChC,GAAG,OAAO;AACN,WAAO,iBAAiB,cAAa,MAAM,QAAQ,KAAK;AAAA,EAC5D;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,aAAa,KAAK,KAAK,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,KAAK,MAAM;AACvB,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,sCAAsC;AAC/D,WAAO,IAAI,WAAU,IAAI,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAE,WAAO,IAAI,YAAY,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIrD,OAAO,MAAM,MAAM;AACf,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,eAAe,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,IAAI;AAC9D,aAAO;AACX,QAAI,WAAW,OAAO,KAAK,KAAK;AAChC,QAAI,YAAY;AACZ,aAAO;AACX,QAAI,QAAQ,OAAO,eAAe,KAAK,MAAM,CAAC,EAAE;AAChD,WAAO,SAAS,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,kBAAkB,MAAM,KAAK,WAAW,OAAO;AAClD,WAAQ,YAAS;AACb,UAAI,CAAC,YAAY,WAAU,MAAM,IAAI;AACjC,eAAO;AACX,UAAI,MAAM,KAAK,KAAK,OAAO;AAE3B,eAAS,IAAI,KAAK,SAAQ,KAAK;AAC3B,YAAI,SAAS,KAAK,KAAK,CAAC;AACxB,YAAI,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,OAAO,aAAa,KAAK,MAAM,CAAC,IAAI,GAAG;AACtE,iBAAO,OAAO,MAAM,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;AACpE;AAAA,QACJ,WACS,KAAK,GAAG;AACb,iBAAO;AAAA,QACX;AACA,eAAO;AACP,YAAI,OAAO,KAAK,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAU,MAAM,IAAI;AACpB,iBAAO;AAAA,MACf;AAEA,iBAAS;AACL,YAAI,SAAS,MAAM,IAAI,KAAK,aAAa,KAAK;AAC9C,YAAI,CAAC,QAAQ;AACT,cAAI,KAAK,UAAU,CAAC,KAAK,UAAU,CAAC,cAAc,aAAa,IAAI,GAAG;AAClE,mBAAO,KAAK,IAAI,QAAQ,MAAM,KAAK,WAAW,GAAG;AACjD,uBAAW;AACX,qBAAS;AAAA,UACb;AACA;AAAA,QACJ;AACA,eAAO;AACP,eAAO;AACP,YAAI,OAAO,KAAK,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAU,MAAM,IAAI;AACpB,iBAAO;AAAA,MACf;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,UAAU,UAAU,UAAU;AAC9B,UAAU,WAAW,UAAU;AAC/B,UAAU,OAAO,aAAa,SAAS;AACvC,IAAM,cAAN,MAAM,aAAY;AAAA,EACd,YAAY,KAAK;AACb,SAAK,MAAM;AAAA,EACf;AAAA,EACA,IAAI,SAAS;AACT,WAAO,IAAI,aAAY,QAAQ,IAAI,KAAK,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,IAAI,QAAQ,KAAK,GAAG;AAC/B,WAAO,UAAU,MAAM,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,EAC5E;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,WAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,QAAI,QAAQ,KAAK,MAAM,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC;AAE/C,QAAI,SAAS,GAAG;AACZ,UAAI,OAAO,KAAK,KAAK;AACjB,eAAO;AACX;AAAA,IACJ;AAEA,aAAS,SAAS,OAAO,MAAM,QAAQ,CAAC,KAAI,SAAS,OAAO,WAAW;AACnE,UAAK,OAAO,cAAc,KAAK,CAAC,OAAO,iBAAkB,OAAO,UAAU,OAAO,KAAK,KAAK;AACvF,eAAO;AACX,UAAI,OAAO;AACP,eAAO;AAAA,IACf;AAAA,EACJ;AAEA,SAAO;AACX;AACA,SAAS,YAAY,MAAM;AACvB,WAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,QAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC;AACpD,QAAI,SAAS,OAAO,YAAY;AAC5B,UAAI,OAAO,KAAK,KAAK;AACjB,eAAO;AACX;AAAA,IACJ;AACA,aAAS,QAAQ,OAAO,MAAM,KAAK,KAAI,QAAQ,MAAM,YAAY;AAC7D,UAAK,MAAM,cAAc,KAAK,CAAC,MAAM,iBAAkB,MAAM,UAAU,MAAM,KAAK,KAAK;AACnF,eAAO;AACX,UAAI,MAAM;AACN,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAWA,SAAS,YAAY;AACjB,SAAO,IAAI,OAAO;AAAA,IACd,OAAO;AAAA,MACH,aAAa;AAAA,MACb,uBAAuB,OAAO,SAAS,OAAO;AAC1C,eAAO,QAAQ,OAAO,MAAM,OAAO,UAAU,MAAM,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,MACvF;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,EAAE,YAAyB;AAAA,IAChD;AAAA,EACJ,CAAC;AACL;AACA,IAAM,gBAAgB,eAAe;AAAA,EACjC,aAAa,MAAM,SAAS,EAAE;AAAA,EAC9B,cAAc,MAAM,SAAS,CAAC;AAAA,EAC9B,WAAW,MAAM,QAAQ,EAAE;AAAA,EAC3B,aAAa,MAAM,QAAQ,CAAC;AAChC,CAAC;AACD,SAAS,MAAM,MAAM,KAAK;AACtB,QAAM,SAAS,QAAQ,SAAU,MAAM,IAAI,SAAS,OAAS,MAAM,IAAI,UAAU;AACjF,SAAO,SAAU,OAAO,UAAU,MAAM;AACpC,QAAI,MAAM,MAAM;AAChB,QAAI,SAAS,MAAM,IAAI,IAAI,MAAM,IAAI,OAAO,WAAW,IAAI;AAC3D,QAAI,eAAe,eAAe;AAC9B,UAAI,CAAC,KAAK,eAAe,MAAM,KAAK,OAAO,SAAS;AAChD,eAAO;AACX,iBAAW;AACX,eAAS,MAAM,IAAI,QAAQ,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC;AAAA,IACzE;AACA,QAAI,SAAS,UAAU,kBAAkB,QAAQ,KAAK,QAAQ;AAC9D,QAAI,CAAC;AACD,aAAO;AACX,QAAI;AACA,eAAS,MAAM,GAAG,aAAa,IAAI,UAAU,MAAM,CAAC,CAAC;AACzD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY,MAAM,KAAK,OAAO;AACnC,MAAI,CAAC,QAAQ,CAAC,KAAK;AACf,WAAO;AACX,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,GAAG;AACrC,MAAI,CAAC,UAAU,MAAM,IAAI;AACrB,WAAO;AACX,MAAI,WAAW,KAAK,YAAY,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ,CAAC;AAC3E,MAAI,YAAY,SAAS,SAAS,MAAM,cAAc,aAAa,KAAK,MAAM,IAAI,OAAO,SAAS,MAAM,CAAC;AACrG,WAAO;AACX,OAAK,SAAS,KAAK,MAAM,GAAG,aAAa,IAAI,UAAU,IAAI,CAAC,CAAC;AAC7D,SAAO;AACX;AAKA,SAAS,YAAY,MAAM,OAAO;AAC9B,MAAI,MAAM,aAAa,2BAA2B,EAAE,KAAK,MAAM,qBAAqB;AAChF,WAAO;AACX,MAAI,EAAE,MAAM,IAAI,KAAK,MAAM;AAC3B,MAAI,SAAS,MAAM,OAAO,eAAe,MAAM,MAAM,CAAC,EAAE,aAAa,KAAK,MAAM,OAAO,MAAM,IAAI;AACjG,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,SAAS;AACpB,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AACpC,WAAO,SAAS,KAAK,OAAO,CAAC,EAAE,cAAc,MAAM,IAAI,CAAC;AAC5D,MAAIC,MAAK,KAAK,MAAM,GAAG,QAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC;AAC1E,EAAAA,IAAG,aAAa,cAAc,KAAKA,IAAG,IAAI,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AACjE,OAAK,SAASA,GAAE;AAChB,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,EAAE,MAAM,qBAAqB;AAC7B,WAAO;AACX,MAAI,OAAO,SAAS,cAAc,KAAK;AACvC,OAAK,YAAY;AACjB,SAAO,cAAc,OAAO,MAAM,KAAK,CAAC,WAAW,OAAO,MAAM,UAAU,MAAM,MAAM,EAAE,KAAK,YAAY,CAAC,CAAC,CAAC;AAChH;;;ACzOA,IAAI,iBAAiB;AAKrB,IAAI,eAAe,SAASC,gBAAgB;AAAC;AAE7C,aAAa,UAAU,SAAS,SAAS,OAAQ,OAAO;AACtD,MAAI,CAAC,MAAM,QAAQ;AAAE,WAAO;AAAA,EAAK;AACjC,UAAQ,aAAa,KAAK,KAAK;AAE/B,SAAQ,CAAC,KAAK,UAAU,SACrB,MAAM,SAAS,kBAAkB,KAAK,WAAW,KAAK,KACtD,KAAK,SAAS,kBAAkB,MAAM,YAAY,IAAI,KACvD,KAAK,YAAY,KAAK;AAC1B;AAIA,aAAa,UAAU,UAAU,SAAS,QAAS,OAAO;AACxD,MAAI,CAAC,MAAM,QAAQ;AAAE,WAAO;AAAA,EAAK;AACjC,SAAO,aAAa,KAAK,KAAK,EAAE,OAAO,IAAI;AAC7C;AAEA,aAAa,UAAU,cAAc,SAAS,YAAa,OAAO;AAChE,SAAO,IAAI,OAAO,MAAM,KAAK;AAC/B;AAIA,aAAa,UAAU,QAAQ,SAAS,MAAOC,OAAM,IAAI;AACrD,MAAKA,UAAS,OAAS,CAAAA,QAAO;AAC9B,MAAK,OAAO,OAAS,MAAK,KAAK;AAEjC,MAAIA,SAAQ,IAAI;AAAE,WAAO,aAAa;AAAA,EAAM;AAC5C,SAAO,KAAK,WAAW,KAAK,IAAI,GAAGA,KAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC;AACrE;AAIA,aAAa,UAAU,MAAM,SAAS,IAAK,GAAG;AAC5C,MAAI,IAAI,KAAK,KAAK,KAAK,QAAQ;AAAE,WAAO;AAAA,EAAU;AAClD,SAAO,KAAK,SAAS,CAAC;AACxB;AAOA,aAAa,UAAU,UAAU,SAAS,QAAS,GAAGA,OAAM,IAAI;AAC5D,MAAKA,UAAS,OAAS,CAAAA,QAAO;AAC9B,MAAK,OAAO,OAAS,MAAK,KAAK;AAEjC,MAAIA,SAAQ,IACV;AAAE,SAAK,aAAa,GAAGA,OAAM,IAAI,CAAC;AAAA,EAAG,OAErC;AAAE,SAAK,qBAAqB,GAAGA,OAAM,IAAI,CAAC;AAAA,EAAG;AACjD;AAKA,aAAa,UAAU,MAAM,SAAS,IAAK,GAAGA,OAAM,IAAI;AACpD,MAAKA,UAAS,OAAS,CAAAA,QAAO;AAC9B,MAAK,OAAO,OAAS,MAAK,KAAK;AAEjC,MAAI,SAAS,CAAC;AACd,OAAK,QAAQ,SAAU,KAAK,GAAG;AAAE,WAAO,OAAO,KAAK,EAAE,KAAK,CAAC,CAAC;AAAA,EAAG,GAAGA,OAAM,EAAE;AAC3E,SAAO;AACT;AAKA,aAAa,OAAO,SAAS,KAAM,QAAQ;AACzC,MAAI,kBAAkB,cAAc;AAAE,WAAO;AAAA,EAAO;AACpD,SAAO,UAAU,OAAO,SAAS,IAAI,KAAK,MAAM,IAAI,aAAa;AACnE;AAEA,IAAI,OAAqB,SAAUD,eAAc;AAC/C,WAASE,MAAK,QAAQ;AACpB,IAAAF,cAAa,KAAK,IAAI;AACtB,SAAK,SAAS;AAAA,EAChB;AAEA,MAAKA,cAAe,CAAAE,MAAK,YAAYF;AACrC,EAAAE,MAAK,YAAY,OAAO,OAAQF,iBAAgBA,cAAa,SAAU;AACvE,EAAAE,MAAK,UAAU,cAAcA;AAE7B,MAAI,qBAAqB,EAAE,QAAQ,EAAE,cAAc,KAAK,GAAE,OAAO,EAAE,cAAc,KAAK,EAAE;AAExF,EAAAA,MAAK,UAAU,UAAU,SAAS,UAAW;AAC3C,WAAO,KAAK;AAAA,EACd;AAEA,EAAAA,MAAK,UAAU,aAAa,SAAS,WAAYD,OAAM,IAAI;AACzD,QAAIA,SAAQ,KAAK,MAAM,KAAK,QAAQ;AAAE,aAAO;AAAA,IAAK;AAClD,WAAO,IAAIC,MAAK,KAAK,OAAO,MAAMD,OAAM,EAAE,CAAC;AAAA,EAC7C;AAEA,EAAAC,MAAK,UAAU,WAAW,SAAS,SAAU,GAAG;AAC9C,WAAO,KAAK,OAAO,CAAC;AAAA,EACtB;AAEA,EAAAA,MAAK,UAAU,eAAe,SAAS,aAAc,GAAGD,OAAM,IAAI,OAAO;AACvE,aAAS,IAAIA,OAAM,IAAI,IAAI,KACzB;AAAE,UAAI,EAAE,KAAK,OAAO,CAAC,GAAG,QAAQ,CAAC,MAAM,OAAO;AAAE,eAAO;AAAA,MAAM;AAAA,IAAE;AAAA,EACnE;AAEA,EAAAC,MAAK,UAAU,uBAAuB,SAAS,qBAAsB,GAAGD,OAAM,IAAI,OAAO;AACvF,aAAS,IAAIA,QAAO,GAAG,KAAK,IAAI,KAC9B;AAAE,UAAI,EAAE,KAAK,OAAO,CAAC,GAAG,QAAQ,CAAC,MAAM,OAAO;AAAE,eAAO;AAAA,MAAM;AAAA,IAAE;AAAA,EACnE;AAEA,EAAAC,MAAK,UAAU,aAAa,SAAS,WAAY,OAAO;AACtD,QAAI,KAAK,SAAS,MAAM,UAAU,gBAChC;AAAE,aAAO,IAAIA,MAAK,KAAK,OAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAE;AAAA,EAC3D;AAEA,EAAAA,MAAK,UAAU,cAAc,SAAS,YAAa,OAAO;AACxD,QAAI,KAAK,SAAS,MAAM,UAAU,gBAChC;AAAE,aAAO,IAAIA,MAAK,MAAM,QAAQ,EAAE,OAAO,KAAK,MAAM,CAAC;AAAA,IAAE;AAAA,EAC3D;AAEA,qBAAmB,OAAO,MAAM,WAAY;AAAE,WAAO,KAAK,OAAO;AAAA,EAAO;AAExE,qBAAmB,MAAM,MAAM,WAAY;AAAE,WAAO;AAAA,EAAE;AAEtD,SAAO,iBAAkBA,MAAK,WAAW,kBAAmB;AAE5D,SAAOA;AACT,EAAE,YAAY;AAId,aAAa,QAAQ,IAAI,KAAK,CAAC,CAAC;AAEhC,IAAI,SAAuB,SAAUF,eAAc;AACjD,WAASG,QAAO,MAAM,OAAO;AAC3B,IAAAH,cAAa,KAAK,IAAI;AACtB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS,KAAK,SAAS,MAAM;AAClC,SAAK,QAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,IAAI;AAAA,EACnD;AAEA,MAAKA,cAAe,CAAAG,QAAO,YAAYH;AACvC,EAAAG,QAAO,YAAY,OAAO,OAAQH,iBAAgBA,cAAa,SAAU;AACzE,EAAAG,QAAO,UAAU,cAAcA;AAE/B,EAAAA,QAAO,UAAU,UAAU,SAAS,UAAW;AAC7C,WAAO,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,EACxD;AAEA,EAAAA,QAAO,UAAU,WAAW,SAAS,SAAU,GAAG;AAChD,WAAO,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,MAAM;AAAA,EACtF;AAEA,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,GAAGF,OAAM,IAAI,OAAO;AACzE,QAAI,UAAU,KAAK,KAAK;AACxB,QAAIA,QAAO,WACP,KAAK,KAAK,aAAa,GAAGA,OAAM,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,MAAM,OACpE;AAAE,aAAO;AAAA,IAAM;AACjB,QAAI,KAAK,WACL,KAAK,MAAM,aAAa,GAAG,KAAK,IAAIA,QAAO,SAAS,CAAC,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAE,IAAI,SAAS,QAAQ,OAAO,MAAM,OACpH;AAAE,aAAO;AAAA,IAAM;AAAA,EACnB;AAEA,EAAAE,QAAO,UAAU,uBAAuB,SAAS,qBAAsB,GAAGF,OAAM,IAAI,OAAO;AACzF,QAAI,UAAU,KAAK,KAAK;AACxB,QAAIA,QAAO,WACP,KAAK,MAAM,qBAAqB,GAAGA,QAAO,SAAS,KAAK,IAAI,IAAI,OAAO,IAAI,SAAS,QAAQ,OAAO,MAAM,OAC3G;AAAE,aAAO;AAAA,IAAM;AACjB,QAAI,KAAK,WACL,KAAK,KAAK,qBAAqB,GAAG,KAAK,IAAIA,OAAM,OAAO,GAAG,IAAI,KAAK,MAAM,OAC5E;AAAE,aAAO;AAAA,IAAM;AAAA,EACnB;AAEA,EAAAE,QAAO,UAAU,aAAa,SAAS,WAAYF,OAAM,IAAI;AAC3D,QAAIA,SAAQ,KAAK,MAAM,KAAK,QAAQ;AAAE,aAAO;AAAA,IAAK;AAClD,QAAI,UAAU,KAAK,KAAK;AACxB,QAAI,MAAM,SAAS;AAAE,aAAO,KAAK,KAAK,MAAMA,OAAM,EAAE;AAAA,IAAE;AACtD,QAAIA,SAAQ,SAAS;AAAE,aAAO,KAAK,MAAM,MAAMA,QAAO,SAAS,KAAK,OAAO;AAAA,IAAE;AAC7E,WAAO,KAAK,KAAK,MAAMA,OAAM,OAAO,EAAE,OAAO,KAAK,MAAM,MAAM,GAAG,KAAK,OAAO,CAAC;AAAA,EAChF;AAEA,EAAAE,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO;AACxD,QAAI,QAAQ,KAAK,MAAM,WAAW,KAAK;AACvC,QAAI,OAAO;AAAE,aAAO,IAAIA,QAAO,KAAK,MAAM,KAAK;AAAA,IAAE;AAAA,EACnD;AAEA,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO;AAC1D,QAAI,QAAQ,KAAK,KAAK,YAAY,KAAK;AACvC,QAAI,OAAO;AAAE,aAAO,IAAIA,QAAO,OAAO,KAAK,KAAK;AAAA,IAAE;AAAA,EACpD;AAEA,EAAAA,QAAO,UAAU,cAAc,SAASC,aAAa,OAAO;AAC1D,QAAI,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,MAAM,OAAO,MAAM,KAAK,IAAI,GAC/D;AAAE,aAAO,IAAID,QAAO,KAAK,MAAM,IAAIA,QAAO,KAAK,OAAO,KAAK,CAAC;AAAA,IAAE;AAChE,WAAO,IAAIA,QAAO,MAAM,KAAK;AAAA,EAC/B;AAEA,SAAOA;AACT,EAAE,YAAY;AAEd,IAAO,eAAQ;;;AC1Lf,IAAM,kBAAkB;AACxB,IAAM,SAAN,MAAM,QAAO;AAAA,EACT,YAAY,OAAO,YAAY;AAC3B,SAAK,QAAQ;AACb,SAAK,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA,EAGA,SAAS,OAAO,eAAe;AAC3B,QAAI,KAAK,cAAc;AACnB,aAAO;AACX,QAAI,MAAM,KAAK,MAAM;AACrB,aAAQ,OAAO;AACX,UAAI,OAAO,KAAK,MAAM,IAAI,MAAM,CAAC;AACjC,UAAI,KAAK,WAAW;AAChB,UAAE;AACF;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO;AACX,QAAI,eAAe;AACf,cAAQ,KAAK,UAAU,KAAK,KAAK,MAAM,MAAM;AAC7C,gBAAU,MAAM,KAAK;AAAA,IACzB;AACA,QAAI,YAAY,MAAM;AACtB,QAAI,WAAW;AACf,QAAI,WAAW,CAAC,GAAG,YAAY,CAAC;AAChC,SAAK,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC5B,UAAI,CAAC,KAAK,MAAM;AACZ,YAAI,CAAC,OAAO;AACR,kBAAQ,KAAK,UAAU,KAAK,IAAI,CAAC;AACjC,oBAAU,MAAM,KAAK;AAAA,QACzB;AACA;AACA,kBAAU,KAAK,IAAI;AACnB;AAAA,MACJ;AACA,UAAI,OAAO;AACP,kBAAU,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC;AACjC,YAAI,OAAO,KAAK,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,GAAGE;AAChD,YAAI,QAAQ,UAAU,UAAU,IAAI,EAAE,KAAK;AACvC,UAAAA,OAAM,UAAU,QAAQ,KAAK,UAAU,QAAQ,KAAK,SAAS,CAAC;AAC9D,mBAAS,KAAK,IAAI,KAAKA,MAAK,QAAW,QAAW,SAAS,SAAS,UAAU,MAAM,CAAC;AAAA,QACzF;AACA;AACA,YAAIA;AACA,gBAAM,UAAUA,MAAK,OAAO;AAAA,MACpC,OACK;AACD,kBAAU,UAAU,KAAK,IAAI;AAAA,MACjC;AACA,UAAI,KAAK,WAAW;AAChB,oBAAY,QAAQ,KAAK,UAAU,IAAI,MAAM,MAAM,OAAO,CAAC,IAAI,KAAK;AACpE,oBAAY,IAAI,QAAO,KAAK,MAAM,MAAM,GAAG,GAAG,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,QAAQ,CAAC,GAAG,KAAK,aAAa,CAAC;AACjH,eAAO;AAAA,MACX;AAAA,IACJ,GAAG,KAAK,MAAM,QAAQ,CAAC;AACvB,WAAO,EAAE,WAAsB,WAAW,UAAqB;AAAA,EACnE;AAAA;AAAA,EAEA,aAAa,WAAW,WAAW,aAAa,eAAe;AAC3D,QAAI,WAAW,CAAC,GAAG,aAAa,KAAK;AACrC,QAAI,WAAW,KAAK,OAAO,WAAW,CAAC,iBAAiB,SAAS,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,IAAI;AAC9G,aAAS,IAAI,GAAG,IAAI,UAAU,MAAM,QAAQ,KAAK;AAC7C,UAAI,OAAO,UAAU,MAAM,CAAC,EAAE,OAAO,UAAU,KAAK,CAAC,CAAC;AACtD,UAAI,OAAO,IAAI,KAAK,UAAU,QAAQ,KAAK,CAAC,GAAG,MAAM,SAAS,GAAG;AACjE,UAAI,SAAS,YAAY,SAAS,MAAM,IAAI,GAAG;AAC3C,eAAO;AACP,YAAI;AACA,mBAAS,IAAI;AAAA;AAEb,qBAAW,SAAS,MAAM,GAAG,SAAS,SAAS,CAAC;AAAA,MACxD;AACA,eAAS,KAAK,IAAI;AAClB,UAAI,WAAW;AACX;AACA,oBAAY;AAAA,MAChB;AACA,UAAI,CAAC;AACD,mBAAW;AAAA,IACnB;AACA,QAAI,WAAW,aAAa,YAAY;AACxC,QAAI,WAAW,gBAAgB;AAC3B,iBAAW,aAAa,UAAU,QAAQ;AAC1C,oBAAc;AAAA,IAClB;AACA,WAAO,IAAI,QAAO,SAAS,OAAO,QAAQ,GAAG,UAAU;AAAA,EAC3D;AAAA,EACA,UAAUC,OAAM,IAAI;AAChB,QAAI,OAAO,IAAI;AACf,SAAK,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC5B,UAAI,YAAY,KAAK,gBAAgB,QAAQ,IAAI,KAAK,gBAAgBA,QAChE,KAAK,KAAK,SAAS,KAAK,eAAe;AAC7C,WAAK,UAAU,KAAK,KAAK,SAAS;AAAA,IACtC,GAAGA,OAAM,EAAE;AACX,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,KAAK,cAAc;AACnB,aAAO;AACX,WAAO,IAAI,QAAO,KAAK,MAAM,OAAO,MAAM,IAAI,CAAAD,SAAO,IAAI,KAAKA,IAAG,CAAC,CAAC,GAAG,KAAK,UAAU;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,kBAAkB,cAAc;AACpC,QAAI,CAAC,KAAK;AACN,aAAO;AACX,QAAI,eAAe,CAAC,GAAG,QAAQ,KAAK,IAAI,GAAG,KAAK,MAAM,SAAS,YAAY;AAC3E,QAAI,UAAU,iBAAiB;AAC/B,QAAI,WAAW,iBAAiB,MAAM;AACtC,QAAI,aAAa,KAAK;AACtB,SAAK,MAAM,QAAQ,UAAQ;AAAE,UAAI,KAAK;AAClC;AAAA,IAAc,GAAG,KAAK;AAC1B,QAAI,WAAW;AACf,SAAK,MAAM,QAAQ,UAAQ;AACvB,UAAI,MAAM,QAAQ,UAAU,EAAE,QAAQ;AACtC,UAAI,OAAO;AACP;AACJ,iBAAW,KAAK,IAAI,UAAU,GAAG;AACjC,UAAIA,OAAM,QAAQ,KAAK,GAAG;AAC1B,UAAI,KAAK,MAAM;AACX,YAAI,OAAO,iBAAiB,MAAM,GAAG,EAAE,OAAO,iBAAiB,KAAK,GAAG,CAAC;AACxE,YAAI,YAAY,KAAK,aAAa,KAAK,UAAU,IAAI,QAAQ,MAAM,WAAW,GAAG,GAAG,CAAC;AACrF,YAAI;AACA;AACJ,qBAAa,KAAK,IAAI,KAAKA,MAAK,MAAM,SAAS,CAAC;AAAA,MACpD,OACK;AACD,qBAAa,KAAK,IAAI,KAAKA,IAAG,CAAC;AAAA,MACnC;AAAA,IACJ,GAAG,KAAK;AACR,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,cAAc,IAAI,UAAU;AACrC,cAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAI,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,OAAO,EAAE,OAAO,YAAY;AAC1E,QAAI,SAAS,IAAI,QAAO,OAAO,UAAU;AACzC,QAAI,OAAO,eAAe,IAAI;AAC1B,eAAS,OAAO,SAAS,KAAK,MAAM,SAAS,aAAa,MAAM;AACpE,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB;AACb,QAAI,QAAQ;AACZ,SAAK,MAAM,QAAQ,UAAQ;AAAE,UAAI,CAAC,KAAK;AACnC;AAAA,IAAS,CAAC;AACd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO,KAAK,MAAM,QAAQ;AAC/B,QAAI,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAG,UAAU,MAAM,KAAK;AAC1D,QAAI,QAAQ,CAAC,GAAG,SAAS;AACzB,SAAK,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC5B,UAAI,KAAK,MAAM;AACX,cAAM,KAAK,IAAI;AACf,YAAI,KAAK;AACL;AAAA,MACR,WACS,KAAK,MAAM;AAChB,YAAI,OAAO,KAAK,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,GAAGA,OAAM,QAAQ,KAAK,OAAO;AAC1E;AACA,YAAIA;AACA,gBAAM,UAAUA,MAAK,OAAO;AAChC,YAAI,MAAM;AACN,cAAI,YAAY,KAAK,aAAa,KAAK,UAAU,IAAI,MAAM,MAAM,OAAO,CAAC;AACzE,cAAI;AACA;AACJ,cAAI,UAAU,IAAI,KAAKA,KAAI,OAAO,GAAG,MAAM,SAAS,GAAG,QAAQ,OAAO,MAAM,SAAS;AACrF,cAAI,SAAS,MAAM,UAAU,MAAM,IAAI,EAAE,MAAM,OAAO;AAClD,kBAAM,IAAI,IAAI;AAAA;AAEd,kBAAM,KAAK,OAAO;AAAA,QAC1B;AAAA,MACJ,WACS,KAAK,KAAK;AACf;AAAA,MACJ;AAAA,IACJ,GAAG,KAAK,MAAM,QAAQ,CAAC;AACvB,WAAO,IAAI,QAAO,aAAa,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAAA,EAChE;AACJ;AACA,OAAO,QAAQ,IAAI,OAAO,aAAa,OAAO,CAAC;AAC/C,SAAS,aAAa,OAAO,GAAG;AAC5B,MAAI;AACJ,QAAM,QAAQ,CAAC,MAAM,MAAM;AACvB,QAAI,KAAK,aAAc,OAAO,GAAI;AAC9B,iBAAW;AACX,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,SAAO,MAAM,MAAM,QAAQ;AAC/B;AACA,IAAM,OAAN,MAAM,MAAK;AAAA,EACP,YAEAA,MAEA,MAIA,WAGA,cAAc;AACV,SAAK,MAAMA;AACX,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,MAAM,OAAO;AACT,QAAI,KAAK,QAAQ,MAAM,QAAQ,CAAC,MAAM,WAAW;AAC7C,UAAI,OAAO,MAAM,KAAK,MAAM,KAAK,IAAI;AACrC,UAAI;AACA,eAAO,IAAI,MAAK,KAAK,OAAO,EAAE,OAAO,GAAG,MAAM,KAAK,SAAS;AAAA,IACpE;AAAA,EACJ;AACJ;AAIA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,MAAM,QAAQ,YAAY,UAAU,iBAAiB;AAC7D,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AAAA,EAC3B;AACJ;AACA,IAAM,iBAAiB;AAEvB,SAAS,iBAAiBE,UAAS,OAAOC,KAAIC,UAAS;AACnD,MAAI,YAAYD,IAAG,QAAQ,UAAU,GAAG;AACxC,MAAI;AACA,WAAO,UAAU;AACrB,MAAIA,IAAG,QAAQ,eAAe;AAC1B,IAAAD,WAAU,IAAI,aAAaA,SAAQ,MAAMA,SAAQ,QAAQ,MAAM,GAAG,EAAE;AACxE,MAAI,WAAWC,IAAG,QAAQ,qBAAqB;AAC/C,MAAIA,IAAG,MAAM,UAAU,GAAG;AACtB,WAAOD;AAAA,EACX,WACS,YAAY,SAAS,QAAQ,UAAU,GAAG;AAC/C,QAAI,SAAS,QAAQ,UAAU,EAAE;AAC7B,aAAO,IAAI,aAAaA,SAAQ,KAAK,aAAaC,KAAI,QAAWC,UAAS,kBAAkB,KAAK,CAAC,GAAGF,SAAQ,QAAQ,UAAUC,IAAG,QAAQ,IAAI,GAAGD,SAAQ,UAAUA,SAAQ,eAAe;AAAA;AAE1L,aAAO,IAAI,aAAaA,SAAQ,MAAMA,SAAQ,OAAO,aAAaC,KAAI,QAAWC,UAAS,kBAAkB,KAAK,CAAC,GAAG,MAAMF,SAAQ,UAAUA,SAAQ,eAAe;AAAA,EAC5K,WACSC,IAAG,QAAQ,cAAc,MAAM,SAAS,EAAE,YAAY,SAAS,QAAQ,cAAc,MAAM,QAAQ;AAExG,QAAI,cAAcA,IAAG,QAAQ,aAAa;AAC1C,QAAI,WAAWD,SAAQ,YAAY,KAC9B,CAAC,YAAYA,SAAQ,mBAAmB,gBACpCA,SAAQ,YAAYC,IAAG,QAAQ,KAAKC,SAAQ,iBAAiB,CAAC,aAAaD,KAAID,SAAQ,UAAU;AAC1G,QAAI,aAAa,WAAW,UAAUA,SAAQ,YAAYC,IAAG,OAAO,IAAI,UAAUA,IAAG,QAAQ,IAAI;AACjG,WAAO,IAAI,aAAaD,SAAQ,KAAK,aAAaC,KAAI,WAAW,MAAM,UAAU,YAAY,IAAI,QAAWC,UAAS,kBAAkB,KAAK,CAAC,GAAG,OAAO,OAAO,YAAYD,IAAG,MAAM,eAAe,OAAOD,SAAQ,kBAAkB,WAAW;AAAA,EAClP,WACS,UAAUC,IAAG,QAAQ,SAAS,GAAG;AAGtC,WAAO,IAAI,aAAaD,SAAQ,KAAK,QAAQC,KAAI,OAAO,GAAGD,SAAQ,OAAO,QAAQC,KAAI,OAAO,GAAG,UAAUD,SAAQ,YAAYC,IAAG,OAAO,GAAGD,SAAQ,UAAUA,SAAQ,eAAe;AAAA,EACxL,OACK;AACD,WAAO,IAAI,aAAaA,SAAQ,KAAK,QAAQC,IAAG,QAAQ,IAAI,GAAGD,SAAQ,OAAO,QAAQC,IAAG,QAAQ,IAAI,GAAG,UAAUD,SAAQ,YAAYC,IAAG,OAAO,GAAGD,SAAQ,UAAUA,SAAQ,eAAe;AAAA,EAChM;AACJ;AACA,SAAS,aAAa,WAAW,YAAY;AACzC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,CAAC,UAAU;AACX,WAAO;AACX,MAAI,WAAW;AACf,YAAU,QAAQ,KAAK,CAAC,EAAE,QAAQ,CAAC,OAAO,QAAQ;AAC9C,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,SAAS,WAAW,IAAI,CAAC,KAAK,OAAO,WAAW,CAAC;AACjD,mBAAW;AAAA,EACvB,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,MAAM;AACrB,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,KAAK,OAAO,UAAU,GAAG;AACxD,SAAK,CAAC,EAAE,QAAQ,CAAC,OAAO,KAAKD,OAAM,OAAO,OAAO,KAAKA,OAAM,EAAE,CAAC;AACnE,SAAO;AACX;AACA,SAAS,UAAU,QAAQ,SAAS;AAChC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvC,QAAIA,QAAO,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;AACxE,QAAIA,SAAQ;AACR,aAAO,KAAKA,OAAM,EAAE;AAAA,EAC5B;AACA,SAAO;AACX;AAGA,SAAS,gBAAgBC,UAAS,OAAOG,OAAM;AAC3C,MAAI,gBAAgB,kBAAkB,KAAK;AAC3C,MAAI,cAAc,WAAW,IAAI,KAAK,EAAE,KAAK;AAC7C,MAAI,OAAOA,QAAOH,SAAQ,SAASA,SAAQ,MAAM,SAAS,OAAO,aAAa;AAC9E,MAAI,CAAC;AACD,WAAO;AACX,MAAI,YAAY,IAAI,UAAU,QAAQ,IAAI,UAAU,GAAG;AACvD,MAAI,SAASG,QAAOH,SAAQ,OAAOA,SAAQ,QAAQ,aAAa,IAAI,WAAW,MAAM,UAAU,YAAY,GAAG,aAAa,aAAa;AACxI,MAAI,UAAU,IAAI,aAAaG,QAAO,QAAQ,IAAI,WAAWA,QAAO,IAAI,YAAY,OAAO,MAAM,GAAG,EAAE;AACtG,SAAO,IAAI,UAAU,aAAa,SAAS,EAAE,QAAQ,YAAY,EAAE,MAAAA,OAAM,cAAc,QAAQ,CAAC;AACpG;AACA,IAAI,sBAAsB;AAA1B,IAAiC,6BAA6B;AAK9D,SAAS,kBAAkB,OAAO;AAC9B,MAAI,UAAU,MAAM;AACpB,MAAI,8BAA8B,SAAS;AACvC,0BAAsB;AACtB,iCAA6B;AAC7B,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,UAAI,QAAQ,CAAC,EAAE,KAAK,sBAAsB;AACtC,8BAAsB;AACtB;AAAA,MACJ;AAAA,EACR;AACA,SAAO;AACX;AASA,IAAM,aAAa,IAAI,UAAU,SAAS;AAC1C,IAAM,kBAAkB,IAAI,UAAU,cAAc;AAUpD,SAAS,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAS;AAAA,IAAE,OAAO,OAAO,SAAS;AAAA,IAC9B,eAAe,OAAO,iBAAiB;AAAA,EAAI;AAC/C,SAAO,IAAI,OAAO;AAAA,IACd,KAAK;AAAA,IACL,OAAO;AAAA,MACH,OAAO;AACH,eAAO,IAAI,aAAa,OAAO,OAAO,OAAO,OAAO,MAAM,GAAG,EAAE;AAAA,MACnE;AAAA,MACA,MAAMC,KAAI,MAAM,OAAO;AACnB,eAAO,iBAAiB,MAAM,OAAOA,KAAI,MAAM;AAAA,MACnD;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACH,iBAAiB;AAAA,QACb,YAAY,MAAM,GAAG;AACjB,cAAI,YAAY,EAAE;AAClB,cAAI,UAAU,aAAa,gBAAgB,OAAO,aAAa,gBAAgB,OAAO;AACtF,cAAI,CAAC;AACD,mBAAO;AACX,YAAE,eAAe;AACjB,iBAAO,QAAQ,KAAK,OAAO,KAAK,QAAQ;AAAA,QAC5C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,aAAaC,OAAM,QAAQ;AAChC,SAAO,CAAC,OAAO,aAAa;AACxB,QAAI,OAAO,WAAW,SAAS,KAAK;AACpC,QAAI,CAAC,SAASA,QAAO,KAAK,SAAS,KAAK,MAAM,cAAc;AACxD,aAAO;AACX,QAAI,UAAU;AACV,UAAID,MAAK,gBAAgB,MAAM,OAAOC,KAAI;AAC1C,UAAID;AACA,iBAAS,SAASA,IAAG,eAAe,IAAIA,GAAE;AAAA,IAClD;AACA,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,OAAO,aAAa,OAAO,IAAI;AAIrC,IAAM,OAAO,aAAa,MAAM,IAAI;AAKpC,IAAM,eAAe,aAAa,OAAO,KAAK;AAK9C,IAAM,eAAe,aAAa,MAAM,KAAK;;;AClXtC,IAAM,iBAAiB,UAAU,OAAqD;EAC3F,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO;MACP,MAAM;MACN,aAAa,CAAA,SAAQ,KAAK;MAC1B,aAAa,CAAA,SAAQ,KAAK,MAAM,GAAG,EAAE,OAAO,CAAA,SAAQ,SAAS,EAAE,EAAE;IACnE;EACF;EAEA,aAAa;AACX,WAAO;MACL,YAAY,MAAM;MAClB,OAAO,MAAM;IACf;EACF;EAEA,iBAAiB;AACf,SAAK,QAAQ,aAAa,CAAAE,aAAW;AACnC,YAAM,QAAOA,YAAA,OAAA,SAAAA,SAAS,SAAQ,KAAK,OAAO,MAAM;AAChD,YAAM,QAAOA,YAAA,OAAA,SAAAA,SAAS,SAAQ,KAAK,QAAQ;AAE3C,UAAI,SAAS,YAAY;AACvB,cAAM,OAAO,KAAK,YAAY,GAAG,KAAK,QAAQ,MAAM,QAAW,GAAG;AAElE,eAAO,KAAK,QAAQ,YAAY,IAAI;MACtC;AAEA,aAAO,KAAK;IACd;AAEA,SAAK,QAAQ,QAAQ,CAAAA,aAAW;AAC9B,YAAM,QAAOA,YAAA,OAAA,SAAAA,SAAS,SAAQ,KAAK,OAAO,MAAM;AAChD,YAAM,OAAO,KAAK,YAAY,GAAG,KAAK,QAAQ,MAAM,KAAK,GAAG;AAE5D,aAAO,KAAK,QAAQ,YAAY,IAAI;IACtC;EACF;EAEA,wBAAwB;AACtB,QAAI,wBAAwB;AAE5B,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,gBAAgB;QACnC,mBAAmB,CAAC,cAAc,UAAU,aAAa;AACvD,cAAI,uBAAuB;AACzB;UACF;AAEA,gBAAM,QAAQ,KAAK,QAAQ;AAE3B,cAAI,UAAU,QAAQ,UAAU,UAAa,UAAU,GAAG;AACxD,oCAAwB;AACxB;UACF;AAEA,gBAAM,qBAAqB,KAAK,QAAQ,WAAW,EAAE,MAAM,SAAS,IAAI,CAAC;AAEzE,cAAI,qBAAqB,OAAO;AAC9B,kBAAM,OAAO,qBAAqB;AAClC,kBAAMC,QAAO;AACb,kBAAM,KAAK;AAEX,oBAAQ;cACN,sDAAsD,KAAK;YAC7D;AACA,kBAAMC,MAAK,SAAS,GAAG,YAAYD,OAAM,EAAE;AAE3C,oCAAwB;AACxB,mBAAOC;UACT;AAEA,kCAAwB;QAC1B;QACA,mBAAmB,CAAC,aAAa,UAAU;AACzC,gBAAM,QAAQ,KAAK,QAAQ;AAG3B,cAAI,CAAC,YAAY,cAAc,UAAU,KAAK,UAAU,QAAQ,UAAU,QAAW;AACnF,mBAAO;UACT;AAEA,gBAAM,UAAU,KAAK,QAAQ,WAAW,EAAE,MAAM,MAAM,IAAI,CAAC;AAC3D,gBAAM,UAAU,KAAK,QAAQ,WAAW,EAAE,MAAM,YAAY,IAAI,CAAC;AAGjE,cAAI,WAAW,OAAO;AACpB,mBAAO;UACT;AAGA,cAAI,UAAU,SAAS,UAAU,SAAS,WAAW,SAAS;AAC5D,mBAAO;UACT;AAGA,cAAI,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS;AAC3D,mBAAO;UACT;AAEA,gBAAM,UAAU,YAAY,QAAQ,OAAO;AAG3C,cAAI,CAAC,SAAS;AACZ,mBAAO;UACT;AAGA,gBAAM,MAAM,YAAY,UAAU,MAAM;AACxC,gBAAM,OAAO,UAAU;AACvB,gBAAMD,QAAO,MAAM;AACnB,gBAAM,KAAK;AAIX,sBAAY,YAAYA,OAAM,EAAE;AAMhC,gBAAM,cAAc,KAAK,QAAQ,WAAW,EAAE,MAAM,YAAY,IAAI,CAAC;AAErE,cAAI,cAAc,OAAO;AACvB,mBAAO;UACT;AAEA,iBAAO;QACT;MACF,CAAC;IACH;EACF;AACF,CAAC;AClKM,IAAM,aAAaE,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO;MACP,OAAO;MACP,OAAO;IACT;EACF;EAEA,wBAAwB;AACtB,WAAO,CAAC,WAAW,KAAK,OAAO,CAAC;EAClC;AACF,CAAC;ACjBM,IAAM,QAAQA,UAAU,OAAqB;EAClD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,WAAW;MACX,MAAM;IACR;EACF;EAEA,wBAAwB;AACtB,WAAO;MACL,IAAIC,OAAO;QACT,KAAK,IAAIC,UAAU,OAAO;QAC1B,OAAO;UACL,aAAa,CAAC,EAAE,KAAK,UAAU,MAAM;AACnC,kBAAM,EAAE,YAAY,UAAU,IAAI,KAAK;AACvC,kBAAM,EAAE,OAAO,IAAI;AACnB,kBAAM,cAA4B,CAAC;AAEnC,gBAAI,CAAC,cAAc,CAAC,WAAW;AAC7B,qBAAO,cAAc,OAAO,KAAK,CAAC,CAAC;YACrC;AAGA,gBAAI,YAAY;AAEhB,gBAAI,KAAK,QAAQ,SAAS,WAAW;AACnC,kBAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,oBAAI,KAAK,QAAQ;AACf;gBACF;AAEA,sBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK,WAAW;AAEnE,oBAAI,CAAC,WAAW;AACd,yBAAO;gBACT;AAEA,6BAAa;cACf,CAAC;YACH;AAGA,gBAAI,eAAe;AAEnB,gBAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,kBAAI,KAAK,QAAQ;AACf,uBAAO;cACT;AAEA,oBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK,WAAW;AAEnE,kBAAI,CAAC,WAAW;AACd,uBAAO;cACT;AAEA,8BAAgB;AAEhB,oBAAM,aACH,KAAK,QAAQ,SAAS,aAAa,YAAY,eAAe,KAC9D,KAAK,QAAQ,SAAS,gBAAgB,eAAe;AAExD,kBAAI,YAAY;AACd,uBAAO,KAAK,QAAQ,SAAS;cAC/B;AAEA,0BAAY;gBACV,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU;kBACxC,OAAO,KAAK,QAAQ;gBACtB,CAAC;cACH;YACF,CAAC;AAED,mBAAO,cAAc,OAAO,KAAK,WAAW;UAC9C;QACF;MACF,CAAC;IACH;EACF;AACF,CAAC;ACjFM,IAAM,YAAYF,UAAU,OAAO;EACxC,MAAM;EAEN,wBAAwB;AACtB,WAAO,CAAC,UAAU,CAAC;EACrB;EAEA,iBAAiB,WAAW;AAnC9B,QAAA;AAoCI,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;IACrB;AAEA,WAAO;MACL,iBAAgB,KAAA,aAAa,kBAAkB,WAAW,kBAAkB,OAAO,CAAC,MAApE,OAAA,KAAyE;IAC3F;EACF;AACF,CAAC;ACgBM,IAAM,cAAcA,UAAU,OAA2B;EAC9D,MAAM;EAEN,aAAa;AACX,WAAO;MACL,kBAAkB;MAClB,gBAAgB;MAChB,aAAa;MACb,sBAAsB;MACtB,iBAAiB;MACjB,iBAAiB;IACnB;EACF;EAEA,wBAAwB;AACtB,WAAO;MACL,IAAIC,OAAO;QACT,KAAK,IAAIC,UAAU,aAAa;QAChC,OAAO;UACL,aAAa,CAAC,EAAE,KAAK,UAAU,MAAM;AACnC,kBAAM,SAAS,KAAK,OAAO,cAAc,CAAC,KAAK,QAAQ;AACvD,kBAAM,EAAE,OAAO,IAAI;AACnB,kBAAM,cAA4B,CAAC;AAEnC,gBAAI,CAAC,QAAQ;AACX,qBAAO;YACT;AAEA,kBAAM,aAAa,KAAK,OAAO;AAE/B,gBAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,oBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK;AACxD,oBAAM,UAAU,CAAC,KAAK,UAAU,YAAY,IAAI;AAEhD,mBAAK,aAAa,CAAC,KAAK,QAAQ,oBAAoB,SAAS;AAC3D,sBAAM,UAAU,CAAC,KAAK,QAAQ,cAAc;AAE5C,oBAAI,YAAY;AACd,0BAAQ,KAAK,KAAK,QAAQ,gBAAgB;gBAC5C;AAEA,sBAAM,aAAaC,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU;kBAC3D,OAAO,QAAQ,KAAK,GAAG;kBACvB,oBACE,OAAO,KAAK,QAAQ,gBAAgB,aAChC,KAAK,QAAQ,YAAY;oBACvB,QAAQ,KAAK;oBACb;oBACA;oBACA;kBACF,CAAC,IACD,KAAK,QAAQ;gBACrB,CAAC;AAED,4BAAY,KAAK,UAAU;cAC7B;AAEA,qBAAO,KAAK,QAAQ;YACtB,CAAC;AAED,mBAAOC,cAAc,OAAO,KAAK,WAAW;UAC9C;QACF;MACF,CAAC;IACH;EACF;AACF,CAAC;AC/GM,IAAMC,aAAYL,UAAU,OAAO;EACxC,MAAM;EAEN,aAAa;AACX,WAAO;MACL,WAAW;IACb;EACF;EAEA,wBAAwB;AACtB,UAAM,EAAE,QAAQ,SAAAH,SAAQ,IAAI;AAE5B,WAAO;MACL,IAAII,OAAO;QACT,KAAK,IAAIC,UAAU,WAAW;QAC9B,OAAO;UACL,YAAY,OAAO;AACjB,gBACE,MAAM,UAAU,SAChB,OAAO,aACP,CAAC,OAAO,cACR,gBAAgB,MAAM,SAAS,KAC/B,OAAO,KAAK,UACZ;AACA,qBAAO;YACT;AAEA,mBAAOE,cAAc,OAAO,MAAM,KAAK;cACrCD,WAAW,OAAO,MAAM,UAAU,MAAM,MAAM,UAAU,IAAI;gBAC1D,OAAON,SAAQ;cACjB,CAAC;YACH,CAAC;UACH;QACF;MACF,CAAC;IACH;EACF;AACF,CAAC;AClDD,SAAS,eAAe,EAAE,OAAO,KAAK,GAAoE;AACxG,SAAQ,QAAQ,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,QAAA,OAAA,SAAA,KAAM,UAAS;AACvF;AA2BO,IAAM,eAAeG,UAAU,OAA4B;EAChE,MAAM;EAEN,aAAa;AACX,WAAO;MACL,MAAM;MACN,UAAU,CAAC;IACb;EACF;EAEA,wBAAwB;AACtB,UAAM,SAAS,IAAIE,UAAU,KAAK,IAAI;AACtC,UAAM,gBAAgB,OAAO,QAAQ,KAAK,OAAO,OAAO,KAAK,EAC1D,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,EACxB,OAAO,CAAA,UAAS,KAAK,QAAQ,YAAY,CAAC,GAAG,OAAO,KAAK,QAAQ,IAAI,EAAE,SAAS,KAAK,IAAI,CAAC;AAE7F,WAAO;MACL,IAAID,OAAO;QACT,KAAK;QACL,mBAAmB,CAAC,GAAG,IAAI,UAAU;AACnC,gBAAM,EAAE,KAAK,IAAAF,KAAI,OAAO,IAAI;AAC5B,gBAAM,wBAAwB,OAAO,SAAS,KAAK;AACnD,gBAAM,cAAc,IAAI,QAAQ;AAChC,gBAAM,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI;AAE3C,cAAI,CAAC,uBAAuB;AAC1B;UACF;AAEA,iBAAOA,IAAG,OAAO,aAAa,KAAK,OAAO,CAAC;QAC7C;QACA,OAAO;UACL,MAAM,CAAC,GAAG,UAAU;AAClB,kBAAM,WAAW,MAAM,GAAG,IAAI;AAE9B,mBAAO,CAAC,eAAe,EAAE,MAAM,UAAU,OAAO,cAAc,CAAC;UACjE;UACA,OAAO,CAACA,KAAI,UAAU;AACpB,gBAAI,CAACA,IAAG,YAAY;AAClB,qBAAO;YACT;AAEA,kBAAM,WAAWA,IAAG,IAAI;AAExB,mBAAO,CAAC,eAAe,EAAE,MAAM,UAAU,OAAO,cAAc,CAAC;UACjE;QACF;MACF,CAAC;IACH;EACF;AACF,CAAC;ACtCM,IAAM,WAAWC,UAAU,OAAwB;EACxD,MAAM;EAEN,aAAa;AACX,WAAO;MACL,OAAO;MACP,eAAe;IACjB;EACF;EAEA,cAAc;AACZ,WAAO;MACL,MACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,eAAO,KAAK,OAAO,QAAQ;MAC7B;MACF,MACE,MACA,CAAC,EAAE,OAAO,SAAS,MAAM;AACvB,eAAO,KAAK,OAAO,QAAQ;MAC7B;IACJ;EACF;EAEA,wBAAwB;AACtB,WAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;EAC/B;EAEA,uBAAuB;AACrB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,KAAK;MACzC,eAAe,MAAM,KAAK,OAAO,SAAS,KAAK;MAC/C,SAAS,MAAM,KAAK,OAAO,SAAS,KAAK;;MAGzC,SAAS,MAAM,KAAK,OAAO,SAAS,KAAK;MACzC,eAAe,MAAM,KAAK,OAAO,SAAS,KAAK;IACjD;EACF;AACF,CAAC;;;ACsFM,IAAM,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,gBAAgB;AA9KlB,QAAA,IAAA,IAAA,IAAA;AA+KI,UAAM,aAAa,CAAC;AAEpB,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;IACnD;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;IACnD;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;IAC7D;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;IAC/D;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;IAC7D;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;IAC7D;AAEA,QAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,iBAAW,KAAK,QAAQ,UAAU,KAAK,QAAQ,OAAO,CAAC;IACzD;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,QAAI,KAAK,QAAQ,mBAAmB,OAAO;AACzC,iBAAW,KAAK,eAAe,UAAU,KAAK,QAAQ,cAAc,CAAC;IACvE;AAEA,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,iBAAW,KAAK,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC;IACvD;AAEA,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;IAC3D;AAEA,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,WAAU,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,UAAU,CAAC;IAChE;AAEA,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,WAAU,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,IAAI,CAAC;IACpD;AAEA,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,iBAAW,KAAK,YAAY,UAAU,KAAK,QAAQ,WAAW,CAAC;IACjE;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;IAC7D;AAEA,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,iBAAW,KAAK,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC;IACvD;AAEA,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAKM,MAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;IACnD;AAEA,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,WAAU,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,SAAS,CAAC;IAC9D;AAEA,QAAI,KAAK,QAAQ,iBAAiB,OAAO;AACvC,iBAAW,KAAK,aAAa,WAAU,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,YAAY,CAAC;IACpE;AAEA,WAAO;EACT;AACF,CAAC;ACtQD,IAAO,gBAAQ;", "names": ["inputRegex", "tr", "tr", "tr", "starInputRegex", "starPasteRegex", "underscoreInputRegex", "underscorePasteRegex", "nextState", "regexp", "Nl", "scheme", "options", "scheme", "options", "options", "tr", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "find", "Node", "mergeAttributes", "getNodeType", "isNodeActive", "ListItemName", "TextStyleName", "wrappingInputRule", "inputRegex", "tr", "Extension", "inputRegex", "pasteRegex", "Text", "options", "rect", "tr", "RopeSequence", "from", "Leaf", "Append", "appendInner", "map", "from", "history", "tr", "options", "redo", "tr", "redo", "options", "from", "tr", "Extension", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Decoration", "DecorationSet", "Selection", "Text"]}