/* eslint-disable */
/* prettier-ignore */
// biome-ignore format: off
// biome-ignore lint: off
// @ts-nocheck
// Generated by unplugin-svg-component
import 'vue'
declare module 'vue' {
  export interface GlobalComponents {
    SvgIcon: import("vue").DefineComponent<{
        name: {
            type: import("vue").PropType<"dashboard" | "fullscreen-exit" | "fullscreen" | "huanzhe" | "keyboard-down" | "keyboard-enter" | "keyboard-esc" | "keyboard-up" | "search" | "md_icons/两端对齐" | "md_icons/保存" | "md_icons/删除线" | "md_icons/右对齐" | "md_icons/字体斜体" | "md_icons/导入" | "md_icons/导出" | "md_icons/居中对齐" | "md_icons/左对齐" | "md_icons/引用" | "md_icons/撤销" | "md_icons/无序列表" | "md_icons/有序列表" | "md_icons/源代码 下游代码" | "md_icons/粗体" | "md_icons/表格" | "md_icons/返回" | "md_icons/重做">;
            default: string;
            required: true;
        };
    }, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        name: {
            type: import("vue").PropType<"dashboard" | "fullscreen-exit" | "fullscreen" | "huanzhe" | "keyboard-down" | "keyboard-enter" | "keyboard-esc" | "keyboard-up" | "search" | "md_icons/两端对齐" | "md_icons/保存" | "md_icons/删除线" | "md_icons/右对齐" | "md_icons/字体斜体" | "md_icons/导入" | "md_icons/导出" | "md_icons/居中对齐" | "md_icons/左对齐" | "md_icons/引用" | "md_icons/撤销" | "md_icons/无序列表" | "md_icons/有序列表" | "md_icons/源代码 下游代码" | "md_icons/粗体" | "md_icons/表格" | "md_icons/返回" | "md_icons/重做">;
            default: string;
            required: true;
        };
    }>>, {
        name: "dashboard" | "fullscreen-exit" | "fullscreen" | "huanzhe" | "keyboard-down" | "keyboard-enter" | "keyboard-esc" | "keyboard-up" | "search" | "md_icons/两端对齐" | "md_icons/保存" | "md_icons/删除线" | "md_icons/右对齐" | "md_icons/字体斜体" | "md_icons/导入" | "md_icons/导出" | "md_icons/居中对齐" | "md_icons/左对齐" | "md_icons/引用" | "md_icons/撤销" | "md_icons/无序列表" | "md_icons/有序列表" | "md_icons/源代码 下游代码" | "md_icons/粗体" | "md_icons/表格" | "md_icons/返回" | "md_icons/重做";
    }>;
  }
}
