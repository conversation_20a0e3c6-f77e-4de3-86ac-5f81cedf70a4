# 富文本编辑器拖拽功能实现指南

## 概述

本文档详细介绍了在 TipTap 富文本编辑器中实现文字块和表格自由移动功能的三种方案。

## 可行性分析

✅ **完全可行** - 项目已具备实现条件：
- TipTap 富文本编辑器（基于 ProseMirror）
- @vueuse/core（包含 useDraggable）
- Vue 3 + TypeScript 环境

## 实现方案

### 方案一：TipTap 官方 Drag Handle 扩展（推荐）

#### 优点
- 官方支持，稳定性好
- 与 TipTap 深度集成
- 支持复杂的拖拽逻辑
- 提供丰富的配置选项

#### 安装步骤

1. **安装依赖**
```bash
npm install @tiptap/extension-drag-handle
```

2. **使用组件**
```vue
<template>
  <DragHandleEditor 
    v-model="content" 
    :height="600"
    placeholder="开始编辑..."
  />
</template>

<script setup>
import DragHandleEditor from './components/DragHandleEditor.vue'
import { ref } from 'vue'

const content = ref('')
</script>
```

3. **配置选项**
```javascript
DragHandle.configure({
  render: () => {
    const element = document.createElement('div')
    element.classList.add('custom-drag-handle')
    element.innerHTML = '⋮⋮'
    return element
  },
  onNodeChange: ({ node, editor, pos }) => {
    // 节点变化回调
  },
  tippyOptions: {
    placement: 'left',
    offset: [0, 0],
  }
})
```

#### 特性
- 🎯 精确的拖拽控制
- 🔒 支持锁定/解锁拖拽手柄
- 🎨 自定义拖拽手柄样式
- 📍 智能定位和提示

---

### 方案二：第三方 GlobalDragHandle 扩展

#### 优点
- 全局拖拽支持
- 自动检测可拖拽元素
- 配置简单
- 社区活跃

#### 安装步骤

1. **安装依赖**
```bash
npm install tiptap-extension-global-drag-handle
npm install tiptap-extension-auto-joiner  # 推荐一起安装
```

2. **使用组件**
```vue
<template>
  <GlobalDragEditor 
    v-model="content" 
    :height="600"
    placeholder="开始编辑..."
  />
</template>

<script setup>
import GlobalDragEditor from './components/GlobalDragEditor.vue'
import { ref } from 'vue'

const content = ref('')
</script>
```

3. **配置选项**
```javascript
GlobalDragHandle.configure({
  dragHandleWidth: 20,
  scrollTreshold: 100,
  dragHandleSelector: ".custom-global-drag-handle",
  excludedTags: [], // 排除的标签
  customNodes: [], // 自定义节点
})
```

#### 特性
- 🌐 全局拖拽检测
- 🚫 可排除特定元素
- 📜 自动滚动支持
- 🎨 无头样式设计

---

### 方案三：VueUse useDraggable

#### 优点
- 基于 VueUse 生态
- 高度可定制
- 支持文件拖拽
- 轻量级实现

#### 安装步骤

1. **无需额外安装**（项目已包含 @vueuse/core）

2. **使用组件**
```vue
<template>
  <VueUseDragEditor 
    v-model="content" 
    :height="600"
    placeholder="开始编辑..."
  />
</template>

<script setup>
import VueUseDragEditor from './components/VueUseDragEditor.vue'
import { ref } from 'vue'

const content = ref('')
</script>
```

#### 特性
- 🎛️ 完全自定义控制
- 📁 支持文件拖拽上传
- 🎯 精确的拖拽反馈
- 🔄 实时状态更新

---

## 功能对比

| 功能特性 | 官方 Drag Handle | GlobalDragHandle | VueUse 方案 |
|---------|-----------------|------------------|-------------|
| 安装复杂度 | 中等 | 简单 | 无需安装 |
| 稳定性 | 高 | 中等 | 中等 |
| 自定义程度 | 高 | 中等 | 极高 |
| 文件拖拽 | ❌ | ❌ | ✅ |
| 自动检测 | ❌ | ✅ | 手动实现 |
| 官方支持 | ✅ | ❌ | ✅ |
| 学习成本 | 中等 | 低 | 高 |

## 推荐使用场景

### 选择官方 Drag Handle 扩展，如果：
- 需要稳定可靠的拖拽功能
- 希望与 TipTap 深度集成
- 对拖拽精度要求高
- 团队技术水平中等

### 选择 GlobalDragHandle 扩展，如果：
- 希望快速实现拖拽功能
- 需要全局自动检测
- 对自定义要求不高
- 追求开发效率

### 选择 VueUse 方案，如果：
- 需要完全自定义控制
- 要支持文件拖拽上传
- 团队对 Vue 生态熟悉
- 有特殊的拖拽需求

## 实现步骤

### 1. 选择方案并安装依赖

根据需求选择合适的方案，按照上述安装步骤进行。

### 2. 替换现有编辑器组件

将现有的 `TemplateEditor.vue` 替换为对应的拖拽编辑器组件：

```vue
<!-- 在 editor.vue 中 -->
<template>
  <div class="template-editor-page">
    <!-- 其他代码保持不变 -->
    
    <!-- 替换编辑器组件 -->
    <DragHandleEditor
      ref="editorRef"
      v-model="templateContent"
      :height="editorHeight"
      :editable="true"
      placeholder="请开始编辑您的病例报告模板..."
      @save="handleSave"
      @change="handleContentChange"
    />
  </div>
</template>

<script setup>
// 导入对应的编辑器组件
import DragHandleEditor from "./components/DragHandleEditor.vue"
// 其他代码保持不变
</script>
```

### 3. 自定义样式

根据项目设计要求，自定义拖拽手柄和拖拽效果的样式。

### 4. 测试功能

- 测试各种元素的拖拽功能
- 验证拖拽的准确性
- 检查用户体验

## 注意事项

1. **性能考虑**：大量元素时可能影响性能，建议使用虚拟滚动或分页
2. **浏览器兼容性**：确保目标浏览器支持拖拽 API
3. **移动端适配**：考虑触摸设备的拖拽体验
4. **数据同步**：确保拖拽后数据正确更新
5. **撤销重做**：拖拽操作应支持撤销重做功能

## 扩展功能

### 可以进一步实现的功能：
- 🎨 拖拽预览效果
- 📏 拖拽辅助线
- 🔄 批量拖拽
- 📱 移动端手势支持
- 🎯 智能吸附对齐
- 📋 拖拽复制功能

## 总结

三种方案各有优势，建议：
1. **生产环境**：使用官方 Drag Handle 扩展
2. **快速原型**：使用 GlobalDragHandle 扩展  
3. **特殊需求**：使用 VueUse 自定义方案

选择合适的方案可以大大提升富文本编辑器的用户体验，让用户能够自由地组织和调整文档结构。
